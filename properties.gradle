// because project level properties.gradle not available from buildSrc
// so we define those some common versions in single gradle file
// https://docs.gradle.org/current/userguide/build_environment.html#sec:gradle_configuration_properties
ext {
    // middleware-bom release notes https://cfl.corp.100.me/display/JGB/Middleware-bom
    // see what's included in middleware-bom,
    // https://nexus.ddxq.mobi/repository/maven-releases/com/ddmc/middleware-bom/1.2.8-RELEASE/middleware-bom-1.2.8-RELEASE.pom
    middlewareBomVersion = '1.3.2-RELEASE'

    // not really used yet
    thirdPartyBomVersion = '1.2.1-RELEASE'

    middlewarePluginVersion = '1.0.2-RELEASE'

    springBootVersion = '2.3.12.RELEASE'
    springCloudVersion = 'Hoxton.SR12'
    junitVersion = '5.11.3'

    nexusUrl = 'https://nexus.ddxq.mobi/repository/maven-public/'
    nexusReleaseUrl = 'https://nexus.ddxq.mobi/repository/maven-releases/'
    nexusSnapshotUrl = 'https://nexus.ddxq.mobi/repository/maven-snapshots/'
    nexusUsername = 'deployment'
    nexusPassword = 'deployment123'
}