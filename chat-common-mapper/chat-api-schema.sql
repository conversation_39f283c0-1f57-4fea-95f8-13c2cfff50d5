USE chat-api;


DROP TABLE IF EXISTS `message_box`;
DROP TABLE IF EXISTS `message_feedback`;
DROP TABLE IF EXISTS `message_box`;


COMMIT;

-- 预定义字典配置
-- stage - tracker - id

-- 场景基本信息表
CREATE TABLE `message_box`
(
    `id`               bigint(20) unsigned         NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `owner`            varchar(50)                 NOT NULL COMMENT '消息箱所属人，实际是用户uid',
    `message_id`       bigint(20) unsigned         NOT NULL COMMENT '消息ID，必须要按照时间单调增',
    `quest_id`         varchar(100)                NOT NULL COMMENT 'v请求ID，向AI进行一次咨询，除了文本内容，还需要附带上新生成请求ID；同时算法的回答，也会被标记上同一个请求ID。',
    `session_id`       varchar(100)                NOT NULL COMMENT '会话ID，全局唯一',
    `station_id`       varchar(100)                NOT NULL COMMENT '站点ID',
    `city_code`        varchar(10)                 NOT NULL COMMENT '城市code',
    `correlate_pid`    varchar(100)   DEFAULT ''   NOT NULL COMMENT '消息关联的商品ID',
    `sender`           varchar(20)                 NOT NULL COMMENT '消息发送人角色，user/assistant/system',
    `type`             tinyint        DEFAULT 1    NOT NULL COMMENT '消息类型 1:普通文本消息，url和卡片消息的type都是1',
    `content`          varchar(5000)               NOT NULL COMMENT '消息内容',
    `commit_status`    tinyint        DEFAULT 0    NOT NULL COMMENT '提交状态，0：未提交不能被读取, 1：已经提交，可以被正常读取, 2：未知状态，需要进一步确认',
    `audio_file_key`   varchar(200)   DEFAULT          NULL COMMENT '音频文件上传后对应的fileKey',
    `extra_content`    text                                 COMMENT '扩展消息内容',
    `is_delete`        tinyint        DEFAULT 0    NOT NULL COMMENT '是否已删除，0:正常；1:已删除',
    `create_time`      timestamp      DEFAULT CURRENT_TIMESTAMP         NOT NULL COMMENT '创建时间',
    `update_time`      timestamp                   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uq_idx_message_id` (`message_id`),
    KEY `idx_owner_message_id` (`owner`,`message_id`),
    KEY `idx_owner_create_time_commit_status_message_id` (`owner`,`create_time`,`commit_status`,`message_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_update_time` (`update_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='消息表';


-- 任务基本信息表
CREATE TABLE `message_feedback`
(
    `id`               bigint(20) unsigned          NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `message_id`       bigint(20) unsigned          NOT NULL COMMENT '消息ID，必须要按照时间单调增',
    `quest_id`         bigint(20) unsigned          NOT NULL COMMENT 'v请求ID，向AI进行一次咨询，除了文本内容，还需要附带上新生成请求ID；同时算法的回答，也会被标记上同一个请求ID。'，
    `feedback`         tinyint                      NOT NULL COMMENT '1：赞、2：踩',
    `reason`           varchar(5000)    DEFAULT ''  NOT NULL COMMENT '原因文字，多个原因的话，用”逗号“拼接',
    `is_delete`        tinyint      DEFAULT 0       NOT NULL COMMENT '是否已删除，0:正常；1:已删除',
    `create_time`      timestamp                    NOT NULL COMMENT '创建时间',
    `update_time`      timestamp                    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_message_id` (`message_id`),
    KEY `idx_quest_id` (`quest_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_update_time` (`update_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='消息反馈表';



-- 用户设置表
CREATE TABLE `user_settings`
(
    `id`               bigint(20) unsigned         NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `owner`            varchar(50)                 NOT NULL COMMENT '实际是用户uid',
    `audio_auto_play`  tinyint        DEFAULT 0    NOT NULL COMMENT '是否开启自动播放，1:开启；0:关闭',
    `is_delete`        tinyint        DEFAULT 0    NOT NULL COMMENT '是否已删除，0:正常；1:已删除',
    `create_time`      timestamp      DEFAULT CURRENT_TIMESTAMP         NOT NULL COMMENT '创建时间',
    `update_time`      timestamp                   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_owner` (`owner`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_update_time` (`update_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户设置表';

COMMIT;