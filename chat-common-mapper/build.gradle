plugins {
    id 'common-conventions'
}


dependencies {
    implementation project(':chat-common')

    api('com.baomidou:mybatis-plus-boot-starter:3.5.1') {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-jdbc'
        exclude group: 'org.springframework.boot', module: 'spring-boot-autoconfigure'
    }
    api 'com.github.yulichang:mybatis-plus-join-boot-starter:1.4.6'
}