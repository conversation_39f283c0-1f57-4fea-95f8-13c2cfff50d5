<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.chat.common.mapper.TopicMessageMapper">
    
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.chat.common.mapper.DO.TopicMessageDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="topic_id" property="topicId"/>
        <result column="message_id" property="messageId"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, topic_id, message_id, is_delete, create_time, update_time
    </sql>
</mapper> 