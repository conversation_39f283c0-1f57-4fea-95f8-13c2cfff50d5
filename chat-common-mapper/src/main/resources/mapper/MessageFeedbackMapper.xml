<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.chat.common.mapper.MessageFeedbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.chat.common.mapper.DO.MessageFeedbackDO">
        <id column="id" property="id" />
        <result column="message_id" property="messageId" />
        <result column="quest_id" property="questId" />
        <result column="feedback" property="feedback" />
        <result column="reason" property="reason" />
        <result column="is_delete" property="isDelete" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, message_id, quest_id, feedback, reason, is_delete, create_time, update_time
    </sql>

</mapper>
