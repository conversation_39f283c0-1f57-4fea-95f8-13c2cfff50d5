<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.chat.common.mapper.MessageBoxMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.chat.common.mapper.DO.MessageBoxDO">
        <id column="id" property="id" />
        <result column="owner" property="owner" />
        <result column="message_id" property="messageId" />
        <result column="quest_id" property="questId" />
        <result column="session_id" property="sessionId" />
        <result column="station_id" property="stationId" />
        <result column="city_code" property="cityCode" />
        <result column="correlate_pid" property="correlatePid" />
        <result column="sender" property="sender" />
        <result column="type" property="type" />
        <result column="content" property="content" />
        <result column="extra_content" property="extraContent" />
        <result column="commit_status" property="commitStatus" />
        <result column="audio_file_key" property="audioFileKey" />
        <result column="is_delete" property="isDelete" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, owner, message_id, quest_id, session_id, station_id, city_code, correlate_pid, sender, type, content, extra_content, commit_status, audio_file_key, is_delete, create_time, update_time
    </sql>

    <select id="selectByMessageIdAndOwner" resultType="com.ddmc.chat.common.mapper.DO.MessageBoxDO">
        select
        <include refid="Base_Column_List" />
        from message_box
        where owner = #{owner} and message_id = #{messageId}
        and is_delete = 0
        limit 1
    </select>

</mapper>
