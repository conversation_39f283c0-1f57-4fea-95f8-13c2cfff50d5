package com.ddmc.chat.common.mapper.DO;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 消息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("message_box")
public class MessageBoxDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * PK
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息箱所属人，实际是用户uid
     */
    private String owner;

    /**
     * 消息ID，必须要按照时间单调增
     */
    private Long messageId;

    /**
     * v请求ID，向AI进行一次咨询，除了文本内容，还需要附带上新生成请求ID；同时算法的回答，也会被标记上同一个请求ID。
     */
    private String questId;

    /**
     * 会话ID，全局唯一
     */
    private String sessionId;

    /**
     * 站点ID
     */
    private String stationId;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 消息关联的商品ID
     */
    private String correlatePid;

    /**
     * 消息发送人角色，user/assistant/system
     */
    private String sender;

    /**
     * 消息类型：1:text；2:商卡消息
     */
    private Integer type;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 扩充的消息内容，与type配合使用，比如type=2时，extra_content是相关商品卡片列表的JSON格式
     */
    private String extraContent;

    /**
     * 提交状态，0：未提交不能被读取； 1：已经提交，可以被正常读取； 2：未知状态，需要进一步确认；
     */
    private Integer commitStatus;

    /**
     * 音频文件上传后对应的fileKey
     */
    private String audioFileKey;

    /**
     * 是否已删除，0:正常；1:已删除; 2:丢弃
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
