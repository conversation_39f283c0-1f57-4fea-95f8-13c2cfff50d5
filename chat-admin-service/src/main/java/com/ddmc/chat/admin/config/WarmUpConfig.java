package com.ddmc.chat.admin.config;

import com.ddmc.antispam.dto.TextCheckDTO;
import com.ddmc.chat.common.domain.dto.req.chat.StartReq;
import com.ddmc.chat.common.infra.proxy.ABTestProxy;
import com.ddmc.chat.common.infra.proxy.AntispamProxy;
import com.ddmc.chat.common.infra.proxy.ChatContext;
import com.ddmc.chat.common.infra.proxy.NlpAiProxy;
import com.ddmc.chat.common.infra.proxy.SummaryProxy;
import com.ddmc.llm.biz.agent.client.ProductAssistSseClient;
import com.ddmc.llm.biz.agent.entity.ProductAssistRequestVo;
import com.ddmc.llm.biz.agent.entity.ProductAssistResultCallback;
import com.ddmc.soa.warmup.AbstractWarmUpComponent;
import com.google.common.collect.Lists;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class WarmUpConfig extends AbstractWarmUpComponent {


    @Autowired
    private ProductAssistSseClient sseClient;

    @Autowired
    private NlpAiProxy nlpAiProxy;

    @Autowired
    private AntispamProxy antispamProxy;

    @Autowired
    private SummaryProxy summaryProxy;

    @Autowired
    private ABTestProxy abTestProxy;


    @Override
    public void warmUp() throws Exception {

        doWarmUp("summaryProxy", "getProductList", () -> {
            ChatContext uc = ChatContext.of("5d3083bd81eef7c644a3682d", "5d3083bd81eef7c644a3682d",
                    "5b50430dc0a1eac91e8b4bc2", "1001", "11.20", "11.20", "11.20",
                    1, "111", "111", "detail");
            summaryProxy.getProductInfoCardByProductIds(uc, "category", Lists.newArrayList("65712ebfd5760b0025d3ed67"));
        }, 5);

        doWarmUp("abProxy", "ab2", () -> {
            ChatContext uc = ChatContext.of("5d3083bd81eef7c644a3682d", "5d3083bd81eef7c644a3682d",
                    "5b50430dc0a1eac91e8b4bc2", "1001", "11.20", "11.20", "11.20",
                    1, "111", "111", "detail");
            abTestProxy.abTest(uc, "aaa");
        }, 5);

        doWarmUp("antispamProxy", "textCheck", () -> {
            TextCheckDTO checkDTO = new TextCheckDTO();
            checkDTO.setAppId("product_assistant");
            checkDTO.setServerType(0);
            checkDTO.setDataId(System.currentTimeMillis()+"");
            checkDTO.setSceneCode("product_assistant");
            checkDTO.setContent("预热");
            checkDTO.setUser("5d3083bd81eef7c644a3682d");
            antispamProxy.textCheck(checkDTO);
        }, 5);


        doWarmUp("nlpAiProxy", "getFirstChatStartMessage", () -> {
            ChatContext uc = ChatContext.of("5d3083bd81eef7c644a3682d", "5d3083bd81eef7c644a3682d",
                    "5b50430dc0a1eac91e8b4bc2", "1001", "11.20", "11.20", "11.20",
                    1, "111", "111", "detail");
            nlpAiProxy.getFirstChatStartMessage(uc, new StartReq());
        }, 5);

        doWarmUp("sseClient", "stopStream", () -> {
            ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
            productAssistRequestVo.setMessageId("预热");
            sseClient.stopStream(productAssistRequestVo);
        }, 5);


        doWarmUp("sseClient", "streamCall", () -> {
            ProductAssistRequestVo requestVo = new ProductAssistRequestVo();
            sseClient.streamCall(requestVo, new ProductAssistResultCallback() {
                @Override
                public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {

                }

                @Override
                public void onEvent(@NotNull EventSource eventSource, String id, String type, @NotNull String data) {

                }

                @Override
                public void onClosed(@NotNull EventSource eventSource) {

                }

                @Override
                public void onFailure(@NotNull EventSource eventSource, Throwable t, Response response) {

                }
            });
        }, 5);
    }

}

