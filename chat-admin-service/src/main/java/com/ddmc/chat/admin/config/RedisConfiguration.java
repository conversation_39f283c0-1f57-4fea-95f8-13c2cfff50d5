//package com.ddmc.chat.app.config;
//
//import com.ddmc.guide.enhance.redis.TfRedisClient;
//import com.fasterxml.jackson.annotation.JsonAutoDetect;
//import com.fasterxml.jackson.annotation.JsonInclude;
//import com.fasterxml.jackson.annotation.PropertyAccessor;
//import com.fasterxml.jackson.databind.DeserializationFeature;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.fasterxml.jackson.databind.SerializationFeature;
//import csoss.daas.redis.core.aop.RedisAspect;
//import io.lettuce.core.ClientOptions;
//import io.lettuce.core.SocketOptions;
//import io.lettuce.core.cluster.ClusterClientOptions;
//import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
//import io.lettuce.core.resource.ClientResources;
//import io.lettuce.core.resource.DefaultClientResources;
//import java.time.Duration;
//import java.util.Optional;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.core.env.Environment;
//import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
//import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
//import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
//import org.springframework.data.redis.serializer.RedisSerializer;
//
//
///**
// * 临时方案，后期会将文件抽取到公共项目中
// *
// * <AUTHOR> */
//@Configuration
//public class RedisConfiguration {
//
//    @Autowired
//    private Environment environment;
//
//    @Bean(destroyMethod = "shutdown")
//    ClientResources clientResources() {
//        return DefaultClientResources.create();
//    }
//
//    @Bean
//    public RedisStandaloneConfiguration redisStandaloneConfiguration(RedisProperties redisProperties) {
//        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
//        config.setHostName(Optional.ofNullable(redisProperties.getHost()).orElse("localhost"));
//        config.setPassword(Optional.ofNullable(redisProperties.getPassword()).orElse(""));
//        config.setPort(redisProperties.getPort());
//        config.setDatabase(redisProperties.getDatabase());
//
//        return config;
//    }
//
//    @Bean
//    public ClientOptions clientOptions(RedisProperties redisProperties) {
//        // 设置连接超时时间，默认 1s，apollo若有配置则优先
//        SocketOptions socketOptions = SocketOptions.builder()
//            .connectTimeout(Optional.ofNullable(redisProperties.getTimeout()).orElse(Duration.ofSeconds(1)))
//            .keepAlive(true)
//            .build();
//
//        // 自动刷新，因借用RedisProperties配置，没有自动刷新的属性，
//        // 这里设置自动刷新间隔为固定时间 30s
//        ClusterTopologyRefreshOptions topologyRefreshOptions = ClusterTopologyRefreshOptions.builder()
//            .enablePeriodicRefresh(Duration.ofSeconds(30L))
//            .enableAllAdaptiveRefreshTriggers()
//            .build();
//
//        // 应用自动刷新，开启自动重连
//        return ClusterClientOptions.builder()
//            .topologyRefreshOptions(topologyRefreshOptions)
//            .socketOptions(socketOptions)
//            .autoReconnect(true)
//            .validateClusterNodeMembership(false)
//            .build();
//    }
//
//    @Bean
//    public LettuceClientConfiguration lettuceClientConfig(ClientOptions clientOptions, RedisProperties redisProperties) {
//        String appName = environment.getProperty("spring.application.name", "UNKNOWN_APP_NAME");
//
//        return LettuceClientConfiguration.builder()
//            .clientName(appName)
//            // 设置统一的默认命令执行超时时间 1s，apollo若有配置则优先
//            .commandTimeout(Optional.ofNullable(redisProperties.getTimeout()).orElse(Duration.ofSeconds(1)))
//            .clientOptions(clientOptions)
//            .build();
//    }
//
//    @Bean
//    public LettuceConnectionFactory redisConnectionFactory(RedisStandaloneConfiguration redisStandaloneConfiguration,
//        LettuceClientConfiguration lettuceClientConfig) {
//        return new LettuceConnectionFactory(redisStandaloneConfiguration, lettuceClientConfig);
//    }
//
//
//    @Bean
//    @ConditionalOnMissingBean(name = "redisTemplate")
//    @Primary
//    public RedisTemplate<String, Object> redisTemplate(LettuceConnectionFactory redisConnectionFactory) {
//        RedisTemplate<String, Object> template = new RedisTemplate<>();
//        template.setConnectionFactory(redisConnectionFactory);
//
//        // key采用String的序列化方式
//        template.setKeySerializer(RedisSerializer.string());
//        // hash的key也采用String的序列化方式
//        template.setHashKeySerializer(RedisSerializer.string());
//
//        // GenericJackson2JsonRedisSerializer 方式对泛型支持有坑，see https://cloud.tencent.com/developer/article/1497568
//        // 纯Jackson的方式又必须提供要序列化对象的类型信息(.class对象)
//        // 这里先按照没有具体类型的方式提供
//        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);
//        ObjectMapper objectMapper = new ObjectMapper();
//        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
//        objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
//        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
//        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
//        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);
//
//        // value序列化方式采用jackson
//        template.setValueSerializer(jackson2JsonRedisSerializer);
//        // hash的value序列化方式采用jackson
//        template.setHashValueSerializer(jackson2JsonRedisSerializer);
//
//        template.afterPropertiesSet();
//        return template;
//    }
//
//    @Bean
//    public TfRedisClient tfRedisClient(RedisTemplate<String, Object> redisTemplate,
//        LettuceConnectionFactory redisConnectionFactory) {
//        TfRedisClient tfRedisClient = new TfRedisClient(redisTemplate, redisConnectionFactory);
//        return tfRedisClient;
//    }
//
//    /**
//     * 启用Redis命令切面，https://cfl.corp.100.me/pages/viewpage.action?pageId=33813021
//     */
//    @Bean
//    public RedisAspect openTracingRedisAspect() {
//        return new RedisAspect();
//    }
//
//}
