package com.ddmc.chat.admin.controller;

import com.csoss.monitor.api.common.AttributeKey;
import com.csoss.monitor.api.common.Attributes;
import com.csoss.monitor.api.metrics.Counter;
import com.csoss.monitor.api.metrics.Metrics;
import com.ddmc.guide.legacy.ResponseBaseVo;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.info.Info;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@Slf4j
@OpenAPIDefinition(info = @Info(title = "chat-admin Debug API", version = "0.1", description = "内部调试方法"))
@RestController
@RequestMapping(value = "/debug")
public class DebugController {

//    @Autowired
//    private DemoService demoService;

    @Operation(summary = "服务是否在线判断")
    @GetMapping(value = "/ping", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseBaseVo ping() {
        Counter counter = Metrics.newCounter("monitor_demo_test_count")
            .build();
        counter.once(Attributes.of(AttributeKey.stringKey("ping"), UUID.randomUUID().toString()));

        return ResponseBaseVo.ok();
    }

//    @Operation(summary = "demo")
//    @GetMapping(value = "/demo", produces = MediaType.APPLICATION_JSON_VALUE)
//    public SimpleResponseVo<DemoVO> demo() {
//
//        return SimpleResponseVo.ok(demoService.demo());
//    }
//


}
