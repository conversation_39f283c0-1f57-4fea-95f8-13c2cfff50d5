package com.ddmc.chat.admin;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.ddmc"})
@Slf4j
public class ChatAdmin {

    public static void main(String[] args) {
        try {
            SpringApplication.run(ChatAdmin.class, args);
        } catch (Exception e) {
            log.error("service start error", e);
        }
    }
}