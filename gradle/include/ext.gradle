//ext {
//    // nexus address
//    nexusUrl = 'https://nexus.ddxq.mobi/repository/maven-public/'
//    nexusReleaseUrl = 'https://nexus.ddxq.mobi/repository/maven-releases/'
//    nexusSnapshotUrl = 'https://nexus.ddxq.mobi/repository/maven-snapshots/'
//
//    nexusUsername = 'deployment'
//    nexusPassword = 'deployment123'
//
//    // https://stackoverflow.com/questions/42659920/is-there-a-compatibility-matrix-of-spring-boot-and-spring-cloud
//    // max supported compatibility version with springboot 2.3.x
//    springBootVersion = '2.3.12.RELEASE'
//    springCloudVersion = 'Hoxton.SR12'
//
//    sourceCompatibility = JavaVersion.VERSION_11
//    targetCompatibility = JavaVersion.VERSION_11
//
//    // Those 3rd dependencies are pre-defined by spring-boot-dependencies.pom
//    // see https://docs.spring.io/spring-boot/docs/current-SNAPSHOT/reference/htmlsingle/#dependency-versions.properties
//    // also https://docs.gradle.org/current/userguide/resolution_rules.html#resolution_rules
//    // also https://nexocode.com/blog/posts/spring-dependencies-in-gradle/
//    ext['byte-buddy.version'] = '1.12.12'
//    ext['logback.version'] = '1.2.10'
//    ext['slf4j.version'] = '1.7.36'
//    ext['log4j2.version'] = '2.17.1'
//    ext['lombok.version'] = '1.18.24'
//    ext['netty.version'] = '4.1.73.Final'
//    //ext['mysql.version'] = '8.0.29' 与ddmc-jdbc-pool:1.3.4-RELEASE不兼容，只能使用5.x版本驱动
//    //ext['mysql.version'] = '5.1.49'
//    ext['groovy.version'] = '3.0.10'
//
//    // other 2nd & 3rd dependency version
//    ddmcDeps = [
//            bba       : 'net.bytebuddy:byte-buddy-agent:1.12.12',
//            log       : 'com.ddmc:ddmc-log:1.2.2-RELEASE',
//            logExt    : 'com.ddmc:ddmc-log-ext:1.7.6-RELEASE',
//            feignExt  : 'com.ddmc:ddmc-feign-ext:1.7.5-RELEASE',
//            jdbcPool  : 'com.ddmc:ddmc-jdbc-pool:1.4.0-RELEASE',
//            jdbcDriver8 : 'com.ddmc:ddmc-jdbc-driver8:2.0.0-SNAPSHOT',
//            redis     : 'csoss-redis:spring-data-redis-core:1.0.4-RELEASE',
//            // 推荐只引入soa包，另外两个csoss和monitor都可通过soa被引入
//            soa       : 'com.ddmc.soa:spring-cloud-ddmc:1.5.10-RELEASE',
//            // gradle 7.4兼容性问题，手动引入sentinel，且版本和soa内部依赖版本保持一致
//            sentinel  : 'com.alibaba.csp:sentinel-core:1.8.0',
//            // below are already included in soa jar, but the version is not always up to date
//            // new monitor
////            monitor     : 'com.csoss:monitor-agent:1.1.2-RELEASE',
////            jdbcDriver: 'com.ddmc:ddmc-jdbc-driver:1.0.3-RELEASE',
//    ]
//
//    thirdDeps = [
//            apolloClient        : 'com.ctrip.framework.apollo:apollo-client:1.9.2',
//            apolloCore          : 'com.ctrip.framework.apollo:apollo-core:1.9.2',
//            // Use Open API instead swagger for Spring v3.0
////            swagger             : 'io.springfox:springfox-swagger2:3.0.0',
////            swaggerUI           : 'io.springfox:springfox-swagger-ui:3.0.0',
//            openApi             : 'org.springdoc:springdoc-openapi-ui:1.6.15',
//
//            slf4jApi            : 'org.slf4j:slf4j-api:1.7.36',
//            logback             : 'ch.qos.logback:logback-classic:1.2.11',
//            logbackKafkaAppender: 'com.github.danielwegener:logback-kafka-appender:0.2.0-RC2',
//
//            caffeine            : 'com.github.ben-manes.caffeine:caffeine:2.9.3',
//            resilience4j        : 'io.github.resilience4j:resilience4j-all:1.7.1',
//            cache2kApi          : 'org.cache2k:cache2k-api:2.6.1.Final',
//            cache2kCore         : 'org.cache2k:cache2k-core:2.6.1.Final',
//            // xxhash, see https://github.com/OpenHFT/Zero-Allocation-Hashing
//            zeroHashing         : 'net.openhft:zero-allocation-hashing:0.16',
//
//            guava               : 'com.google.guava:guava:31.1-jre',
//            httpclient          : 'org.apache.httpcomponents:httpclient:4.5.13',
//            fastjson2           : 'com.alibaba.fastjson2:fastjson2:2.0.31',
//            commonio            : 'commons-io:commons-io:2.11.0',
//            commonLangs         : 'org.apache.commons:commons-lang3:3.12.0',
//            commonCollections   : 'org.apache.commons:commons-collections4:4.4',
//            commonValidators    : 'commons-validator:commons-validator:1.7',
//            commonCodes         : 'commons-codec:commons-codec:1.15',
//
//            lettuce             : 'io.lettuce:lettuce-core:6.1.10.RELEASE',
//            hikaricp            : 'com.zaxxer:HikariCP:5.0.1',
//
//            spock               : 'org.spockframework:spock-core:2.1-groovy-3.0',
//            spockSpring         : 'org.spockframework:spock-spring:2.1-groovy-3.0',
//            junit5              : 'org.junit.jupiter:junit-jupiter:5.8.2'
//    ]
//
//}