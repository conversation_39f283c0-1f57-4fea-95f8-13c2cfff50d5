package com.ddmc.chat.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeleteChatAggregateVO {

    @Schema(description = "会话信息")
    private ChatTopicVO chatTopic;

    @Schema(description = "是否删除了全部消息")
    private Boolean deleteAll;

    @Schema(description = "删除了全部消息的 sessionId - 前端删除消息使用")
    private List<String> deleteAllSessionIdList;

}
