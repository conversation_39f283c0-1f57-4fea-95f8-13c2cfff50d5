package com.ddmc.chat.common.domain.dto.req.chat;

import com.ddmc.chat.common.domain.dto.req.CommonReq;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 开启对话的，第一个请求体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StartReq extends CommonReq {

    // 商详页场景下，商品id是必填的
    // 想要咨询的商品，当来源场景不仅限于商品详情页时，这个字段可以为空
    @Schema(description = "商品id")
    private String productId;

    // 分类页场景下，分类页用户当前停留的前端叶子类目 名称和ID，是必填的
    // 当来源场景不仅限于分类页时，这个字段可以为空
    @Schema(description = "叶子分类名称")
    @JsonProperty("front_category_name")
    private String frontCategoryName;

    @Schema(description = "叶子分类ID")
    @JsonProperty("front_category_id")
    private String frontCategoryId;

    // 搜索场景下，用户当前搜索的关键词，是必填的
    // 当来源场景不仅限于搜索页时，这个字段可以为空
    @Schema(description = "搜索关键词")
    @JsonProperty("search_keyword")
    private String searchKeyword;

    @Schema(description = "搜索结果数量")
    @JsonProperty("search_result_cnt")
    private Integer searchResultCnt;

    @Schema(description = "扩展参数")
    @JsonProperty("extra_param")
    private Map<String, String> extraParamMap;

}
