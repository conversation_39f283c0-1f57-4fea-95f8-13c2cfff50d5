package com.ddmc.chat.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/4 11:22 AM
 * @Description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FeedbackVO {

    @Schema(description = "二次反馈标签")
    private List<String> tags;

    @Schema(description = "toast内容")
    private String toast;

}
