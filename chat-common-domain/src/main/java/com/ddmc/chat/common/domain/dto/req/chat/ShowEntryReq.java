package com.ddmc.chat.common.domain.dto.req.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShowEntryReq {

    @Schema(description = "用户id")
    private String uid;

    @Schema(description = "站点id")
    private String stationId;

    @NotBlank(message = "场景不能为空")
    @Schema(description = "场景: search_float-搜索浮层页")
    private List<String> sceneList;

}
