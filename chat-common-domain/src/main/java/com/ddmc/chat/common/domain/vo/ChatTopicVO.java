package com.ddmc.chat.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChatTopicVO {

    @Schema(description = "话题 id")
    private String topicId;

    @Schema(description = "session id")
    private String sessionId;
}
