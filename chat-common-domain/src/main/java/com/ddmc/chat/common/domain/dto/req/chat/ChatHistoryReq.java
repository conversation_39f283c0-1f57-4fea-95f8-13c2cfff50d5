package com.ddmc.chat.common.domain.dto.req.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/9/4 11:16 AM
 * @Description: 会话开始请求体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChatHistoryReq {

    @Schema(description = "用户id")
    @NotBlank(message = "uid 不能为空")
    private String uid;

    /**
     *拉取到的最后一条消息id，第一次拉取时为0
     */
    @Schema(description = "拉取到的最后一条消息id，第一次拉取时为0或者不传")
    private Long lastMessageId;

    /**
     *每次拉取的消息数
     */
    @Schema(description = "每次拉取的消息数")
    private Integer limit;

}
