package com.ddmc.chat.common.domain.dto.req.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/9/4 11:16 AM
 * @Description: 会话开始请求体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
public class HistoryReq extends EntryReq {

    @Schema(description = "sessionId")
    private String uid;

    @Schema(description = "分页拉取的最后一条消息id，第一次拉取可不传")
    private Long lastMessageId;

}
