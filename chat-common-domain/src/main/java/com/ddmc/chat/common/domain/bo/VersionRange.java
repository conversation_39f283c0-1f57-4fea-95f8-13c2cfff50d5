package com.ddmc.chat.common.domain.bo;

import com.ddmc.chat.common.util.VersionUtils;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 版本区间配置类
 * @param <T> 配置的值类型
 */
@Data
public class VersionRange<T> {
    // 最小版本（包含）
    private String minVersion;
    // 最大版本（包含）
    private String maxVersion;
    // 该版本区间对应的值
    private T value;

    /**
     * 判断指定版本是否在当前版本区间内
     */
    public boolean isInRange(String version) {
        // 检查下限
        if (StringUtils.isNotBlank(minVersion) && VersionUtils.isLessThan(version, minVersion)) {
            return false;
        }
        // 检查上限
        if (StringUtils.isNotBlank(maxVersion) && VersionUtils.isGreaterThan(version, maxVersion)) {
            return false;
        }
        return true;
    }

    /**
     * 根据版本号获取对应的配置值
     */
    public T getConfig(String version, List<VersionRange<T>> configurations) {
        for (VersionRange<T> range : configurations) {
            if (range.isInRange(version)) {
                return range.getValue();
            }
        }
        return null;
    }
}
