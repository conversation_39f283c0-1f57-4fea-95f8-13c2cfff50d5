package com.ddmc.chat.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/9/4 11:22 AM
 * @Description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MessageBoxVO {

    @Schema(description = "uid")
    private String uid;

    @Schema(description = "消息列表")
    private List<MessageVO> messageList;

    @Schema(description = "是否有下一页")
    private Boolean hasMore;

    @Schema(description = "拉取到的最后一条消息id，第一次拉取时为0或者不传")
    private Long lastMessageId;


}
