package com.ddmc.chat.common.domain.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @<PERSON> <PERSON>i<PERSON>
 * @Date 2024/9/4 11:22 AM
 * @Description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MessageContext {

    @Schema(description = "messageId")
    private Long messageId;

    @Schema(description = "sessionId")
    private String sessionId;

    @Schema(description = "questId")
    private String questId;

    @Schema(description = "uid")
    private String uid;

    @Schema(description = "topicId")
    private String topicId;

}
