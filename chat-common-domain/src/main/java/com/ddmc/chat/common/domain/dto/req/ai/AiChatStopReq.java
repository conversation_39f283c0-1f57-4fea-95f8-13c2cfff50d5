package com.ddmc.chat.common.domain.dto.req.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiChatStopReq {

    @Schema(description = "用户id")
    private String uid;

    @Schema(description = "qid")
    private String qid;

    @Schema(description = "message id")
    @JsonProperty("message_id")
    private Long messageId;

    @Schema(description = "1:user（表示用户主动停止）  2: timeout  3：杀进程等异常")
    private Integer type;

    @Schema(description = "透传算法参数，是个 json 结构的字段")
    @JsonProperty("ai_param")
    private String aiParam;

}
