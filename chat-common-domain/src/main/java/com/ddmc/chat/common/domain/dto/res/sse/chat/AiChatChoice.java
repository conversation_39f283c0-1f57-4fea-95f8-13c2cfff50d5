package com.ddmc.chat.common.domain.dto.res.sse.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class AiChatChoice {

    /**
     * null：生成未结束；
     *
     * stop：生成遇到停止词而结束（正常结束）；
     *
     * length：达到设置的最大生成长度而结束。
     */
    @JsonProperty("finish_reason")
    private String finishReason;

    private Integer index;

    // 增量更新内容
    private AiChatDelta delta;

}
