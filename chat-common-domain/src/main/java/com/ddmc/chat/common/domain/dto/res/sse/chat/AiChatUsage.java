package com.ddmc.chat.common.domain.dto.res.sse.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/9/6 2:41 PM
 * @Description: 计量信息，表示本次请求所消耗的token数据。
 */
@Data
public class AiChatUsage {

    // 用户输入文本转换成token后的长度。
    @JsonProperty("prompt_tokens")
    private Integer promptTokens;

    // 模型生成回复转换为token后的长度。
    @JsonProperty("completion_tokens")
    private Integer completionTokens;

    // usage.prompt_tokens与usage.completion_tokens的总和。
    @JsonProperty("total_tokens")
    private Integer totalTokens;

}
