package com.ddmc.chat.common.domain.entity;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiChatOpMdTemplate {
    // 动作
    private String action;
    // 要转换的 markdown 模板
    private String mdTemplate;
    // 不同端的 url
    private Map<String, String> appClientUrlMap;

}
