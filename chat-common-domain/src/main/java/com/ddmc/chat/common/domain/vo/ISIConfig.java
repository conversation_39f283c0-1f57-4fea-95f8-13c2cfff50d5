package com.ddmc.chat.common.domain.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * aliyun 智能语音交互 相关的配置项目，配置的默认值参考 {@link com.ddmc.chat.common.infra.config.GlobalApolloConfig}
 * 参考文档起点 https://help.aliyun.com/zh/isi/
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ISIConfig {

    // 项目Appkey
    private String appKey;

    // 为服务端分配的Token，在Token过期失效前，可以一直使用，也支持在不同机器、进程或应用上同时使用该Token。
    private String token;

    // 为此令牌的有效期时间戳，单位：秒。例如，1527592757换算为北京时间为2018/5/29 19:19:17，即Token在该时间之前有效，过期需要重新获取。
    private Long expireTime;

    // ASR的url地址
    private String asrStreamUrl;

    // TTS的url地址
    private String ttsStreamUrl;

    // 语音的音色
    private String tone;

}
