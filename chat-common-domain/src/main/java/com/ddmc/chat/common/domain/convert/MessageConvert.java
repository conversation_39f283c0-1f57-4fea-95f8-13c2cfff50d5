package com.ddmc.chat.common.domain.convert;

import com.ddmc.chat.common.domain.vo.MessageVO;
import com.ddmc.chat.common.mapper.DO.MessageBoxDO;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @Date 2024/9/3 2:25 PM
 * @Description:
 */
@Mapper(componentModel = "spring",unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,imports = {
    LocalDateTime.class,
    Date.class, ZoneId.class})
public interface MessageConvert {


    List<MessageVO> messageDO2VO(List<MessageBoxDO> messageBoxDOS);

    @Mappings(value = {
        @Mapping(source = "sender", target = "role"),
        @Mapping(source = "createTime", target = "time", qualifiedByName = "localDateTime2Str")
    })
    MessageVO messageDO2VO(MessageBoxDO messageBoxDO);

    @Mappings(value = {
        @Mapping(source = "role", target = "sender"),
    })
    MessageBoxDO messageVO2DO(MessageVO messageVO);

    @Named("localDateTime2Str")
    default String localDateTime2Str(LocalDateTime localDateTime) {
        if(localDateTime == null){
            return null;
        }
        return localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

}
