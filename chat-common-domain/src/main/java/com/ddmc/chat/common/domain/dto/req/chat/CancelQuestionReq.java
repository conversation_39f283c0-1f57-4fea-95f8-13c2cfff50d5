package com.ddmc.chat.common.domain.dto.req.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @<PERSON> <PERSON>i<PERSON>
 * @Date 2024/9/4 11:16 AM
 * @Description:停止回答问题
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CancelQuestionReq{

    @Schema(description = "用户id")
    private String uid;

    @Schema(description = "sessionId")
    private Long sessionId;

    @NotNull(message = "messageId 不能为空")
    @Schema(description = "messageId")
    private Long messageId;

    @Schema(description = "requestId")
    private Long requestId;


    @Schema(description = "1:user（表示用户主动停止）  2: timeout  3：杀进程等异常")
    private Integer type;

}
