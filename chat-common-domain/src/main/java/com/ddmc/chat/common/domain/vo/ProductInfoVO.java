package com.ddmc.chat.common.domain.vo;

import com.ddmc.summary.client.dto.TagFormBO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;


/**
 * Where is it from?
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductInfoVO {

    /**
     * 商品ID
     */
    @JsonProperty("id")
    private String id;

    /**
     * 原始价
     */
    @JsonProperty("origin_price")
    private String originPrice;

    /**
     * 现价
     */
    private String price;

    /**
     * 绿卡价
     */
    @JsonProperty("vip_price")
    private String vipPrice;

    /**
     * 库存数量
     */
    @JsonProperty("stock_number")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer stockNumber;


    /**
     * 营销标签
     */
    @JsonProperty("marketing_tags")
    private List<MarketingTag> marketingTags;

    /**
     * 是否是抢购商品 0:否 1:是
     */
    @JsonProperty("is_promotion")
    private Integer isPromotion;

    /**
     * 限购数量
     * 兼容老服务，限购字段可为null。null标识没有对应的限购活动
     */
    @Nullable // 兼容老服务，限购字段可为null。null标识没有对应的限购活动
    @JsonProperty("buy_limit")
    private Integer buyLimit;


    @JsonProperty("is_booking_new")
    private Integer isBookingNew;

    @JsonProperty("today_stockout_new")
    private String todayStockoutNew;

    /**
     * 扩展字段
     */
    private Map<String, String> extMap;


    /**
     * 单位价格
     */
    @JsonProperty("unit_price_hint")
    private String unitPriceHint;



    @JsonProperty("product_name")
    private String productName;

    private String name;

    @JsonProperty("short_title")
    private String shortTitle;

    private String spec;

    @JsonProperty("small_image")
    private String smallImage;

    @JsonProperty("image_list")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> smallImageList;

    @JsonProperty("mini_image_list")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> miniImageList;

    // 1:miniDetail
    @JsonProperty("card_type")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer cardType;


    @JsonProperty("category_id")
    private String categoryId;

    private List<ProductDetailSizeVO> sizes;

    @JsonProperty("total_sales")
    private Integer totalSales;

    @JsonProperty("month_sales")
    private Integer monthSales;

    @JsonProperty("station_stock")
    private Integer stationStock;

    @JsonProperty("mark_discount")
    private Integer markDiscount;

    @JsonProperty("mark_new")
    private Integer markNew;

    @JsonProperty("mark_self")
    private Integer markSelf;

    private Integer status;

    @JsonProperty("category_path")
    private String categoryPath;

    private Integer type;

    @JsonProperty("stockout_reserved")
    private Boolean stockOutReserved;

    @JsonProperty("sale_point_msg")
    private List<List<String>> salePointMsg;


    @JsonProperty("is_presale")
    private Integer isPresale;

    @JsonProperty("presale_delivery_date_display")
    private String presaleDeliveryDateDisplay;

    @JsonProperty("is_gift")
    private Integer isGift;

    @JsonProperty("is_bulk")
    private Integer isBulk;

    @JsonProperty("net_weight")
    private String netWeight;

    @JsonProperty("net_weight_unit")
    private String netWeightUnit;

    @JsonProperty("is_onion")
    private Integer isOnion;

    @JsonProperty("is_invoice")
    private Integer isInvoice;

    @JsonProperty("badge_img")
    private String badgeImg;

    @JsonProperty("badge_position")
    private Integer badgePosition;

    @JsonProperty("is_vod")
    private Boolean isVod;

    private Integer oid;

    @JsonProperty("decision_information")
    private List<String> decisionInformation;

    @JsonProperty("user_bought")
    private Integer userBought;

    @JsonProperty("sub_list")
    private List<ProductInfoSubListVO> subList;

    @JsonProperty("image_preferential_choice")
    private String imagePreferentialChoice;

    @JsonProperty("similar_product_images")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String[] similarProductImages;

    @JsonProperty("today_stockout")
    private String todayStockout;

    @JsonProperty("is_booking")
    private Integer isBooking;


    @JsonProperty("min_order_quantity")
    private Integer minOrderQuantity;

    @JsonProperty("sale_unit")
    private String saleUnit;

    @JsonProperty("temperature_layer")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String temperatureLayer;

    @JsonProperty("storage_value_id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer storageValueId;


    @JsonProperty("algo_id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String algoId;

    @JsonProperty("recommend_cate")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer recommendCate;

    @JsonProperty("recommend_reason")
    private String recommendReason;

    @JsonProperty("share_text")
    private String shareText;

    @JsonProperty("share_background_image")
    private String shareBackgroundImage;

    @JsonProperty("scene_id")
    private Integer sceneId;

    @JsonProperty("scene_tag")
    private String sceneTag;

    @JsonProperty("recommended_reason")
    private List<String> recommendedReason;


    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer features;

    /**
     * 在列表或搜索页是否展示
     */
    @JsonProperty("is_aggregation_show")
    private Integer isAggregationShow;

    /**
     * 水果卖点信息
     */
    @JsonProperty("selling_point")
    private String sellingPoint;


    @JsonProperty("is_stockout_notify")
    private Integer isStockOutNotify;


    /**
     * 活动埋点id（商品所有活动id，逗号分割）
     */
    @JsonProperty("tracker_activity_ids")
    private String trackerActivityIds;

    @JsonProperty("feature")
    private String feature;

    @JsonProperty("scm")
    private String scm;

    @JsonProperty("ad_channel")
    private Integer adChannel;
    /**
     * 来源 0 外投 1 算法
     */
    @JsonProperty("source_channel")
    private String sourceChannel;

    /**
     * 外投所见即所得原始投放品
     */
    @JsonProperty("original_product_id")
    private String originalProductId;

    /**
     * 缺货可加购
     */
    @JsonProperty("stockout_reserved_new")
    private boolean stockOutReservedNew = true;

    /**
     * 活动作用域
     **/
    private List<String> lowPriceActivityUserScopeLimits;

    /**
     * 标准分类（后台分类|运营分类|总共5级,返回叶子级）
     */
    @JsonProperty("backend_category_id")
    private Integer backendCategoryId;

    @JsonProperty("backend_category_path")
    private String backendCategoryPath;


    /**
     * 商品上投放的标签
     */
    @JsonProperty("tag_info")
    private Map<String, Map<String, TagFormBO.TagPositionBO>> tagInfo;


    @Data
    public static class ProductDetailSizeVO {



        @JsonProperty("title")
        private String title;

        @JsonProperty("title_name")
        private String titleName;

        @JsonProperty("recommended_reason")
        private String recommendedReason;

        @JsonProperty("id")
        private Integer id;

        @JsonProperty("_id")
        private String mongoId;

        /**
         * 处理方式商品增加缺重提示
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String prompt;


        private List<ProductDetailSizeValueVO> values;
    }


    @Data
    public static class ProductDetailSizeValueVO {

        @JsonProperty("name")
        private String name;

        @JsonProperty("id")
        private Integer id;

        @JsonProperty("_id")
        private String mongoId;


        @JsonProperty("price")
        private String price;


        @JsonProperty("is_irreversible")
        private Integer isIrreversible;

        @JsonProperty("sort")
        private Integer sort;

        @JsonProperty("remark")
        private String remark;
    }


    @Data
    public static class ProductInfoSubListVO {

        private String id;

        private Integer count;

        @JsonProperty("product_name")
        private String productName;

        private String name;

        @JsonProperty("origin_price")
        private String originPrice;
        /**
         * 子品平均价
         */
        @JsonProperty("average_price")
        private String averagePrice;

        private String price;

        @JsonProperty("small_image")
        private String smallImage;

        @JsonProperty("stock_number")
        private Integer stockNumber;

        private Integer status;

        @JsonProperty("is_invoice")
        private Integer isInvoice;

        @JsonProperty("category_path")
        private String categoryPath;

        @JsonProperty("manage_category_path")
        private String manageCategoryPath;

        private List<ProductDetailSize> sizes;

        @JsonProperty("sale_unit")
        private String saleUnit;
    }


    /**
     * 商品处理方式详情
     *
     * <AUTHOR>
     */
    @Data
    public static class ProductDetailSize {

        private static final long serialVersionUID = 7125066580296559404L;

        @JsonProperty("title")
        private String title;

        @JsonProperty("title_name")
        private String titleName;

        @JsonProperty("recommended_reason")
        private String recommendedReason;

        @JsonProperty("id")
        private Integer id;

        @JsonProperty("_id")
        private String mongoId;

        /**
         * 处理方式商品增加缺重提示
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String prompt;

        private List<ProductDetailSizeValue> values;
    }

    @Data
    public static class ProductDetailSizeValue  {


        @JsonProperty("name")
        private String name;

        @JsonProperty("id")
        private Integer id;

        @JsonProperty("_id")
        private String mongoId;


        @JsonProperty("price")
        private BigDecimal price;


        @JsonProperty("is_irreversible")
        private Integer isIrreversible;

        @JsonProperty("sort")
        private Integer sort;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketingTag {

        @JsonProperty("type")
        private String type;

        @JsonProperty("name")
        private String name;

        private List<String> tagNameList;

        private List<String> userScopeLimits;

        private BigDecimal discount;

        public MarketingTag(String type, String name) {
            this.type = type;
            this.name = name;
        }
    }



}
