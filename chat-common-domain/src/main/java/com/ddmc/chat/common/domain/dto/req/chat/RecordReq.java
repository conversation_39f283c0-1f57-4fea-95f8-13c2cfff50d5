package com.ddmc.chat.common.domain.dto.req.chat;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;


/**
 * 上传音频文件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecordReq {

    @Schema(description = "用户id")
    @NotBlank(message = "uid 不能为空")
    private String uid;


    @Schema(description = "messageId")
    private Long messageId;


}
