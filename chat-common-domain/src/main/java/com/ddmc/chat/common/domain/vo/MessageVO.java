package com.ddmc.chat.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024/9/4 11:22 AM
 * @Description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString(callSuper = true)
public class MessageVO {

    @Schema(description = "messageId")
    private Long messageId;

    @Schema(description = "sessionId")
    private String sessionId;

    /**
     * 一问一答是一个questId
     */
    @Schema(description = "一问一答是一个questId")
    private String questId;

    @Schema(description = "消息体")
    private String content;

    // 保留，前端应该不需要用到，直接读取补全后的 recommendProducts 即可
    @Schema(description = "消息体扩展")
    private String extraContent;

    @Schema(description = "消息类型：1:text;2:商卡消息;3:分段消息;4:图片消息")
    private Integer type;

    @Schema(description = "推荐商品列表")
    private List<ProductInfoVO> recommendProducts;

    @Schema(description = "是否结束")
    private Boolean end;

    @Schema(description = "消息生成时间")
    private String time;

    @Schema(description = "角色 user/assistant/system")
    private String role;//角色

    @Schema(description = "算法下发的埋点字段")
    private String purpose;

    @Schema(description = "猜你想问问题列表")
    private List<String> quickQuestionList;

    @Schema(description = "风控结果:true:风控 false:正常")
    private Boolean riskResult;

    @Schema(description = "话题 id")
    private String topicId;

}
