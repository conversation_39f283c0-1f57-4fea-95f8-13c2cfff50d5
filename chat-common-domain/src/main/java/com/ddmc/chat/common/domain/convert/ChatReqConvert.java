package com.ddmc.chat.common.domain.convert;

import com.ddmc.chat.common.domain.dto.req.chat.ClearMessageReq;
import com.ddmc.chat.common.domain.dto.req.chat.CreateTopicReq;
import com.ddmc.chat.common.domain.dto.req.chat.DeleteMessageReq;
import com.ddmc.chat.common.domain.dto.req.chat.StartReq;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,imports = {
    LocalDateTime.class,
    Date.class, ZoneId.class})
public interface ChatReqConvert {

    StartReq createTopicReq2StartReq(CreateTopicReq req);

    StartReq deleteMessageReq2StartReq(DeleteMessageReq startReq);

    StartReq clearMessageReq2StartReq(ClearMessageReq startReq);

    CreateTopicReq clearMessageReq2CreateTopicReq(ClearMessageReq req);

    CreateTopicReq deleteMessageReq2CreateTopicReq(DeleteMessageReq req);

}
