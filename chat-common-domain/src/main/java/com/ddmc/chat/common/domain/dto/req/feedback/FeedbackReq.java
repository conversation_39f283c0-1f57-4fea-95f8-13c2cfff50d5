package com.ddmc.chat.common.domain.dto.req.feedback;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/9/4 11:16 AM
 * @Description: 用户点赞、点踩
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FeedbackReq {


    @Schema(description = "uid")
    private String uid;

    @Schema(description = "questId")
    private String questId;

    @Schema(description = "messageId")
    private Long messageId;

    /**
     * 1:赞  2:踩
     */
    @Schema(description = "1:赞  2:踩")
    private Integer comment;

    /**
     * 详细点评
     */
    @Schema(description = "详细点评")
    private String detailComment;

}
