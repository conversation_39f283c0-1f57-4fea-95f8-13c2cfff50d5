package com.ddmc.chat.common.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString(callSuper = true)
public class AiParamVO {

    /**
     * sug 词信息
     */
    private IntentionInfoVO intentionInfo;
    /**
     * 来源类型: 1,搜索发现 2,搜索提示 3,搜索历史 4,主动跳转DS 5,算法分发DS
     */
    private Integer sourceType;

}
