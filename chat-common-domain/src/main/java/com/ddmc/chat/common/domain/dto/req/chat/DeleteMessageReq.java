package com.ddmc.chat.common.domain.dto.req.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 删除指定聊天记录
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DeleteMessageReq extends StartReq {

    @Schema(description = "消息 message id")
    @NotEmpty(message = "messageIdList 不能为空")
    private List<Long> messageIdList;

    @Schema(description = "当前会话 sessionId")
    private String sessionId;

    @Schema(description = "当前会话 topicId")
    private String topicId;

}
