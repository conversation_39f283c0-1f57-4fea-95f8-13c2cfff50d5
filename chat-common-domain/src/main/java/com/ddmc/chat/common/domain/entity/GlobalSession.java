package com.ddmc.chat.common.domain.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/9/4 11:22 AM
 * @Description: 全局session信息
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GlobalSession {


    /**
     * 会话最大生存时长，超过这个时间自动重新开始会话
     */
    @Schema(description = "会话最大时长")
    private Long sessionMaxAliveTime;


    /**
     * 会话最大等待时长，超过这个时间没有交互重新开始会话
     */
    @Schema(description = "会话最大等待时长")
    private Long sessionMaxWaitTime;



}
