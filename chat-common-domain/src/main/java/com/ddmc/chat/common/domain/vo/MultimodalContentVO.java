package com.ddmc.chat.common.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/3/25 11:22 AM
 * @Description: 多模态 VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MultimodalContentVO {

    /**
     * 类型
     */
    private String type;
    /**
     * 文本内容
     */
    private String text;
    /**
     * 图片内容
     */
    @JSONField(name = "image_url")
    @JsonProperty("image_url")
    private ImageData imageUrl;

    @Data
    public static class ImageData {
        /**
         * 图片 url
         */
        private String url;
    }

}
