package com.ddmc.chat.common.domain.dto.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryConfigResponse {

    @Schema(description = "首页可展示商品数量")
    private Integer showProductCnt;
    /**
     * 0 是因为产品要求对这个开关增加降级不展示的能力，说是后期可能该开关会下线
     */
    @Schema(description = "深度思考开关 0-不展示；1-打开；2-关闭；")
    private Integer reasonSwitch;
    /**
     * 模型名称 和开关有映射关系
     * 深度思考开关打开 DeepSeek-R1
     * 深度思考开关关闭 DeepSeek
     */
    @Schema(description = "模型名称")
    private String modelName;

    @Schema(description = "tips 信息")
    private String tips;

}
