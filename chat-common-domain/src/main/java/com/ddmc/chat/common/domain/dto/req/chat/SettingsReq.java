package com.ddmc.chat.common.domain.dto.req.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;


/**
 * 用户设置
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SettingsReq {

    @Schema(description = "uid")
    @NotBlank(message = "uid 不能为空")
    private String uid;

    // 1:开启 0:关闭,默认关闭，即无需自动播报
    @Schema(description = "audioAutoPlay")
    private Integer audioAutoPlay;

}
