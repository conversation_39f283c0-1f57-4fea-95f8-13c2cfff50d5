package com.ddmc.chat.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString(callSuper = true)
public class AiChatMessageVO {

    /**
     * 一问一答是一个questId
     */
    @Schema(description = "一问一答是一个questId")
    private String questId;

    @Schema(description = "session id")
    private String sessionId;

    @Schema(description = "message id")
    private Long messageId;

    @Schema(description = "消息体")
    private String content;

    @Schema(description = "思维链内容")
    private String reasoningContent;

    @Schema(description = "是否结束")
    private Boolean end;

    @Schema(description = "消息生成时间")
    private String time;

    @Schema(description = "llm生成的标识本次调用的id")
    private String requestId;

    @Schema(description = "消息类型")
    private Integer type;

    @Schema(description = "消息生成角色")
    private String role;

    @Schema(description = "算法下发的，需要埋点的字段")
    private String aiPurpose;

    @Schema(description = "唯一请求标识")
    private String qid;

    @Schema(description = "推荐商品列表")
    private List<ProductInfoVO> recommendProducts;

}
