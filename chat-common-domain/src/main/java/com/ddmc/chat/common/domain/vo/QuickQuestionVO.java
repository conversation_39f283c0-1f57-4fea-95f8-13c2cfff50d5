package com.ddmc.chat.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QuickQuestionVO {
    /**
     * 快捷问题列表
     */
    @Schema(description = "快捷问题列表")
    private List<String> questionList;

    /**
     * 招呼语
     */
    @Schema(description = "招呼语")
    private String greeted;
}
