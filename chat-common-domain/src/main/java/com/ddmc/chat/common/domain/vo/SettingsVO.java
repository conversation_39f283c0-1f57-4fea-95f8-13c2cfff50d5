package com.ddmc.chat.common.domain.vo;


import static com.ddmc.chat.common.domain.Constants.SETTING_KEY_AUDIO_AUTOPLAY;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SettingsVO {

    // 1:开启 0:关闭,默认关闭，即无需自动播报
    @Schema(description = "audioAutoPlay")
    private Integer audioAutoPlay;


    public static SettingsVO of(Map<String, Integer> settingsMap) {
        SettingsVO settings = new SettingsVO();
        if (settingsMap.containsKey(SETTING_KEY_AUDIO_AUTOPLAY)) {
            settings.setAudioAutoPlay(settingsMap.get(SETTING_KEY_AUDIO_AUTOPLAY));
        }

        return settings;
    }

}
