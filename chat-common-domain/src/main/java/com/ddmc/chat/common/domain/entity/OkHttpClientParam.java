package com.ddmc.chat.common.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OkHttpClientParam {
    private Integer connectTimeout;
    private Integer readTimeOut;
    private Integer writeTimeOut;
    private Integer connectionIdleTimeout;
    private Integer connectionPoolSize;
    private Integer maximumAsyncRequests;
    private Integer maximumAsyncRequestsPerHost;
}
