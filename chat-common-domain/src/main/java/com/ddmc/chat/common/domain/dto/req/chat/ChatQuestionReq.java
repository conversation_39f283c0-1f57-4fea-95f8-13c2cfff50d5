package com.ddmc.chat.common.domain.dto.req.chat;

import com.ddmc.chat.common.domain.dto.req.CommonReq;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 对话进行过程中，用户发送问题的请求体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ChatQuestionReq extends CommonReq {

    // 商详页场景下，商品id是必填的
    // 想要咨询的商品，当来源场景不仅限于商品详情页时，这个字段可以为空
    @Schema(description = "商品id")
    private String productId;

    // 用户的问题，可是自己输入，也可能是语音转的文字，还可以是点击的“猜你想问问题/引导词”
    // 但无论来自哪里，这个都不能为空
    // 但这个长度有最大限制，被 messageUserMaxLength 约束
    @Schema(description = "问题")
    private String question;

    @Schema(description = "图片 url 列表")
    private List<String> imageUrls;

    @Schema(description = "是否流式回答，默认true")
    private Boolean stream;

    @NotBlank(message = "sessionId 不能为空")
    @Schema(description = "sessionId,start接口或者completions接口返回的session,若过期，服务端会返回新的sessionId")
    private String sessionId;

    // 分类页场景下，分类页用户当前停留的前端叶子类目 名称和ID，是必填的
    // 当来源场景不仅限于分类页时，这个字段可以为空
    @Schema(description = "叶子分类名称")
    @JsonProperty("front_category_name")
    private String frontCategoryName;

    @Schema(description = "叶子分类ID")
    @JsonProperty("front_category_id")
    private String frontCategoryId;

    // 搜索场景下，用户当前搜索的关键词，是必填的
    // 当来源场景不仅限于搜索页时，这个字段可以为空
    @Schema(description = "搜索关键词")
    @JsonProperty("search_keyword")
    private String searchKeyword;

    @Schema(description = "扩展参数")
    @JsonProperty("extra_param")
    private Map<String, String> extraParamMap;

    @Schema(description = "话题 id")
    private String topicId;

    @Schema(description = "问题来源")
    private String questionSource;

}
