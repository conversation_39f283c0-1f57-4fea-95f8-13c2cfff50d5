package com.ddmc.chat.common.domain.dto.req.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @<PERSON> liwan<PERSON>
 * @Date 2024/9/4 11:16 AM
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EntryReq {

    @Schema(description = "用户id")
    private String uid;

    @Schema(description = "站点id")
    private String stationId;

    @Schema(description = "商品id")
    private String productId;

    @Schema(description = "后端类目树 1,2,3")
    private String backendCategoryPath;

    @Schema(description = "场景: search_float-搜索浮层页")
    private String scene;

    /**
     * app版本
     */
    @Schema(description = "app版本")
    private String appVersion;

    /**
     * native 版本
     */
    @Schema(description = "native版本")
    private String nativeVersion;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    private String channel;

    /**
     * 应用id
     */
    @Schema(description = "应用id 1:ios,2:安卓,3:微信,4:小程序,支付宝小程序:10")
    private String appClientId;

    /**
     * 版本
     */
    @Schema(description = "api版本")
    private String  apiVersion;

}
