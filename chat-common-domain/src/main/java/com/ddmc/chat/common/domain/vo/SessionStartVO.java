package com.ddmc.chat.common.domain.vo;

import com.ddmc.chat.common.domain.entity.FunctionButton;
import com.ddmc.chat.common.domain.entity.Session;
import com.ddmc.chat.common.domain.entity.SugWords;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/9/4 11:21 AM
 * @Description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SessionStartVO {

    /**
     * 欢迎语
     * 这里直接做成String类型的消息，而不是MessageVO对象，会更合理一些
     */
    @Schema(description = "欢迎语信息")
    private MessageVO welcome;

    /**
     * 快捷问题列表
     */
    @Schema(description = "快捷问题列表")
    private List<String> quickQuestionList;

    /**
     * 意图引导词
     */
    @Schema(description = "意图引导词")
    private List<String> intentGuideWords;

    /**
     * 会话信息
     */
    @Schema(description = "会话信息")
    private Session session;

    /**
     * 商卡信息，可能有也可能没有
     */
    @Schema(description = "商卡信息")
    private ProductInfoVO productInfoVO;

    /**
     * 助手头像
     */
    @Schema(description = "助手头像")
    private String avatar;

    /**
     * 是否有历史记录
     */
    @Schema(description = "是否有历史记录")
    private boolean hasHistory;

    /**
     * 是否开启新的语音识别功能
     */
    @Schema(description = "是否开启新的语音识别功能")
    private boolean isAudioEnabled;

    /**
     * 开启语音功能后，所必须的配置信息,
     * 目前有五个字段，appKey, token, expireTime, asrUrl, ttsUrl。
     */
    @Schema(description = "开启语音功能后，所必须的配置信息")
    private ISIConfig isiConfig;

    @Schema(description = "每日小知识")
    private DailyTipsVO dailyTips;

    @Schema(description = "底部功能 button 列表")
    private List<FunctionButton> functionButtonList;

    @Schema(description = "sug 词列表")
    private List<SugWords> sugList;

    @Schema(description = "可分享轮数")
    private Integer shareNumber;

    @Schema(description = "可删除条数")
    private Integer deleteNumber;

    @Schema(description = "新版助手头像")
    private String avatar2;

    @Schema(description = "拍照能力开关")
    private Boolean photoEnabled;

    @Schema(description = "topicId")
    private String topicId;

}
