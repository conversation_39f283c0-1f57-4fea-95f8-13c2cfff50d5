package com.ddmc.chat.common.domain.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/9/19 10:26 AM
 * @Description: 前端公共参数
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonReq {

    // fixme: 风险，用户uid应该从auth中获取，而不是让用户手动传入指定
    @Schema(description = "用户id")
    private String uid;

    @JsonProperty("station_id")
    @NotBlank(message = "station_id 不能为空")
    @Schema(description = "站点id")
    private String stationId;


    @NotBlank(message = "city_number 不能为空")
    @Schema(description = "城市code")
    @JsonProperty("city_number")
    private String cityNumber;

    @Schema(description = "城市名称")
    @JsonProperty("city_name")
    private String cityName;

    @Schema(description = "应用id 1:ios,2:安卓,3:微信,4:小程序,支付宝小程序:10")
    @JsonProperty("app_client_id")
    private String appClientId;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private String latitude;

    /**
     * api版本
     */
    @Schema(description = "api版本")
    @JsonProperty("api_version")
    private String apiVersion;

    /**
     * app版本
     */
    @Schema(description = "app版本")
    @JsonProperty("app_version")
    private String appVersion;

    /**
     * app版本号 h5使用
     */
    @Schema(description =  "app版本号 h5使用")
    @JsonProperty("native_version")
    private String nativeVersion;

    /**
     * h5版本号
     */
    @Schema(description = "h5版本号 h5使用")
    @JsonProperty("h5_source")
    private String h5Source;

    /**
     * 设备编号
     */
    @Schema(description = "设备编号")
    @JsonProperty("device_id")
    private String deviceId;

    /**
     * rn版本
     */
    @Schema(description ="rn版本")
    @JsonProperty("rn_version")
    private String rnVersion;

    /**
     * 来源页面
     */
    @Schema(description = "来源页面")
    @JsonProperty("from")
    private String from;

    /**
     * scene
     */
    @Schema(description = "使用场景（首页：main_page")
    @JsonProperty("scene")
    private String scene;

}
