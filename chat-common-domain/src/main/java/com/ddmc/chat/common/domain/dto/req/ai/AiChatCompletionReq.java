package com.ddmc.chat.common.domain.dto.req.ai;

import com.ddmc.chat.common.domain.dto.req.CommonReq;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 对话进行过程中，用户发送问题的请求体
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AiChatCompletionReq extends CommonReq {

    @Schema(description = "请求唯一标识")
    private String qid;

    // 用户的问题
    @NotBlank(message = "question 不能为空")
    @Schema(description = "问题")
    private String question;

    @Schema(description = "透传算法参数，是个 json 结构的字段")
    @JsonProperty("ai_param")
    private String aiParam;

    @Schema(description = "接入场景: 搜索页AI助手 - search_result_assistant")
    @NotBlank(message = "scene 不能为空")
    private String scene;

    @Schema(description = "会话id，一段时间内的多轮次对话，用同一个sessionId关联, 非必传。不传递服务端生成")
    @JsonProperty("session_id")
    private String sessionId;

    @Schema(description = "对话id，一问一答用同一个questId，算法用来关联对话的上下文，非必传。 不传递服务端生成")
    @JsonProperty("quest_id")
    private String questId;

    @Schema(description = "请求回答方式 true-刷新重新生成回答; false-首次生成回答；")
    @JsonProperty("refresh_answer")
    private Boolean refreshAnswer;

}
