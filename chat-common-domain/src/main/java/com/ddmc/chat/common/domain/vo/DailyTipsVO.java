package com.ddmc.chat.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString(callSuper = true)
public class DailyTipsVO {

    @Schema(description = "每日小知识文本内容")
    private String content;

    @Schema(description = "日期")
    private String date;

    @Schema(description = "农历日期")
    private String lunarDate;

    @Schema(description = "messageId")
    private Long messageId;

}
