package com.ddmc.chat.common.domain.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @<PERSON> <PERSON><PERSON><PERSON>
 * @Date 2024/9/4 11:22 AM
 * @Description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Session {

    /**
     * 会话id
     */
    @Schema(description = "会话id")
    private String sessionId;
    /**
     * 会话开始时间
     */
    @Schema(description = "会话开始时间")
    private Long sessionStartTime;

}
