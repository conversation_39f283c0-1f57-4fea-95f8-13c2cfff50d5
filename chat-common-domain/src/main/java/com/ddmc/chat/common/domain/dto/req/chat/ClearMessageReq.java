package com.ddmc.chat.common.domain.dto.req.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 清空聊天记录
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ClearMessageReq extends StartReq {

    @Schema(description = "当前会话 sessionId")
    private String sessionId;

    @Schema(description = "当前会话 topicId")
    private String topicId;

}
