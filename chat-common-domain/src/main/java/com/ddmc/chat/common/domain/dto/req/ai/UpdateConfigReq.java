package com.ddmc.chat.common.domain.dto.req.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UpdateConfigReq {

    @Schema(description = "用户id")
    private String uid;

    @Schema(description = "算法下发 qid")
    private String qid;

    @Schema(description = "深度思考开关 1-打开；2-关闭；")
    private Integer reasonSwitch;

}
