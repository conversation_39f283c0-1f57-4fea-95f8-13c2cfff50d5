package com.ddmc.chat.common.domain.dto.res.sse.chat;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class /**/AiChatData {

    private String requestId;

    private Long created;

    private String model;

    private String qid;

    private List<AiChatChoice> choices;

    // 计量信息，表示本次请求所消耗的token数据。
    private AiChatUsage usage;

    // 后置追加内容，在stop时，检查appendContents是否有内容，如有，则执行追加处理
    private List<AiChatAppendContents> appendContents;

}
