package com.ddmc.chat.common.domain.dto.req.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryConfigReq {

    @Schema(description = "用户id")
    private String uid;

    @Schema(description = "算法下发 qid")
    private String qid;

    @Schema(description = "场景, DeepSeek Tab 场景:search_result_assistant,主搜综合场景:search_result_assistant_composite")
    private String scene;

}
