package com.ddmc.chat.common.domain.entity;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiChatOpConfig {
    // 默认动作
    private String defaultAction;
    // 动作模板映射
    private Map<String, AiChatOpMdTemplate> actionTemplates;
}
