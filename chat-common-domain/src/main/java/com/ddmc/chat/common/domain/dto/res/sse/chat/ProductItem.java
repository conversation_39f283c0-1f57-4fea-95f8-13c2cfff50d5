package com.ddmc.chat.common.domain.dto.res.sse.chat;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ProductItem {

    /**
     * 商品 id
     */
    @JSONField(name = "p_mongo_id")
    @JsonProperty("p_mongo_id")
    private String pMongoId;
    /**
     * scm 信息
     */
    private String scm;

}
