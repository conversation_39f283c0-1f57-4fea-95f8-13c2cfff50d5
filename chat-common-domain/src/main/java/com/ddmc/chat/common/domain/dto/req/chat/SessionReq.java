package com.ddmc.chat.common.domain.dto.req.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @<PERSON> <PERSON><PERSON><PERSON>
 * @Date 2024/9/4 11:16 AM
 * @Description: 会话开始请求体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper=false)
public class SessionReq {

    @Schema(description = "uid")
    @NotBlank(message = "uid 不能为空")
    private String uid;


    @NotBlank(message = "sessionId 不能为空")
    @Schema(description = "sessionId")
    private String sessionId;

}
