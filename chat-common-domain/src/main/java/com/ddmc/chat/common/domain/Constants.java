package com.ddmc.chat.common.domain;

public class Constants {

    /**
     * 语音播报设置开关
     */
    public static final String SETTING_KEY_AUDIO_AUTOPLAY = "audioAutoPlay";
    /**
     * 深度思考开关
     */
    public static final String SETTING_KEY_REASON_SWITCH = "reasonSwitch";

    public static final Integer SETTING_VALUE_ON = 1;

    public static final Integer SETTING_VALUE_OFF = 0;

    public static final Integer SETTING_VALUE_DEFAULT = -1;

    /**
     * 消息的三种状态，与{@link com.ddmc.chat.common.mapper.DO.MessageBoxDO#commitStatus}的定义一致。
     *  0：未提交不能被读取； 1：已经提交，可以被正常读取； 2：未知状态，需要进一步确认；
     */
    public static final Integer MSG_STATUS_UNCOMMIT = 0;
    public static final Integer MSG_STATUS_COMMIT = 1;
    public static final Integer MSG_STATUS_UNKNOWN = 2;


    /**
     * 消息发送的三种角色，与{@link com.ddmc.chat.common.mapper.DO.MessageBoxDO#sender}的定义一致。
     * user: 用户；assistant: 机器人；system: 系统
     */
    public static final String MSG_SENDER_USER = "user";
    public static final String MSG_SENDER_ASSISTANT = "assistant";
    public static final String MSG_SENDER_SYSTEM = "system";


    /**
     * 消息的两种类型，与{@link com.ddmc.chat.common.mapper.DO.MessageBoxDO#type}的定义一致。
     * 1：文本消息，所有的超链接、商卡，前端都要求用这种文本类型
     */
    public static final Integer MSG_TYPE_TEXT = 1;

    /**
     * 聊天页面的商卡展示，是否需要补全标签信息
     * 一种是在页面头部的，新建的pageId默认为 ai_chat_food_card；一种是在聊天框内的，新建的pageId为 ai_chat_page；
     */
    public static final String MSG_CARD_PAGE_ID_TITLE = "ai_chat_float_card";
    public static final String MSG_CARD_PAGE_ID_RECOMMENDATION = "ai_chat_page";
    public static final String MSG_CARD_PAGE_ID_SEARCH_RESULT_PAGE = "search_result";

    /**
     * appendContent purpose
     */
    public static final String MSG_APPEND_CONTENT_PURPOSE_ARRIVAL_ALERT = "到货提醒";
    public static final String MSG_APPEND_CONTENT_PURPOSE_CUSTOM_SERVICE = "客服";

    /**
     * 请求来源 - 分类页
     */
    public static final String REQ_SOURCE_CATEGORY = "category";

    /**
     * 请求来源 - 叮小咚新形态
     */
    public static final String REQ_SOURCE_DXD_NEW = "dxd_new";

    /**
     * 对话场景 - 首页底部 tab
     */
    public static final String COMPLETIONS_HOME_PAGE_SCENE = "home_page";

    /**
     * sse response complete message
     */
    public static final String RESPONSE_BODY_EMITTER_COMPLETED = "ResponseBodyEmitter has already completed";

    public static final String DEGRADE_CODE = "-3000";
    public static final String DEGRADE_MESSAGE = "服务器拥挤，请稍后再试~";

    /**
     * start completions 接口扩展参数 key
     * 评价列表页接入技术文档：https://cfl.corp.100.me/pages/viewpage.action?pageId=209104901
     */
    // 评价列表页标签 key,
    public static final String TRAIT_KEY = "traitKey";
    // 评价列表页标签类型
    public static final String TRAIT_TYPE = "traitType";


    /**
     * start completions 接口扩展参数 key
     * 品质之爱落地页接入技术文档：https://cfl.corp.100.me/pages/viewpage.action?pageId=218630988
     */
    // 品质之爱 tab 名称 key,
    public static final String LOVE_QUALITY_TAB_NAME = "loveQualityTabName";
    // 品质之爱飘红文本 key
    public static final String LOVE_QUALITY_TEXT = "loveQualityText";

}
