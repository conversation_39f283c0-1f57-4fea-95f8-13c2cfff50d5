package com.ddmc.chat.common.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/9/4 11:16 AM
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChatEntryVO {

    @Schema(description = "是否展示入口")
    private boolean showEntry;

    @Schema(description = "入口文案")
    private String text;

    @Schema(description = "入口跳转链接")
    private String link;

}
