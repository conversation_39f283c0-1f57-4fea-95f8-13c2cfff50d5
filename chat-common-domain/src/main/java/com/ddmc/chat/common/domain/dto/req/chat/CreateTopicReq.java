package com.ddmc.chat.common.domain.dto.req.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 创建新话题
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CreateTopicReq extends StartReq {

    @Schema(description = "当前会话 sessionId")
    private String sessionId;

    @Schema(description = "当前会话 topicId")
    private String topicId;

}
