package com.ddmc.chat.common.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FunctionButton {

    /**
     * 功能 button 展示文案
     */
    private String text;
    /**
     * 自动输入文案
     */
    private String displayText;
    /**
     * 展示顺序
     */
    private Integer sort;
    /**
     * 类型 1-今天吃什么；2-拍照试食；3-拍照识别营养；4-拍照识卡路里；5-菜谱做法；0-自定义
     */
    private Integer type;
    /**
     * 图标
     */
    private String icon;

}
