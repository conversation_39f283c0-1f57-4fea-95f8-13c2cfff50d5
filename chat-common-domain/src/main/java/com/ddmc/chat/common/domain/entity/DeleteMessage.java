package com.ddmc.chat.common.domain.entity;


import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeleteMessage {

    /**
     * 要删除聊天记录的用户 uid
     */
    private String userId;

    /**
     * 要删除的聊天记录的 questId
     */
    private List<String> questIdList;

    /**
     * 是否清空所有消息
     */
    private Boolean deleteAll;

}
