以下是我的项目基础信息

# 核心技术栈
  SpringBoot、Mybatis-Plus、JDK11、PowerMockRunner、Junit5

# 项目结构
* chat-api-service - controller 接口层代码
* chat-client - rpc 接口 client 代码
* chat-common - 通用工具类代码, 包含常量类、枚举、Exception 以及通用 Util 等代码
* chat-common-domain - 通用领域实体对象，包含所有的 VO、BO、DTO、Entity 类以及对象转换的 convert 类代码
* chat-common-mapper - 存放数据库层的 mapper 类和 DO 类，以及 mapper.xml 文件
* chat-common-service - 存放 service 层代码，包含服务接口和服务实现类代码
* chat-common-infra - 存放数据层 DAO 类，handler 处理类，Apollo 的配置类，以及第三方 rpc 接口的代理类等代码

接下来我向你举个例子，比如我要实现一个完整的接口功能 com.ddmc.chat.api.controller.ChatApiController#completions
1. 这个接口的 controller 层位于 chat-api-service 模块中 com.ddmc.chat.api.controller.ChatApiController#completions 
2. service 层位于 chat-common-service 模块中 com.ddmc.chat.common.service.impl.ChatServiceImpl#completions
3. 建了一个 handler 层 com.ddmc.chat.common.infra.handler.MessageBoxHandler 处理和 dao 层的交互以及其他通用类型转换等功能
4. 在 handler 层中会引入 dao 层，如 com.ddmc.chat.common.infra.dao.MessageBoxDAO
5. 在 dao 层会引入具体的 mapper 处理类，如 com.ddmc.chat.common.mapper.MessageBoxMapper

# 已有的通用工具类介绍
* id 生成 com.ddmc.chat.common.util.OUIDGenerator#generate(java.lang.String)
* 对象转换可以使用 mapstruct 进行简化，非必要不用逐个属性去 set, 使用 mapstruct 的 convert 类可以放置在模块 chat-common-infra 中的 convert 下
* 抛出异常时可以直接使用 com.ddmc.chat.common.exception.BizException，不用直接排除 RuntimeException

# 关于单元测试
项目整个模块的单元测试都位于 chat-api-service 模块中的 test 目录下，其中单元测试框架使用了 PowerMockRunner、Junit5，其中 PowerMockRunner 可以用来对私有方法进行单元测试

# 用户饮食记录需求

现在我需要实现一个通过用户拍照，根据照片识别用户餐食，调用第三方大模型接口来识别餐食中的营养元素功能

* 接口我已经定义在了 com.ddmc.chat.api.controller.MealRecordController
* service 层接口位于 com.ddmc.chat.common.service.MealRecordService，对应实现类在 com.ddmc.chat.common.service.impl.MealRecordServiceImpl
* 相关数据层文件位于 chat-common-mapper 模块中
- 主要有三张表，sql 表结构如下：
```sql
------ （分库）用户饮食记录域 ------ 
-- 用户每日摄入营养汇总，数值为 当天所有餐次 的累加
CREATE TABLE
  `meal_daily_summary` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '用户uid',
    `meal_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '用餐时间',
    `total_calories` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '摄入总热量',
    `total_weight` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '摄入总重量',
    `total_carbs` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '总碳水重量',
    `total_protein` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '总蛋白质重量',
    `total_fat` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '总脂肪重量',
    `total_fiber` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '总膳食纤维重量',
    `total_sugar` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '总糖分重量',
    `summary` varchar(50) NOT NULL DEFAULT '' COMMENT 'Ai 总结',
    `extra_content` text DEFAULT '' COMMENT '扩展字段',
    `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '是否已删除，0:正常；1:已删除',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `drc_check_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT 'drc时间校验字段',
    `ezone_shard_id` int DEFAULT NULL COMMENT '多活分片信息',
    PRIMARY KEY (`id`),
    KEY `idx_user_id_meal_date` (`user_id`, `meal_date`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_update_time` (`update_time`),
    KEY `idx_drc_check_time` (`drc_check_time`),
    KEY `idx_ezone_shard_id` (`ezone_shard_id`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户饮食记录每日汇总表'; 
 
------ （分库）用户餐食域 ------
-- 用户某日某次餐的记录，同时冗余营养值（如：calories, protein 等），数值为 当前餐次内所有餐食 的累加
CREATE TABLE
  `meals` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '用户 uid',
    `meal_id` varchar(100) NOT NULL DEFAULT '' COMMENT '餐次 id',
    `meal_type` tinyint NOT NULL DEFAULT '0' COMMENT '餐次类型',
    `meal_image` varchar(50) NOT NULL DEFAULT '' COMMENT '餐次照片',
    `total_calories` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '本餐次摄入总卡路里',
    `total_weight` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '本餐次总重量',
    `total_carbs` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '本餐次碳水重量',
    `total_protein` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '本餐次蛋白质重量',
    `total_fat` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '本餐次脂肪重量',
    `total_fiber` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '本餐次总膳食纤维重量',
    `total_sugar` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '本餐次总糖分重量', 
    `meal_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '餐次记录时间',
    `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '是否已删除，0:正常；1:已删除',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `drc_check_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT 'drc时间校验字段',
    `ezone_shard_id` int DEFAULT NULL COMMENT '多活分片信息',
    PRIMARY KEY (`id`),
    KEY `idx_user_id_meal_date` (`user_id`, `meal_date`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_update_time` (`update_time`),
    KEY `idx_drc_check_time` (`drc_check_time`),
    KEY `idx_ezone_shard_id` (`ezone_shard_id`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户餐次记录表';
 
-- 每次餐中包含的具体食物及份量，同时也将 营养值（如：calories, protein 等）冗余到 meal_items 中
CREATE TABLE
  `meal_items` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `meal_id` varchar(100) NOT NULL DEFAULT '0' COMMENT '餐次 id',
    `meal_item_id` varchar(100) NOT NULL DEFAULT '' COMMENT '餐食 id',
    `meal_item_name` varchar(100) NOT NULL DEFAULT '' COMMENT '餐食名称',
    `measurement_unit` VARCHAR(20) NOT NULL DEFAULT 'g' COMMENT '重量单位，如 g/ml',
    `user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '用户uid',
    `meal_item_image` varchar(50) NOT NULL DEFAULT '' COMMENT '食物照片',
    `calories` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '摄入卡路里',
    `weight` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '总重量',
    `carbs` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '碳水重量',
    `protein` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '蛋白质重量',
    `fat` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '脂肪重量',
    `fiber` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '膳食纤维重量',
    `sugar` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '糖分重量',
    `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '是否已删除，0:正常；1:已删除',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `drc_check_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT 'drc时间校验字段',
    `ezone_shard_id` int DEFAULT NULL COMMENT '多活分片信息',
    PRIMARY KEY (`id`),
    KEY `idx_user_id_meal_id` (`user_id`, `meal_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_update_time` (`update_time`),
    KEY `idx_drc_check_time` (`drc_check_time`),
    KEY `idx_ezone_shard_id` (`ezone_shard_id`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户餐次食物记录表';
```
- 对应的数据层 mapper 类对应关系如下：
  - meal_daily_summary：
    - com.ddmc.chat.common.mapper.MealDailySummaryMapper
    - com.ddmc.chat.common.mapper.DO.MealDailySummaryDO
  - meal_items
    - com.ddmc.chat.common.mapper.DO.MealItemsDO
    - com.ddmc.chat.common.mapper.MealItemsMapper
  - meals
    - com.ddmc.chat.common.mapper.DO.MealsDO
    - com.ddmc.chat.common.mapper.MealsMapper

接下来我们将逐步来实现 com.ddmc.chat.api.controller.MealRecordController 中定义的所有接口，你需要先熟悉我的项目结构和代码风格，之后我将逐个告诉你每个接口的详细需求，然后我们来分步实现

# 已实现功能记录

## 1. 新增饮食记录接口 (saveMeal)

实现了 `MealRecordController#saveMeal` 接口，用于保存用户的饮食记录。接口入参为 `SaveMealRequest`，返回 `SaveMealVO`。

### 实现文件清单
1. **DAO层**：
   - `chat-common-infra/src/main/java/com/ddmc/chat/common/infra/dao/MealsDAO.java`
   - `chat-common-infra/src/main/java/com/ddmc/chat/common/infra/dao/MealItemsDAO.java`
   - `chat-common-infra/src/main/java/com/ddmc/chat/common/infra/dao/MealDailySummaryDAO.java`

2. **Handler层**：
   - `chat-common-infra/src/main/java/com/ddmc/chat/common/infra/handler/MealRecordHandler.java`

3. **Service层**：
   - `chat-common-service/src/main/java/com/ddmc/chat/common/service/impl/MealRecordServiceImpl.java` - 实现了 `saveMeal` 方法

4. **Controller层**：
   - `chat-api-service/src/main/java/com/ddmc/chat/api/controller/MealRecordController.java` - 修改了 `saveMeal` 方法

5. 餐次类型枚举 com.ddmc.chat.common.enums.MealTypeEnum

6. **Convert层**：
   - `chat-common-infra/src/main/java/com/ddmc/chat/common/infra/convert/MealRecordConvert.java` - 使用 MapStruct 实现对象转换

### 业务流程
1. 接收 `SaveMealRequest` 请求对象
2. 生成餐次ID (UUID)
3. 计算餐次中所有食物的营养元素总和
4. 保存餐次记录到 `meals` 表
5. 批量保存食物项记录到 `meal_items` 表
6. 更新或创建当日营养汇总记录到 `meal_daily_summary` 表
7. 使用 `@Transactional` 注解确保数据一致性

### 数据处理特点
1. 采用 `MealRecordHandler` 进行业务逻辑处理，实现DAO层和Controller层的解耦
2. 在 Handler 中进行数据转换、汇总计算和事务控制
3. 使用 BigDecimal 处理所有数值计算，确保精度
4. 在每日汇总表中累加营养元素值，便于后续查询统计
5. 多表操作采用事务保证数据一致性
6. 使用 MapStruct 简化对象转换，提高代码可维护性和减少手动赋值出错的可能
7. 基于枚举类型处理餐次类型的转换，提高代码健壮性

后续可以进一步优化的点：
1. 批量插入性能优化
2. 异常情况的细化处理
3. 添加更多的数据校验逻辑

## 2. 删除饮食记录接口 (deleteMeal)

实现了 `MealRecordController#deleteMeal` 接口，支持两种删除场景：删除整个餐次记录或删除单个食物项。接口入参为 `UpdateMealRequest`，返回 `Boolean`。

### 实现文件清单
1. **DAO层**：
   - 在 `MealsDAO` 中添加 `deleteMealById` 方法
   - 在 `MealItemsDAO` 中添加 `deleteMealItemById` 和 `deleteMealItemsByMealId` 方法

2. **Handler层**：
   - 在 `MealRecordHandler` 中添加 `deleteMeal`、`deleteMealById` 和 `deleteMealItemById` 方法

3. **Service层**：
   - 修改 `MealRecordService` 接口，将 `deleteMeal` 方法返回类型改为 `Boolean`
   - 在 `MealRecordServiceImpl` 中实现 `deleteMeal` 方法

4. **Controller层**：
   - 在 `MealRecordController` 中实现 `deleteMeal` 方法

### 业务流程
1. 接收 `UpdateMealRequest` 请求对象
2. 根据请求中的 `mealId` 和 `mealItemId` 判断删除类型：
   - 如果有 `mealId`，则删除整个餐次记录及其所有食物项
   - 如果有 `mealItemId`，则只删除单个食物项
3. 删除整个餐次记录的流程：
   - 查询餐次记录和所有食物项
   - 更新每日汇总信息，减去该餐次的营养值
   - 删除所有食物项
   - 删除餐次记录
4. 删除单个食物项的流程：
   - 查询食物项和所属餐次
   - 更新餐次记录的营养总值
   - 更新每日汇总信息
   - 删除食物项
5. 使用 `@Transactional` 注解确保数据一致性

### 数据处理特点
1. 支持多种删除场景，提供灵活的业务操作
2. 在删除数据的同时，确保营养汇总数据的一致性
3. 实现逻辑删除（设置 `is_delete` 字段），而非物理删除
4. 处理删除后可能出现的负值情况，确保数据合理性
5. 进行用户权限校验，确保用户只能删除自己的记录
6. 使用事务保证复杂操作的数据一致性
7. 完善的异常处理和日志记录

后续可以进一步优化的点：
1. 增加删除记录的日志，方便追踪用户操作
2. 添加恢复删除记录的功能
3. 优化删除大量数据时的性能

## 3. 修改饮食记录接口 (updateMeal)

实现了 `MealRecordController#updateMeal` 接口，用于修改用户的饮食记录。接口入参为 `UpdateMealRequest`，返回 `UpdateMealVO`。

### 实现文件清单
1. **DAO层**：
   - 在 `MealsDAO` 中添加 `updateMeal` 方法
   - 在 `MealItemsDAO` 中添加 `updateMealItem` 方法

2. **Handler层**：
   - 在 `MealRecordHandler` 中添加 `updateMeal` 和 `processUpdateMealItems` 方法

3. **Convert层**：
   - 在 `MealRecordConvert` 中添加 `convertToUpdateMealsDO`、`convertToUpdateMealItemsDO` 和 `createUpdateMealVO` 方法

4. **Service层**：
   - 在 `MealRecordServiceImpl` 中实现 `updateMeal` 方法

5. **Controller层**：
   - 在 `MealRecordController` 中实现 `updateMeal` 方法

### 业务流程
1. 接收 `UpdateMealRequest` 请求对象
2. 查询原餐次记录和原食物项列表
3. 处理每日汇总数据，先减去原餐次数据的贡献
4. 更新餐次基本信息（图片、日期、餐次类型等）
5. 处理餐次食物项：
   - 保留并更新已标记要保留的食物项
   - 新增未在原列表中的新食物项
   - 删除不在新列表中的旧食物项
6. 重新计算餐次的营养元素总和
7. 更新或保存每日汇总数据
8. 使用 `@Transactional` 注解确保数据一致性

### 数据处理特点
1. 采用先删后加的策略处理每日汇总表，确保数据一致性
2. 使用Map和Set数据结构高效处理食物项的更新、新增和删除
3. 对每个食物项的操作进行单独的错误处理，提高代码健壮性
4. 复用已有的营养元素计算和汇总逻辑，保持代码简洁
5. 对修改后的每日汇总数据进行非负检查，确保数据合理性
6. 对用户权限进行严格校验，确保安全性
7. 使用事务保证复杂操作的数据一致性

后续可以进一步优化的点：
1. 对餐次修改操作进行详细的操作日志记录
2. 引入乐观锁或版本控制，处理并发修改的情况
3. 支持批量修改多个餐次记录
4. 优化数据库操作，减少事务处理时间

## 4. 查询饮食记录接口 (queryMeal)

实现了 `MealRecordController#queryMeal` 接口，用于查询用户指定日期的饮食记录。接口入参为 `QueryMealRequest`，返回 `MealPageVO`，包含餐次列表、每日热量分析和营养比例等信息。

### 实现文件清单
1. **DAO层**：
   - 使用 `MealsDAO` 中的 `selectByUserIdAndDate` 方法查询指定日期的餐次记录
   - 使用 `MealItemsDAO` 中的 `selectByMealId` 方法查询餐次的食物项
   - 使用 `MealDailySummaryDAO` 中的 `selectByUserIdAndDate` 方法查询每日汇总数据

2. **Handler层**：
   - 在 `MealRecordHandler` 中添加 `queryMeal` 方法和一系列辅助方法：
     - `convertMealItemsDOToVO`: 将食物项DO转换为VO
     - `calculateMealCalorieRatio`: 计算单个餐次的营养比例
     - `calculateDailyCalorieRatio`: 计算每日营养比例
     - `calculateMealAnalysis`: 计算各餐次热量分布
     - `calculatePercentage`: 计算营养比例百分比

3. **Service层**：
   - 在 `MealRecordServiceImpl` 中实现 `queryMeal` 方法

4. **Controller层**：
   - 在 `MealRecordController` 中实现 `queryMeal` 方法

### 业务流程
1. 接收 `QueryMealRequest` 请求对象，包含用户ID和日期
2. 解析日期字符串为 `LocalDateTime` 对象
3. 查询指定日期的所有餐次记录和每日汇总信息
4. 对于每个餐次记录，查询其对应的食物项列表
5. 计算每个餐次的营养比例
6. 计算每日总体的营养比例
7. 计算各餐次在热量、碳水、蛋白质、脂肪等维度的分布占比
8. 构建并返回最终结果

### 数据处理特点
1. **营养比例计算**：基于标准能量换算(碳水4千卡/克、蛋白质4千卡/克、脂肪9千卡/克)计算各营养素的能量贡献比例
2. **数据格式转换**：将数值类型转换为字符串，便于前端展示
3. **空值处理**：对于没有餐次记录或没有每日汇总的情况进行适当处理，返回空结构而非null
4. **多维度分析**：提供热量、碳水、蛋白质、脂肪四个维度的餐次分布分析
5. **按餐次类型排序**：返回的餐次列表按照类型(早餐→午餐→下午茶→晚餐→夜宵)排序

### 异常处理
1. 参数校验异常：用户ID为空、日期格式错误等
2. 业务逻辑异常：日期解析失败等
3. 系统异常：记录错误日志，并返回友好的错误信息给用户

### 特别说明
AI摘要字段预留了接口，但暂未实现具体内容生成，留待后续由调用方实现。

此接口为用户提供了全面的饮食记录查询功能，用户可以查看指定日期的所有餐次和食物项，了解各餐次的营养分布情况，对健康饮食管理非常有帮助。

## 5. 每日热量查询接口 (queryDailyKcal)

### 功能概述
`queryDailyKcal`接口用于查询用户在指定日期范围内的每日热量汇总数据。此功能允许用户查看自己在一段时间内的每日热量摄入情况，数据来源于`meal_daily_summary`表。

### 接口实现层级
1. **Controller层**：接收前端请求，校验参数，调用Service层方法，处理异常并返回统一响应格式。
2. **Service层**：负责业务逻辑封装，记录日志，处理异常，调用Handler层实现具体业务操作。
3. **Handler层**：处理具体业务逻辑，解析日期参数，调用DAO层查询数据，转换结果格式。
4. **DAO层**：直接操作数据库，封装查询逻辑，返回DO对象。

### 业务流程
1. 前端传入日期区间参数`startDate`和`endDate`。
2. 后端解析日期参数，转换为`LocalDateTime`格式。
3. 查询指定日期范围内的每日热量汇总数据。
4. 将DO对象转换为VO对象返回给前端。

### 数据处理特点
1. **日期处理**：将字符串格式的日期转换为`LocalDateTime`对象，并对结束日期加1天以包含当天的记录。
2. **数据过滤**：只查询未删除的记录，并按照日期升序排序。
3. **数据转换**：将数据库返回的`BigDecimal`类型的热量值转换为字符串类型，便于前端处理。
4. **性能优化**：查询时只选择需要的字段（日期和热量），减少数据传输量。

### 异常处理
接口在各层都实现了完善的异常处理机制：
1. 参数校验异常：用户ID为空、日期格式错误等。
2. 业务逻辑异常：封装为`BizException`并向上传递。
3. 系统异常：记录错误日志，并返回友好的错误信息给用户。

### 日志记录
在Service层记录了完整的请求和响应日志，包括：
1. 请求开始日志，记录请求参数。
2. 请求结束日志，记录日期区间和结果条数。
3. 错误日志，记录异常信息。

此接口为移动端提供了查看用户饮食热量摄入趋势的重要数据支持，可用于生成热量趋势图表，帮助用户了解自己的饮食习惯。




