//apply from: "${rootDir}/gradle/include/sonar.gradle"
apply from: 'properties.gradle'

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

buildscript {

}

allprojects {
    group = 'com.ddmc.chat'
    version = '1.0.3-RELEASE'
}

subprojects {
    apply plugin: 'common-conventions'

    // see https://cfl.corp.100.me/pages/viewpage.action?pageId=96783557#middlewarebom%E6%8E%A5%E5%85%A5%E8%AF%B4%E6%98%8E-%E6%A0%B9%E7%9B%AE%E5%BD%95build.gradle
    apply plugin: 'com.ddmc.sdkVersionControl'

    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8

    MiddlewareConfig {
        // warn or error，默认warn，error直接抛异常阻断执行
        conflictSeverityLevel = "warn"
        // 默认为false，开启后执行checkConflict会在当前目录下生成conflict.txt文件，与console打印的内容一致
        conflictFileEnable = false
        // 需要排除检查的jar包，多个用分号分隔
        excludePackage ="org.eclipse;org.jetbrains.kotlin"
        // bom版本信息，格式：group:module:version，分号间隔，与引入依赖格式一致
        bomInfo = "com.ddmc:middleware-bom:${middlewareBomVersion};" +
                "com.ddmc:third-party-bom:${thirdPartyBomVersion};" +
                "com.ddmc:middleware-gradle-plugin:${middlewarePluginVersion}"
        // warn or error，默认warn，error直接抛异常阻断执行
        snapshotSeverityLevel = "warn"
    }

    dependencies {
        // 不带依赖版本引包
        // coding 简化, should not change
        compileOnly 'org.mapstruct:mapstruct:1.5.5.Final', 'org.projectlombok:lombok:1.18.34'
        annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final', 'org.projectlombok:lombok:1.18.28', 'org.projectlombok:lombok-mapstruct-binding:0.2.0'

        // test
        testImplementation 'org.mapstruct:mapstruct:1.5.5.Final', 'org.projectlombok:lombok:1.18.34'
        testAnnotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final', 'org.projectlombok:lombok:1.18.28', 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
        testImplementation 'org.junit.jupiter:junit-jupiter'
    }

    // see https://plugins.gradle.org/plugin/io.spring.dependency-management
//    tasks.withType(GenerateModuleMetadata).configureEach {
//        // The value 'enforced-platform' is provided in the validation
//        // error message you got
//        suppressedValidationErrors.add('enforced-platform')
//    }


    // see https://github.com/spring-gradle-plugins/dependency-management-plugin/issues/257
    tasks.withType(GenerateMavenPom).all {
        doLast {
            File file = new File("$buildDir/publications/jar/pom-default.xml")
            def text = file.text
            def pattern = "(?s)(<dependencyManagement>.+?<dependencies>)(.+?)(</dependencies>.+?</dependencyManagement>)"
            java.util.regex.Matcher matcher = text =~ pattern
            if (matcher.find()) {
                text = text.replaceFirst(pattern, "")
                def firstDeps = matcher.group(2)
                text = text.replaceFirst(pattern, '$1$2' + firstDeps + '$3')
            }
            file.write(text)
        }
    }
}