package com.ddmc.chat.common.constant;

/**
 * <AUTHOR>
 * @Date 2024/9/12 2:29 PM
 * @Description:
 */
public class CacheKeyConstants {

    // 记录算法的流式返回文案，每次新收到的文字，都可以通过append方式将新内容追加上去
    private static final String CHAT_MESSAGE_CONTEXT_KEY = "chat:msg:context:%s";

    public static String getMessageContextKey(Long messageId) {
        return String.format(CHAT_MESSAGE_CONTEXT_KEY, messageId);
    }


    // 记录算法返回的额外信息，目前结构是商品id的列表，用逗号分隔
    private static final String CHAT_MESSAGE_EXTRA_KEY = "chat:msg:extra:%s";

    public static String getMessageExtraKey(Long messageId) {
        return String.format(CHAT_MESSAGE_EXTRA_KEY, messageId);
    }


    // 记录当前的sessionId是否有效，sessionId写入时有个固定的过期时间（默认15分钟）
    // 只要sessionId还在，就认为session仍然有效，可以继续使用。否则就要创建一个新的session
    private static final String SESSION_CACHE_KEY = "chat:session:%s";

    public static String getSessionCacheKey(String sessionId) {
        return String.format(SESSION_CACHE_KEY, sessionId);
    }

    // 记录调用aliyun刷新token接口生成的token及过期时间，对应的value为：tokenId,expireTime,
    // expireTime 的单位是 秒
    public static String getAliyunTokenCacheKey() {
        return "chat:aliyun:token";
    }

    // 记录当前 session 是否已经插入过分段消息
    private static final String SESSION_SEGMENT_CACHE_KEY = "chat:session:segment:%s";

    public static String getSessionSegmentCacheKey(String sessionId) {
        return String.format(SESSION_SEGMENT_CACHE_KEY, sessionId);
    }

    // 缓存 topicId
    private static final String TOPIC_ID_CACHE_KEY = "chat:topic:id:%s";

    public static String getTopicIdCacheKey(String userId) {
        return String.format(TOPIC_ID_CACHE_KEY, userId);
    }
}
