package com.ddmc.chat.common.exception;

public class BizException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private final Integer code;

    public BizException(Integer code) {
        super();
        this.code = code;
    }

    public BizException(Integer code, Throwable ex) {
        super(ex);
        this.code = code;
    }

    public BizException(Integer code, String message, Throwable ex) {
        super(message, ex);
        this.code = code;
    }

    public BizException(String message) {
        super(message);
        this.code = 500;
    }

    public Integer getCode() {
        return code;
    }

}
