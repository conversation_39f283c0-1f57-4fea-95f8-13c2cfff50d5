package com.ddmc.chat.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum MessageTypeEnum {

    TEXT(1, "普通文本消息"),
    // 目前在叮小咚中商卡实际返回的是 1，没有返回 2
    // DeepSeek 场景商卡消息是返回的 2
    GOODS_CARD(2, "商卡消息"),
    SEGMENT(3, "分段消息"),
    IMAGE(4, "图片消息"),
    DAILY_TIPS(5, "每日小知识"),
    ;

    private final Integer code;
    private final String desc;

    MessageTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
