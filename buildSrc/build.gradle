// VERSION CHANGE LIST:
// 1.0 : 2023-08-30, init prod-ready
// 2.0 : 2023-09-25, use git submodule
// 2.1 : 2023-11-22, add test related dependencies
// 2.2 : 2024-01-03, manually upgrade log4j2 & logback version for security issue
// 2.3 : 2024-02-27, add pom.xml in META-INF/maven for jar artifact
// 2.4 : 2024-10-11, try to remove guide-platform-bom
// 2.5 : 2024-11-25, to reflect the latest changes in middleware-bom, really support MAZ ability

plugins {
    // Refer https://docs.gradle.org/current/samples/sample_convention_plugins.html
    id 'groovy-gradle-plugin'
}

apply from: "${rootDir.parentFile}/properties.gradle"

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

repositories {
    mavenLocal()
    maven { url nexusUrl }
    // Use the plugin portal to apply community plugins in convention plugins.
    gradlePluginPortal()
}

dependencies {
    // Refer https://docs.spring.io/spring-boot/docs/current/gradle-plugin/reference/htmlsingle/#introduction
    // the plugin should not be changed frequently, just use a fix version that compatible with spring-boot version
    implementation("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
    implementation("com.ddmc:middleware-gradle-plugin:${middlewarePluginVersion}")
}