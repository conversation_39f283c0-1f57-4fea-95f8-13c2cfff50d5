plugins {
    id 'common-conventions'
    id 'application'

    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
}

configurations.all {
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'log4j', module: 'log4j'
    exclude group: 'cn.hutool', module: 'hutool-core'
    exclude group: 'mysql', module: 'mysql-connector-java'
}

dependencies {
    // spring cloud
    implementation 'org.springframework.boot:spring-boot'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation('org.springframework.cloud:spring-cloud-starter-zookeeper-discovery') {
        exclude group: 'org.apache.zookeeper', module: 'zookeeper'
    }
    implementation('org.apache.zookeeper:zookeeper') {
        exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    }

    implementation('io.github.openfeign.form:feign-form')
    implementation('io.github.openfeign.form:feign-form-spring')
    implementation('io.github.openfeign:feign-okhttp')
    implementation('io.github.openfeign:feign-jackson')

    // mysql and orm tools, the mybatis-plus version should not be higher than "3.5.1"
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-autoconfigure'
    implementation('com.baomidou:mybatis-plus-boot-starter:3.5.1') {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-jdbc'
        exclude group: 'org.springframework.boot', module: 'spring-boot-autoconfigure'
    }
    runtimeOnly 'mysql:mysql-connector-java'
    implementation 'com.ddmc:ddmc-jdbc-pool'

    // redis，多活最新版本
    implementation 'csoss.redis:spring-data-redis-client'
    // If use with Lettuce, just enable below two lines,
    implementation 'io.lettuce:lettuce-core:6.3.2.RELEASE'
    implementation 'io.projectreactor:reactor-core:3.6.8'
    // If use with Jedis, needs to enable below 3 lines -- and you maybe still encounter some compatibility issues!
    // Because DRP implement depends on lower version of redis, must explicitly import jedis 2.10.x version
    // and compatible spring-data-redis 2.1.x version. If not necessary, suggest to use Lettuce.
//    implementation 'redis.clients:jedis:2.10.2'
//    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
//    implementation 'org.springframework.data:spring-data-redis:2.1.21.RELEASE'
//    implementation 'org.apache.commons:commons-pool2:2.12.0'


    // mq, only import "pulsar" as default, for "rocketmq", should manually added it in project-module level,
    // copy the below code to the build.gradle of the project-module
//    // rocket mq, see http://mq.srv.mc.dd/rocketmq/sdk%E5%8F%82%E8%80%83.html
//    implementation('org.apache.rocketmq:rocketmq-client:4.9.8.20241205-DDMC-RELEASE')
//    implementation('org.apache.rocketmq:rocketmq-acl:4.9.8.20241205-DDMC-RELEASE')
//    implementation('org.apache.rocketmq:rocketmq-spring-boot-starter:2.1.1.20241205-DDMC-RELEASE') {
//        exclude group: 'org.apache.rocketmq', module: 'rocketmq-client'
//        exclude group: 'org.apache.rocketmq', module: 'rocketmq-acl'
//    }
    // this one should unnecessary, just use normal rocketmq initialization style
    // implementation 'com.alibaba.cloud:spring-cloud-starter-stream-rocketmq:2.2.7.20241205-DDMC-RELEASE'

    // pulsar, 接入文档 http://mq.srv.mc.dd/sdk.html, 建议优先使用原生pulsar的方式，与biz-service-template-client配合使用
    implementation 'org.apache.pulsar:pulsar-client'
    // ddmc-pulsar固定4.1.66，在m1芯片上编译与强制指定的高版本netty有冲突导致编译失败，故禁止掉手动指定netty版本的地方
    // 而且同时依赖两个版本的pulsar会造成冲突，默认只依赖原生pulsar。若代码中使用ddmc-pulsar则需要手动切换依赖
//    implementation 'com.ddmc.pulsar:pulsar-java-spring-boot-starter:1.1.0-RELEASE'

    // es, current the cloud ES service are mainly 7.10.1 version, so the client keep the same version
    implementation 'org.elasticsearch.client:elasticsearch-rest-high-level-client:7.10.2'
    implementation 'org.elasticsearch:elasticsearch:7.10.2'
    // 要在ES中添加中文分词增强，可以先看看 https://github.com/infinilabs/analysis-ik
    // 要在应用里支持中文分词能力，可以先看看 https://github.com/renfei/ik-analyzer，https://github.com/magese/ik-analyzer-solr

    // swagger
//    implementation 'org.springframework.plugin:spring-plugin-core:2.0.0.RELEASE'

}

application {
    startScripts.enabled = false
    distTar.enabled = false
    distZip.enabled = false
    bootStartScripts.enabled = false
    bootDistTar.enabled = false
    bootDistZip.enabled = false
}

def gitVersionCode() {
    // 显示最后一次 commit short ID
    def cmd = 'git rev-parse --short HEAD'
    cmd.execute(null, project.rootDir).text.trim()
}

jar {
    archiveFileName = "${archiveBaseName.get()}-${archiveVersion.get()}_${gitVersionCode()}.${archiveExtension.get()}"

    into("META-INF/maven/$project.group/$project.name") {
        from { generatePomFileForJarPublication }
        rename "pom-default.xml", "pom.xml"
    }
}

bootJar {
    archiveFileName = "${archiveBaseName.get()}-${archiveVersion.get()}_${gitVersionCode()}.${archiveExtension.get()}"

    metaInf {
        from '/tmp/bom.properties'
        into '/META-INF/soa/'
    }
}

//springBoot {
//    // Creates META-INF/build-info.properties for Spring Boot Actuator
//    buildInfo()
//}