plugins {
    id 'common-conventions'

    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
}

jar.enabled = true
bootJar.enabled = false

dependencies {
    // client usually need feign dependency from spring cloud
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
}

// see https://cfl.corp.100.me/pages/viewpage.action?pageId=51047973
jar {
    into("META-INF/maven/$project.group/$project.name") {
        from { generatePomFileForJarPublication }
        rename "pom-default.xml", "pom.xml"
    }
}