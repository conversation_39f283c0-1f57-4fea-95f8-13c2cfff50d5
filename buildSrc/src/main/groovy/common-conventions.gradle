plugins {
    id 'base'
    id 'java'
    id 'java-library'
    id 'groovy'
    id 'idea'
    id 'maven-publish'
}

repositories {
    mavenLocal()
    maven { url nexusUrl }
    mavenCentral()
    maven {
        url "https://plugins.gradle.org/m2/"
    }
}

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

ext {
    // Those 3rd dependencies are pre-defined by spring-boot-dependencies.pom
    // see https://docs.spring.io/spring-boot/docs/current-SNAPSHOT/reference/htmlsingle/#dependency-versions.properties
    // also https://docs.gradle.org/current/userguide/resolution_rules.html#resolution_rules
    // also https://nexocode.com/blog/posts/spring-dependencies-in-gradle/
    ext['byte-buddy.version'] = '1.12.12'
//    ext['netty.version'] = '4.1.89.Final'
    // 这里再次覆盖了springboot的一些内置依赖组件的版本，注意要和 biz-service-template-client 及 guide-platform-bom 中的依赖保持一致
    ext['groovy.version'] = '3.0.23'
    ext['junit-jupiter.version'] = '5.11.3'
    ext['jackson-bom.version'] = '2.14.3'
    ext['mysql.version'] = '8.0.33'
    ext['log4j2.version'] = '2.17.2'
    ext['logback.version'] = '1.2.13'
    ext['commons-pool2.version'] = '2.12.0'
}

dependencies {
    api platform("org.springframework.boot:spring-boot-dependencies:${springBootVersion}")
    api platform("org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}")
    api platform("org.junit:junit-bom:${junitVersion}")

    // ONLY NEED TO DECLARE MIDDLEWARE-BOM AND GUIDE-PLATFORM-BOM ONCE!
    implementation platform("com.ddmc:middleware-bom:${middlewareBomVersion}")

//    // see https://plugins.gradle.org/plugin/io.spring.dependency-management
//    // 这里放弃采用 enforcedPlatform, 改成在spring-conventions里手动指定zookeeper的版本
//    implementation platform("com.ddmc.biz:guide-platform-bom:${guidePlatformBomVersion}") {
//        exclude(group: 'org.springframework.data', module: 'spring-data-redis')
//        exclude(group: 'redis.clients', module: 'jedis')
//    }

    // apollo
    implementation 'com.ctrip.framework.apollo:apollo-client'
    implementation 'com.ctrip.framework.apollo:apollo-core'

    // log
    implementation 'com.ddmc:ddmc-log'
    // !! if need to use rocklog, then uncomment below
    implementation('com.ddmc:ddmc-log-ext:1.7.6-RELEASE') {
        exclude group: 'com.ddmc.soa', module: 'spring-cloud-ddmc'
    }

    implementation 'org.slf4j:slf4j-api:1.7.36'
    implementation 'ch.qos.logback:logback-classic:1.2.13'

    // corp middleware
    implementation 'com.ddmc.soa:spring-cloud-ddmc'
    implementation 'com.alibaba.csp:sentinel-core'

    // OpenAPI 3
    implementation 'org.springdoc:springdoc-openapi-ui:1.4.8'

    // memory cache
    // For Java 11 or above, use 3.x otherwise use 2.x.
//    implementation 'com.github.ben-manes.caffeine:caffeine:3.1.8'


    // third party utils
    implementation 'com.google.guava:guava:33.2.1-jre'
    // httpclient has been deprecated, we kept here just for some legacy usage
    // if really need to use httpclient, please use the latest: "org.apache.httpcomponents.client5:httpclient5"
    implementation 'org.apache.httpcomponents:httpclient:4.5.14'
    // for fastjson, if you want to use it, please use the latest fastjson2
    implementation 'com.alibaba.fastjson2:fastjson2:2.0.56'
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'org.apache.commons:commons-collections4:4.4'
    implementation 'commons-validator:commons-validator:1.9.0'
    implementation 'commons-codec:commons-codec:1.17.1'
    implementation 'org.apache.commons:commons-pool2:2.12.0'
    implementation 'org.jetbrains:annotations:24.1.0'
    implementation 'net.openhft:zero-allocation-hashing:0.16'

    implementation 'com.fasterxml.jackson.core:jackson-databind'


    // guide specific utils
    implementation 'com.ddmc.guide:biz-service-template-client:2.0.5-RELEASE'

    // privilege-starter log-ext中已经引用，统一使用log-ext的版本，这里无需手动指定
    //implementation 'com.ddmc:gateway-starter:1.8.1-RELEASE'

    // JAX-B dependencies for JDK 9+
    api 'jakarta.xml.bind:jakarta.xml.bind-api:4.0.0'
    api 'org.glassfish.jaxb:jaxb-runtime:4.0.0'

    // ---- below are test dependencies ----
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
        exclude group: 'junit', module: 'junit'
    }
    // junit 5 test support
    //    testImplementation 'org.junit.jupiter:junit-jupiter'
    //    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    // mockito
    testImplementation 'org.mockito:mockito-core:5.12.0'
    testImplementation 'org.mockito:mockito-inline:5.2.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:5.12.0'
    // powermock
    testImplementation 'org.powermock:powermock-module-junit4:2.0.9'
    testImplementation 'org.powermock:powermock-api-mockito2:2.0.9'
    // spock, because our spring version is old, so the groovy version should also keep old for compatibility
    // currently the supported & highest groovy version is "3.x"
    implementation 'org.codehaus.groovy:groovy:3.0.23'
    testImplementation 'org.spockframework:spock-core:2.3-groovy-3.0'
    testImplementation 'org.spockframework:spock-spring:2.3-groovy-3.0'
    // optional dependencies for using Spock
    //    testImplementation "org.hamcrest:hamcrest-core:2.2"   // only necessary if Hamcrest matchers are used
    //    testRuntimeOnly 'net.bytebuddy:byte-buddy:1.14.8' // allows mocking of classes (in addition to interfaces)
    //    testRuntimeOnly "org.objenesis:objenesis:3.3"      // allows mocking of classes without default constructor (together with ByteBuddy or CGLIB)
}


// junit 5 test support
test {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }
}


java {
//    withJavadocJar()
    withSourcesJar()
}


publishing {
    publications {
        jar(MavenPublication) {
            // 默认就是这几个值，需要覆盖时再手动指定
//            groupId project.group
//            artifactId project.name
//            version project.version
            from components.java
            versionMapping {
                usage('java-api') {
                    fromResolutionOf('runtimeClasspath')
                }
                usage('java-runtime') {
                    fromResolutionResult()
                }
            }
        }
    }

    repositories {
        maven {
            if (project.version.endsWith('-SNAPSHOT')) {
                url = nexusSnapshotUrl
            } else {
                url = nexusReleaseUrl
            }
            credentials {
                username nexusUsername
                password nexusPassword
            }
        }
    }
}


javadoc {
    if(JavaVersion.current().isJava9Compatible()) {
        options.addBooleanOption('html5', true)
    }
}