plugins {
    id 'spring-conventions'
}

bootJar {
    mainClassName = 'com.ddmc.chat.api.ChatApi'
}

configurations {
    all*.exclude group: "org.slf4j", module: "slf4j-log4j12"
    all*.exclude group: 'log4j', module: 'log4j'
    all*.exclude group: 'cn.hutool', module: 'hutool-core'
}


dependencies {
    // ========== SECTION 1: project dependency ==========

    api project(':chat-common-service')

    api project(':chat-common-domain')

    // ========== SECTION 2: middleware dependency ==========

    implementation 'org.springframework.boot:spring-boot-starter-webflux'


    // ========== SECTION 3: 2nd-party dependency ==========
    // transformers contract




    // ========== SECTION 4: 3rd-party dependency ==========
    implementation('io.springfox:springfox-swagger2:2.7.0')
    implementation('io.springfox:springfox-swagger-ui:2.7.0')

    // mysql, mybatis-mapper, page-helper (https://mybatis.io/),
//    implementation 'io.mybatis:mybatis-mapper'
//    implementation 'io.mybatis:mybatis-service'
//    implementation 'com.github.pagehelper:pagehelper-spring-boot-starter'
//    runtimeOnly 'mysql:mysql-connector-java'


    //未纳入中间件bom，先手动指定
//    implementation 'com.ddmc:ddmc-jdbc-pool:2.1.5-RELEASE'
}
