package com.ddmc.chat.api.controller;

import com.ddmc.chat.common.domain.dto.req.chat.CancelQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatHistoryReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.EntryReq;
import com.ddmc.chat.common.domain.dto.req.chat.SessionReq;
import com.ddmc.chat.common.domain.dto.req.chat.SettingsReq;
import com.ddmc.chat.common.domain.dto.req.chat.StartReq;
import com.ddmc.chat.common.domain.dto.req.chat.TokenReq;
import com.ddmc.chat.common.domain.vo.ChatEntryVO;
import com.ddmc.chat.common.domain.vo.MessageBoxVO;
import com.ddmc.chat.common.domain.vo.SessionStartVO;
import com.ddmc.chat.common.domain.vo.SettingsVO;
import com.ddmc.chat.common.domain.vo.TokenVO;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.service.ChatService;
import com.ddmc.gateway.api.client.model.UserInfo;
import com.ddmc.gateway.api.client.utils.UserUtil;
import com.ddmc.guide.enhance.rest.SimpleResponseVo;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.info.Info;
import java.util.Map;
import java.util.Objects;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@Slf4j
@OpenAPIDefinition(info = @Info(title = "Chat API", version = "0.1", description = "AI对话相关"))
@RestController
@RequestMapping(value = "/chatApi")
public class ChatApiController {

    @Autowired
    private GlobalApolloConfig globalApolloConfig;

    @Autowired
    private ChatService chatService;


    /**
     * 这个接口由导购调用，不直接对C端。
     * @param entryReq
     * @return
     */
    @Operation(summary = "是否能开启会话")
    @PostMapping(value = "/entry", produces = MediaType.APPLICATION_JSON_VALUE)
    public SimpleResponseVo<ChatEntryVO> entry(@RequestBody EntryReq entryReq) {
        if (StringUtils.isBlank(entryReq.getUid())) {
            UserInfo userInfo = UserUtil.getUserInfo();
            entryReq.setUid(Objects.nonNull(userInfo) ? userInfo.getUid() : entryReq.getUid());
        }
        return SimpleResponseVo.ok(chatService.entry(entryReq));
    }

    @Operation(summary = "会话开始")
    @PostMapping(value = "/start", produces = MediaType.APPLICATION_JSON_VALUE)
    public SimpleResponseVo<SessionStartVO> start(@RequestBody StartReq startReq) {
        if (StringUtils.isBlank(startReq.getUid())) {
            UserInfo userInfo = UserUtil.getUserInfo();
            startReq.setUid(Objects.nonNull(userInfo) ? userInfo.getUid() : startReq.getUid());
        }
        return SimpleResponseVo.ok(chatService.start(startReq));

    }

//    @Operation(summary = "发送消息")
//    @GetMapping(value = "/completions/get")
//    public SseEmitter completionsGet(String uid, String question, String productId, String stationId, HttpServletResponse response) {
//        response.setHeader("Cache-control", "no-cache");
//        response.setHeader("pragma", "no-cache");
//        response.setDateHeader("expires", 0);
//        CustomerSseEmitter sseEmitter = new CustomerSseEmitter();
//        try {
//
//            ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
//            chatQuestionReq.setUid(uid);
//            chatQuestionReq.setQuestion(question);
//            chatQuestionReq.setProductId(productId);
//            chatQuestionReq.setStationId(stationId);
//            chatQuestionReq.setStream(true);
//            return chatService.completions(chatQuestionReq, sseEmitter);
//        } catch (Exception e) {
//            log.error("completions Exception", e);
//            sseEmitter.completeWithError(e);
//        }
//        return sseEmitter;
//    }


    @Operation(summary = "发送消息")
    @PostMapping(value = "/completions")
    public SseEmitter completions(@RequestBody @Valid ChatQuestionReq chatQuestionReq) {
        SseEmitter sseEmitter = new SseEmitter(globalApolloConfig.getSseTimeout());  //默认10秒超时
        try {
            if (StringUtils.isBlank(chatQuestionReq.getUid())) {
                UserInfo userInfo = UserUtil.getUserInfo();
                chatQuestionReq.setUid(Objects.nonNull(userInfo) ? userInfo.getUid() : chatQuestionReq.getUid());
            }

            if (StringUtils.isBlank(chatQuestionReq.getUid())) {
                throw new RuntimeException("uid is blank");
            }

            if (StringUtils.isBlank(chatQuestionReq.getQuestion())
                && CollectionUtils.isEmpty((chatQuestionReq.getImageUrls()))) {
                throw new RuntimeException("question and imageUrls is blank");
            }

            // 截断长度过长的问题
            chatQuestionReq.setQuestion(truncateString(chatQuestionReq.getQuestion(), globalApolloConfig.getMessageUserMaxLength()));

            return chatService.completions(chatQuestionReq, sseEmitter);
        } catch (Exception e) {
            log.error("completions.error", e);
            sseEmitter.completeWithError(e);
            return sseEmitter;
        }
    }

    @Operation(summary = "取消回答")
    @PostMapping(value = "/cancelQuestion", produces = MediaType.APPLICATION_JSON_VALUE)
    public SimpleResponseVo<Boolean> cancelQuestion(@RequestBody CancelQuestionReq cancelQuestionReq) {
        if (StringUtils.isBlank(cancelQuestionReq.getUid())) {
            UserInfo userInfo = UserUtil.getUserInfo();
            cancelQuestionReq.setUid(Objects.nonNull(userInfo) ? userInfo.getUid() : cancelQuestionReq.getUid());
        }
        return SimpleResponseVo.ok(chatService.cancelQuestion(cancelQuestionReq));
    }

    @Operation(summary = "结束会话")
    @PostMapping(value = "/session/end", produces = MediaType.APPLICATION_JSON_VALUE)
    public SimpleResponseVo<Boolean> sessionEnd(@RequestBody SessionReq entryReq) {
        if (StringUtils.isBlank(entryReq.getUid())) {
            UserInfo userInfo = UserUtil.getUserInfo();
            entryReq.setUid(Objects.nonNull(userInfo) ? userInfo.getUid() : entryReq.getUid());
        }
        return SimpleResponseVo.ok(chatService.sessionEnd(entryReq));
    }

    @Operation(summary = "历史消息")
    @PostMapping(value = "/history", produces = MediaType.APPLICATION_JSON_VALUE)
    public SimpleResponseVo<MessageBoxVO> history(@RequestBody ChatHistoryReq chatHistoryReq) {
        if (StringUtils.isBlank(chatHistoryReq.getUid())) {
            UserInfo userInfo = UserUtil.getUserInfo();
            chatHistoryReq.setUid(Objects.nonNull(userInfo) ? userInfo.getUid() : chatHistoryReq.getUid());
        }
        return SimpleResponseVo.ok(chatService.history(chatHistoryReq));
    }


    @Operation(summary = "查看or更新用户设置项目")
    @PostMapping(value = "/settings", produces = MediaType.APPLICATION_JSON_VALUE)
    public SimpleResponseVo<SettingsVO> settings(@RequestBody SettingsReq settingsReq) {
        if (StringUtils.isBlank(settingsReq.getUid())) {
            UserInfo userInfo = UserUtil.getUserInfo();
            settingsReq.setUid(Objects.nonNull(userInfo) ? userInfo.getUid() : settingsReq.getUid());
        }

        if (settingsReq.getAudioAutoPlay() != null) {
            // do update
            chatService.toggleSettings(settingsReq.getUid(), settingsReq.getAudioAutoPlay());
        }

        // query latest settings
        Map<String, Integer> settingsMap = chatService.querySettings(settingsReq.getUid());

        return SimpleResponseVo.ok(SettingsVO.of(settingsMap));
    }


    @Operation(summary = "上传用户输入的音频文件")
    @PostMapping(value = "/record", produces = MediaType.APPLICATION_JSON_VALUE)
    public SimpleResponseVo<String> record(@RequestParam("file") MultipartFile file,
                                              @RequestParam("uid") String uid,
                                              @RequestParam("messageId") Long messageId) {
        if (Strings.isBlank(uid) || messageId == null) {
            log.error("uid or messageId is empty");
            return SimpleResponseVo.fail("uid or messageId is empty");
        }

        // check file size
        if (file == null || file.isEmpty()) {
            log.error("file is empty");
            return SimpleResponseVo.fail("file is empty");
        }

        // max allowed uploaded file size, by default 5M, need convert it to byte then compare
        if (file.getSize() > globalApolloConfig.getAudioMaxSizeInByte()) {
            log.error("file size is too large");
            return SimpleResponseVo.fail("file size is too large");
        }

        // invoke ddfs to upload file
        String fileKey = chatService.uploadFile(file, String.valueOf(messageId));

        if (fileKey == null) {
            log.error("upload file failed");
            return SimpleResponseVo.fail("upload file failed");
        }

        // update message detail history with the file url
        boolean record = chatService.record(uid, messageId, fileKey);

        if (!record) {
            log.error("record failed");
            return SimpleResponseVo.fail("record failed");
        }

        // if reach here, means all logic are executed successfully
        return SimpleResponseVo.ok(fileKey);
    }



    @Operation(summary = "客户端调用阿里云SDK前，需要先获取token，这个接口用来刷新token")
    @PostMapping(value = "/token", produces = MediaType.APPLICATION_JSON_VALUE)
    public SimpleResponseVo<TokenVO> token(@RequestBody TokenReq tokenReq) {
        UserInfo userInfo = UserUtil.getUserInfo();
        if (userInfo == null) {
            return SimpleResponseVo.fail("user info is empty");
        }
        log.debug("token request from {}", userInfo.getUid());

        boolean forceRefreshToken = tokenReq != null && tokenReq.getIsForce() != null ? tokenReq.getIsForce() : false;

        Pair<String, Long> tokenPair = chatService.getToken(forceRefreshToken);

        if (tokenPair == null) {
            return SimpleResponseVo.fail("get token failed");
        }

        return SimpleResponseVo.ok(TokenVO.of(tokenPair.getLeft(), tokenPair.getRight()));
    }

    private static String truncateString(String input, int maxLength) {
        if (input == null) {
            return null;
        }
        if (input.length() > maxLength) {
            return input.substring(0, maxLength) + "..."; // 截断并添加省略号
        }
        return input;
    }

}
