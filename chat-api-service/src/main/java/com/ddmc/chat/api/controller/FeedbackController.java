package com.ddmc.chat.api.controller;

import com.ddmc.chat.common.domain.dto.req.feedback.FeedbackReq;
import com.ddmc.chat.common.domain.vo.FeedbackVO;
import com.ddmc.chat.common.service.FeedBackService;
import com.ddmc.gateway.api.client.model.UserInfo;
import com.ddmc.gateway.api.client.utils.UserUtil;
import com.ddmc.guide.enhance.rest.SimpleResponseVo;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.info.Info;

import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@OpenAPIDefinition(info = @Info(title = "feedback API", version = "0.1", description = "AI消息点评相关"))
@RestController
@RequestMapping(value = "/chatApi")
public class FeedbackController {

    @Autowired
    private FeedBackService feedBackService;

    @Operation(summary = "点赞点踩")
    @PostMapping(value = "/feedBack", produces = MediaType.APPLICATION_JSON_VALUE)
    public SimpleResponseVo<FeedbackVO> feedBack(@RequestBody FeedbackReq feedbackReq) {
        if (StringUtils.isBlank(feedbackReq.getUid())) {
            UserInfo userInfo = UserUtil.getUserInfo();
            feedbackReq.setUid(Objects.nonNull(userInfo) ? userInfo.getUid() : feedbackReq.getUid());
        }
        return SimpleResponseVo.ok(feedBackService.feedBack(feedbackReq));
    }

    @Operation(summary = "二次点评")
    @PostMapping(value = "/feedBack2", produces = MediaType.APPLICATION_JSON_VALUE)
    public SimpleResponseVo<String> feedBack2(@RequestBody FeedbackReq feedbackReq) {
        if (StringUtils.isBlank(feedbackReq.getUid())) {
            UserInfo userInfo = UserUtil.getUserInfo();
            feedbackReq.setUid(Objects.nonNull(userInfo) ? userInfo.getUid() : feedbackReq.getUid());
        }
        return SimpleResponseVo.ok(feedBackService.feedBack2(feedbackReq));
    }


}
