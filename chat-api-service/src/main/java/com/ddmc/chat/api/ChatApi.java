package com.ddmc.chat.api;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.annotation.MapperScans;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.ddmc"})
@MapperScans({
    @MapperScan("com.ddmc.chat.common.mapper")
})@Slf4j
//@EnableAsync
public class ChatApi {

    public static void main(String[] args) {
        try {
            SpringApplication.run(ChatApi.class, args);
        } catch (Exception e) {
            log.error("service start error", e);
        }
    }
}