logging:
  config: classpath:logback-ddmc-guide-std.xml

# open-feign支持三种超时配置，其中优先级 Feign > Ribbon，Hystrix和他们并列，谁短谁生效。
# 按照中间件Sentinel的配置说明，Hystrix必须被关闭，
server:
  tomcat:
    uri-encoding: UTF-8
    accept-count: 100
    max-threads: 400
    min-spare-threads: 20
    max-connections: 2000

# 设置feign 时间
feign:
  hystrix:
    enabled: false
  okhttp:
    enabled: true
  httpclient:
    enabled: false

# 设置ribbon 时间，当Feign超时没配置/错误配置时，做为兜底
# see https://cfl.corp.100.me/pages/viewpage.action?pageId=4846003
# and https://cfl.corp.100.me/pages/viewpage.action?pageId=4847099
ribbon:
  ConnectTimeout: 300
  ReadTimeout: 3000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 0
  eager-load:
    enabled: true
    clients:


# http://apis-docs.new.test.srv.mc.dd/reliability/quickstart/#1-sdk
spring:
  cloud:
    ddmc:
      reliability:
        enabled: true
        feign:
          enabled: true
  jackson:
    default-property-inclusion: non_null
    serialization:
      FAIL_ON_EMPTY_BEANS: false
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: false



mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true



