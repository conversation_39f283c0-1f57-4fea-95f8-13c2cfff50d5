package com.ddmc.chat.common.infra.proxy;

import com.ddmc.chat.api.JunitBaseTest;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.guide.enhance.json.JsonUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

//@ExtendWith(MockitoExtension.class)
class AliyunProxyTest extends JunitBaseTest {

    @Autowired
    private AliyunProxy aliyunProxy;

    @Autowired
    private GlobalApolloConfig globalApolloConfig;

    @Test
    void testGenerateToken_Success() {
        Pair<String, Long> newToken = aliyunProxy.generateToken(globalApolloConfig.getAliyunAppAccessKeyId(),
            globalApolloConfig.getAliyunAppAccessKeySecret(),
            globalApolloConfig.getAliyunAppTokenDomain());
        System.out.println(JsonUtil.toJson(newToken));
        Assertions.assertNotNull(newToken);
        Assertions.assertNotNull(newToken.getLeft());
        Assertions.assertNotNull(newToken.getRight());
    }
}
