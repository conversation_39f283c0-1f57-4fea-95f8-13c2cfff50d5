package com.ddmc.chat.common.infra.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

import com.ddmc.chat.common.infra.dao.TopicMessageDAO;
import com.ddmc.chat.common.infra.dao.TopicsDAO;
import com.ddmc.chat.common.infra.proxy.NlpAiProxy;
import com.ddmc.chat.common.mapper.DO.TopicMessageDO;
import com.ddmc.chat.common.mapper.DO.TopicsDO;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.chat.common.util.OUIDGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TopicHandlerTest {
    @InjectMocks
    private TopicHandler topicHandler;

    @Mock
    private TopicMessageDAO topicMessageDAO;

    @Mock
    private TopicsDAO topicsDAO;

    @Mock
    private NlpAiProxy nlpAiProxy;

    /**
     * TC01: 用户已有话题，应返回已有话题ID
     */
    @Test
    void testGetTopicIdOrInsert_UserHasExistingTopic() {
        String userId = "user1";
        String existingTopicId = "t123456";

        TopicMessageDO mockTopicMessageDO = new TopicMessageDO();
        mockTopicMessageDO.setTopicId(existingTopicId);
        mockTopicMessageDO.setUserId(userId);

        when(topicMessageDAO.selectByUserId(userId)).thenReturn(mockTopicMessageDO);

        String result = topicHandler.getTopicIdOrInsert(userId);

        assertEquals(existingTopicId, result);
        verify(topicMessageDAO).selectByUserId(userId);
    }

    /**
     * TC02: 用户无话题，保存成功，应返回生成的话题ID
     */
    @Test
    void testGetTopicIdOrInsert_NewUser_SaveSuccess() {
        String userId = "user2";
        String generatedTopicId = "t789012";

        when(topicMessageDAO.selectByUserId(userId)).thenReturn(null);
        when(topicsDAO.save(any(TopicsDO.class))).thenReturn(true);

        try (MockedStatic<OUIDGenerator> mockedOUID = mockStatic(OUIDGenerator.class)) {
            mockedOUID.when(() -> OUIDGenerator.generate("t")).thenReturn(generatedTopicId);

            String result = topicHandler.getTopicIdOrInsert(userId);

            assertEquals(generatedTopicId, result);
            verify(topicMessageDAO).selectByUserId(userId);
            verify(topicsDAO).save(any(TopicsDO.class));
        }
    }

    /**
     * TC03: 用户无话题，保存失败，应返回生成的话题ID并监控上报
     */
    @Test
    void testGetTopicIdOrInsert_NewUser_SaveFailed() {
        String userId = "user3";
        String generatedTopicId = "t345678";

        when(topicMessageDAO.selectByUserId(userId)).thenReturn(null);
        when(topicsDAO.save(any(TopicsDO.class))).thenReturn(false);

        try (MockedStatic<OUIDGenerator> mockedOUID = mockStatic(OUIDGenerator.class);
            MockedStatic<MonitorUtil> mockedMonitor = mockStatic(MonitorUtil.class)) {

            mockedOUID.when(() -> OUIDGenerator.generate("t")).thenReturn(generatedTopicId);
            mockedMonitor.when(() -> MonitorUtil.counterOnce("topic_op", "get_topic_id_or_insert_fail")).thenAnswer(invocation -> null);

            String result = topicHandler.getTopicIdOrInsert(userId);

            assertEquals(generatedTopicId, result);
            verify(topicMessageDAO).selectByUserId(userId);
            verify(topicsDAO).save(any(TopicsDO.class));
            mockedMonitor.verify(() -> MonitorUtil.counterOnce("topic_op", "get_topic_id_or_insert_fail"), times(1));
        }
    }
}
