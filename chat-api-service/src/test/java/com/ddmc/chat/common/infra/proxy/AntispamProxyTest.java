package com.ddmc.chat.common.infra.proxy;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.ddmc.antispam.ImgCheckService;
import com.ddmc.antispam.TextCheckService;
import com.ddmc.antispam.common.enums.RepStatusEnum;
import com.ddmc.antispam.common.response.GenericResponse;
import com.ddmc.antispam.dto.CheckResultDTO;
import com.ddmc.antispam.dto.ImgCheckDTO;
import com.ddmc.antispam.dto.TextCheckDTO;
import com.ddmc.chat.common.constant.Constans;
import com.ddmc.chat.common.domain.dto.req.chat.ChatQuestionReq;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.proxy.dto.TextCheckReason;
import com.google.common.collect.Lists;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AntispamProxyTest {

    @Mock
    private TextCheckService textCheckService;

    @Mock
    private ImgCheckService imgCheckService;

    @Mock
    private GlobalApolloConfig globalApolloConfig;

    @InjectMocks
    private AntispamProxy antispamProxy;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testTextCheck_Success_Level0() {
        // Arrange
        TextCheckDTO checkDTO = new TextCheckDTO();
        checkDTO.setContent("This is a test message.");

        CheckResultDTO checkResultDTO = new CheckResultDTO();
        checkResultDTO.setLevel(0);
        TextCheckReason reason2 = new TextCheckReason();
        reason2.setKeyword("safe");
        reason2.setLabel("safe");
        checkResultDTO.setLabels(Arrays.asList(reason2));

        GenericResponse<CheckResultDTO> response = new GenericResponse<>();
        response.setCode(RepStatusEnum.SUCCESS.getCode());
        response.setData(checkResultDTO);

        when(textCheckService.check(eq(checkDTO), any())).thenReturn(response);
        when(globalApolloConfig.getAntispamDegradeSwitch()).thenReturn(0);

        // Act
        List<TextCheckReason> result = antispamProxy.textCheck(checkDTO);

        // Assert
        assertNull(result);
    }

    @Test
    void testTextCheck_Success_Level1_WithoutPoliticalLabel() throws Exception {
        // Arrange
        TextCheckDTO checkDTO = new TextCheckDTO();
        checkDTO.setContent("This is a test message.");

        CheckResultDTO checkResultDTO = new CheckResultDTO();
        checkResultDTO.setLevel(1);
        TextCheckReason reason2 = new TextCheckReason();
        reason2.setKeyword("其他词汇");
        reason2.setLabel("其他词汇");
        checkResultDTO.setLabels(Arrays.asList(reason2));

        GenericResponse<CheckResultDTO> response = new GenericResponse<>();
        response.setCode(RepStatusEnum.SUCCESS.getCode());
        response.setData(checkResultDTO);

        when(textCheckService.check(eq(checkDTO), any())).thenReturn(response);
        when(globalApolloConfig.getAntispamDegradeSwitch()).thenReturn(0);

        // Act
        List<TextCheckReason> result = antispamProxy.textCheck(checkDTO);

        // Assert
        assertNotNull(result);
    }

    @Test
    void testTextCheck_Success_Level2() throws Exception {
        // Arrange
        TextCheckDTO checkDTO = new TextCheckDTO();
        checkDTO.setContent("This is a test message.");

        CheckResultDTO checkResultDTO = new CheckResultDTO();
        checkResultDTO.setLevel(2);

        TextCheckReason reason1 = new TextCheckReason();
        reason1.setKeyword("涉政词汇");
        reason1.setLabel("涉政词汇");
        TextCheckReason reason2 = new TextCheckReason();
        reason2.setKeyword("其他词汇");
        reason2.setLabel("其他词汇");

        checkResultDTO.setLabels(Arrays.asList(reason1, reason2));

        GenericResponse<CheckResultDTO> response = new GenericResponse<>();
        response.setCode(RepStatusEnum.SUCCESS.getCode());
        response.setData(checkResultDTO);

        when(textCheckService.check(eq(checkDTO), any())).thenReturn(response);
        when(globalApolloConfig.getAntispamDegradeSwitch()).thenReturn(0);

        // Act
        List<TextCheckReason> result = antispamProxy.textCheck(checkDTO);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("涉政词汇", result.get(0).getLabel());
        assertEquals("其他词汇", result.get(1).getLabel());
    }

    @Test
    void testTextCheck_Failure() throws Exception {
        // Arrange
        TextCheckDTO checkDTO = new TextCheckDTO();
        checkDTO.setContent("This is a test message.");

        GenericResponse<CheckResultDTO> response = new GenericResponse<>();
        response.setCode(RepStatusEnum.SUCCESS.getCode());
        response.setMessage("Check failed");

        when(textCheckService.check(eq(checkDTO), any())).thenReturn(response);
        when(globalApolloConfig.getAntispamDegradeSwitch()).thenReturn(0);

        // Act
        List<TextCheckReason> result = antispamProxy.textCheck(checkDTO);

        // Assert
        assertNull(result);
    }

    @Test
    void testTextCheck_Exception() throws Exception {
        // Arrange
        TextCheckDTO checkDTO = new TextCheckDTO();
        checkDTO.setContent("This is a test message.");

        when(textCheckService.check(eq(checkDTO), any())).thenThrow(new RuntimeException("Check exception"));
        when(globalApolloConfig.getAntispamDegradeSwitch()).thenReturn(0);

        // Act
        List<TextCheckReason> result = antispamProxy.textCheck(checkDTO);

        // Assert
        assertNull(result);
    }

    @Test
    void testTextCheck_NullResponseData() throws Exception {
        // Arrange
        TextCheckDTO checkDTO = new TextCheckDTO();
        checkDTO.setContent("This is a test message.");

        GenericResponse<CheckResultDTO> response = new GenericResponse<>();
        response.setCode(RepStatusEnum.SUCCESS.getCode());
        response.setData(null);

        when(textCheckService.check(eq(checkDTO), any())).thenReturn(response);
        when(globalApolloConfig.getAntispamDegradeSwitch()).thenReturn(0);

        // Act
        List<TextCheckReason> result = antispamProxy.textCheck(checkDTO);

        // Assert
        assertNull(result);
    }

    @Test
    void testImageCheck_FallbackOn() {
        Mockito.when(globalApolloConfig.getAntispamImageCheckDegradeSwitch()).thenReturn(Constans.ONE);

        List<String> result = antispamProxy.imageCheck(new ImgCheckDTO());

        assertTrue(result.isEmpty());
    }

    @Test
    void testImageCheck_ResponseIsNull() {
        ImgCheckDTO imgCheckDTO = new ImgCheckDTO();
        imgCheckDTO.setUser("1234");
        imgCheckDTO.setImgUrls(Lists.newArrayList("https://ddxq.com/example.jpg"));

        Mockito.when(imgCheckService.check(eq(imgCheckDTO), any())).thenReturn(null);

        List<String> result = antispamProxy.imageCheck(imgCheckDTO);

        assertTrue(result.isEmpty());
    }

    @Test
    void testImageCheck_ResponseIsNotSuccess() {
        GenericResponse<CheckResultDTO> response = new GenericResponse<>();
        response.setCode(RepStatusEnum.PARAM_ERROR.getCode());
        response.setMessage("Error occurred");

        ImgCheckDTO imgCheckDTO = new ImgCheckDTO();
        imgCheckDTO.setUser("1234");
        imgCheckDTO.setImgUrls(Lists.newArrayList("https://ddxq.com/example.jpg"));

        Mockito.when(imgCheckService.check(eq(imgCheckDTO), any())).thenReturn(response);

        List<String> result = antispamProxy.imageCheck(imgCheckDTO);

        assertTrue(result.isEmpty());
    }

    @Test
    void testImageCheck_Level0() {
        CheckResultDTO checkResultDTO = new CheckResultDTO();
        checkResultDTO.setLevel(0);

        GenericResponse<CheckResultDTO> response = new GenericResponse<>();
        response.setCode(RepStatusEnum.SUCCESS.getCode());
        response.setData(checkResultDTO);

        ImgCheckDTO imgCheckDTO = new ImgCheckDTO();
        imgCheckDTO.setUser("1234");
        imgCheckDTO.setImgUrls(Lists.newArrayList("https://ddxq.com/example.jpg"));

        Mockito.when(imgCheckService.check(eq(imgCheckDTO), any())).thenReturn(response);

        List<String> result = antispamProxy.imageCheck(imgCheckDTO);

        assertTrue(result.isEmpty());
    }

    @Test
    void testImageCheck_Level1_WithPoliticalReason() {
        CheckResultDTO checkResultDTO = new CheckResultDTO();
        checkResultDTO.setLevel(1);
        checkResultDTO.setLabels(Lists.newArrayList("涉政词汇", "其他词汇"));

        GenericResponse<CheckResultDTO> response = new GenericResponse<>();
        response.setCode(RepStatusEnum.SUCCESS.getCode());
        response.setData(checkResultDTO);

        when(globalApolloConfig.getAntispamImageCheckDegradeSwitch()).thenReturn(0);
        Mockito.when(imgCheckService.check(any(ImgCheckDTO.class), any()))
            .thenReturn(response);

        List<String> result = antispamProxy.imageCheck(new ImgCheckDTO());

        assertEquals(1, result.size());
        assertEquals("涉政词汇", result.get(0));
    }

    @Test
    void testImageCheck_Level1_WithoutPoliticalReason() {
        CheckResultDTO checkResultDTO = new CheckResultDTO();
        checkResultDTO.setLevel(1);
        checkResultDTO.setLabels(Lists.newArrayList("其他词汇"));

        GenericResponse<CheckResultDTO> response = new GenericResponse<>();
        response.setCode(RepStatusEnum.SUCCESS.getCode());
        response.setData(checkResultDTO);

        Mockito.when(imgCheckService.check(any(ImgCheckDTO.class), any()))
            .thenReturn(response);

        List<String> result = antispamProxy.imageCheck(new ImgCheckDTO());

        assertTrue(result.isEmpty());
    }

    @Test
    void testImageCheck_Level2() {
        CheckResultDTO checkResultDTO = new CheckResultDTO();
        checkResultDTO.setLevel(2);
        checkResultDTO.setLabels(Lists.newArrayList("涉政词汇", "其他词汇"));

        GenericResponse<CheckResultDTO> response = new GenericResponse<>();
        response.setCode(RepStatusEnum.SUCCESS.getCode());
        response.setData(checkResultDTO);

        Mockito.when(imgCheckService.check(any(ImgCheckDTO.class), any()))
            .thenReturn(response);

        List<String> result = antispamProxy.imageCheck(new ImgCheckDTO());

        assertEquals(2, result.size());
        assertTrue(result.contains("涉政词汇"));
        assertTrue(result.contains("其他词汇"));
    }

    @Test
    void testImageCheck_Exception() {
        Mockito.when(imgCheckService.check(any(ImgCheckDTO.class), any()))
            .thenThrow(new RuntimeException("Service error"));

        List<String> result = antispamProxy.imageCheck(new ImgCheckDTO());

        assertTrue(result.isEmpty());
    }

    @Test
    void testBuildImgCheckDTO_Success() {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setImageUrls(Collections.singletonList("http://example.com/image.jpg"));

        // Act
        ImgCheckDTO imgCheckDTO = antispamProxy.buildImgCheckDTO(chatQuestionReq);

        // Assert
        assertNotNull(imgCheckDTO);
        assertEquals("product_assistant", imgCheckDTO.getAppId());
        assertEquals(1, imgCheckDTO.getServerType());
        assertNotNull(imgCheckDTO.getDataId());
        assertEquals("product_assistant", imgCheckDTO.getSceneCode());
        assertEquals(Collections.singletonList("http://example.com/image.jpg"), imgCheckDTO.getImgUrls());
        assertEquals("user123", imgCheckDTO.getUser());
    }
}
