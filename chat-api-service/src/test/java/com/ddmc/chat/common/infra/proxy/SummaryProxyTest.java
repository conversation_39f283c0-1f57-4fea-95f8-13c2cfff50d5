package com.ddmc.chat.common.infra.proxy;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.ddmc.chat.common.constant.Constans;
import com.ddmc.chat.common.domain.vo.ProductInfoVO;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.convert.ProductConvert;
import com.ddmc.summary.client.SummaryClient;
import com.ddmc.summary.client.dto.PageOldProductInfoDTO;
import com.ddmc.summary.client.dto.PageProductIdDTO;
import com.ddmc.summary.client.dto.SummaryBaseResponse;
import com.ddmc.summary.client.dto.request.ProductListRequest;
import com.ddmc.summary.client.enums.AppClientIdEnum;
import com.ddmc.summary.client.enums.ProductMergeSourceEnum;
import com.google.common.collect.Lists;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SummaryProxyTest {

    @Mock
    private SummaryClient summaryClient;

    @Mock
    private ProductConvert productConvert;

    @Mock
    private GlobalApolloConfig globalApolloConfig;

    @Spy
    @InjectMocks
    private SummaryProxy summaryProxy;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetProductList_Success() {
        // Arrange
        ProductListRequest productListRequest = new ProductListRequest();
        productListRequest.setUId("testUid");
        productListRequest.setStationId("testStationId");
        productListRequest.setCityNumber("testCityNumber");
        productListRequest.setApiVersion("testApiVersion");
        productListRequest.setAppClient(AppClientIdEnum.IOS);
        productListRequest.setBreakShelf(true);
        productListRequest.setMustShowVip(true);
        productListRequest.setLongitude("123.456");
        productListRequest.setLatitude("789.012");
        productListRequest.setPageProductIdDTOS(
            Lists.newArrayList(new PageProductIdDTO("testPageId", Arrays.asList("productId1", "productId2"))));
        productListRequest.setProductMergeType(ProductMergeSourceEnum.PRODUCT_DETAIL);

        SummaryBaseResponse<List<PageOldProductInfoDTO>> summaryBaseResponse = new SummaryBaseResponse<>();
        summaryBaseResponse.setSuccess(true);
        summaryBaseResponse.setData(Arrays.asList(new PageOldProductInfoDTO()));

        when(summaryClient.pageProductList(productListRequest)).thenReturn(summaryBaseResponse);

        // Act
        List<PageOldProductInfoDTO> result = summaryProxy.getProductList(productListRequest);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void testGetProductList_Failure() {
        // Arrange
        ProductListRequest productListRequest = new ProductListRequest();
        productListRequest.setUId("testUid");
        productListRequest.setStationId("testStationId");
        productListRequest.setCityNumber("testCityNumber");
        productListRequest.setApiVersion("testApiVersion");
        productListRequest.setAppClient(AppClientIdEnum.IOS);
        productListRequest.setBreakShelf(true);
        productListRequest.setMustShowVip(true);
        productListRequest.setLongitude("123.456");
        productListRequest.setLatitude("789.012");
        productListRequest.setPageProductIdDTOS(Lists.newArrayList(new PageProductIdDTO("testPageId", Arrays.asList("productId1", "productId2"))));
        productListRequest.setProductMergeType(ProductMergeSourceEnum.PRODUCT_DETAIL);

        SummaryBaseResponse<List<PageOldProductInfoDTO>> summaryBaseResponse = new SummaryBaseResponse<>();
        summaryBaseResponse.setSuccess(false);

        when(summaryClient.pageProductList(productListRequest)).thenReturn(summaryBaseResponse);

        // Act
        List<PageOldProductInfoDTO> result = summaryProxy.getProductList(productListRequest);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetProductList_Exception() {
        // Arrange
        ProductListRequest productListRequest = new ProductListRequest();
        productListRequest.setUId("testUid");
        productListRequest.setStationId("testStationId");
        productListRequest.setCityNumber("testCityNumber");
        productListRequest.setApiVersion("testApiVersion");
        productListRequest.setAppClient(AppClientIdEnum.IOS);
        productListRequest.setBreakShelf(true);
        productListRequest.setMustShowVip(true);
        productListRequest.setLongitude("123.456");
        productListRequest.setLatitude("789.012");
        productListRequest.setPageProductIdDTOS(Lists.newArrayList(new PageProductIdDTO("testPageId", Arrays.asList("productId1", "productId2"))));
        productListRequest.setProductMergeType(ProductMergeSourceEnum.PRODUCT_DETAIL);

        when(summaryClient.pageProductList(productListRequest)).thenThrow(new RuntimeException("Test exception"));

        // Act
        List<PageOldProductInfoDTO> result = summaryProxy.getProductList(productListRequest);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetProductList_FallbackOn() {
        // Arrange
        ProductListRequest productListRequest = new ProductListRequest();
        productListRequest.setUId("testUid");
        productListRequest.setStationId("testStationId");
        productListRequest.setCityNumber("testCityNumber");
        productListRequest.setApiVersion("testApiVersion");
        productListRequest.setAppClient(AppClientIdEnum.IOS);
        productListRequest.setBreakShelf(true);
        productListRequest.setMustShowVip(true);
        productListRequest.setLongitude("123.456");
        productListRequest.setLatitude("789.012");
        productListRequest.setPageProductIdDTOS(Lists.newArrayList(new PageProductIdDTO("testPageId", Arrays.asList("productId1", "productId2"))));
        productListRequest.setProductMergeType(ProductMergeSourceEnum.PRODUCT_DETAIL);

        when(globalApolloConfig.getSummarySwitch()).thenReturn(Constans.ONE);

        // Act
        List<PageOldProductInfoDTO> result = summaryProxy.getProductList(productListRequest);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGenerateSummaryRequest() {
        // Arrange
        ChatContext chatContext = new ChatContext();
        chatContext.setUid("testUid");
        chatContext.setStationId("testStationId");
        chatContext.setCityNumber("testCityNumber");
        chatContext.setApiVersion("testApiVersion");
        chatContext.setAppClientId(AppClientIdEnum.IOS.getCodeInt());
        chatContext.setLongitude("123.456");
        chatContext.setLatitude("789.012");

        String pageId = "testPageId";
        List<String> productIds = Arrays.asList("productId1", "productId2");

        // Act
        ProductListRequest result = summaryProxy.generateSummaryRequest(chatContext, pageId, productIds);

        // Assert
        assertNotNull(result);
        assertEquals(chatContext.getUid(), result.getUId());
        assertEquals(chatContext.getStationId(), result.getStationId());
        assertEquals(chatContext.getCityNumber(), result.getCityNumber());
        assertEquals(chatContext.getApiVersion(), result.getApiVersion());
        assertEquals(AppClientIdEnum.IOS, result.getAppClient());
        assertTrue(result.isBreakShelf());
        assertTrue(result.isMustShowVip());
        assertEquals(chatContext.getLongitude(), result.getLongitude());
        assertEquals(chatContext.getLatitude(), result.getLatitude());
        assertEquals(1, result.getPageProductIdDTOS().size());
        assertEquals(pageId, result.getPageProductIdDTOS().get(0).getPageId());
        assertEquals(productIds, result.getPageProductIdDTOS().get(0).getProductIds());
        assertEquals(ProductMergeSourceEnum.PRODUCT_DETAIL, result.getProductMergeType());
    }

    @Test
    void testGetProductInfoCardByProductIds_Failure() {
        // Arrange
        ChatContext chatContext = new ChatContext();
        chatContext.setUid("testUid");
        chatContext.setStationId("testStationId");
        chatContext.setCityNumber("testCityNumber");
        chatContext.setApiVersion("testApiVersion");
        chatContext.setAppClientId(AppClientIdEnum.IOS.getCodeInt());
        chatContext.setLongitude("123.456");
        chatContext.setLatitude("789.012");

        String pageId = "testPageId";
        List<String> productIds = Arrays.asList("productId1", "productId2");

        ProductListRequest summaryReq = new ProductListRequest();
        summaryReq.setUId(chatContext.getUid());
        summaryReq.setStationId(chatContext.getStationId());
        summaryReq.setCityNumber(chatContext.getCityNumber());
        summaryReq.setApiVersion(chatContext.getApiVersion());
        summaryReq.setAppClient(AppClientIdEnum.IOS);
        summaryReq.setBreakShelf(true);
        summaryReq.setMustShowVip(true);
        summaryReq.setLongitude(chatContext.getLongitude());
        summaryReq.setLatitude(chatContext.getLatitude());
        summaryReq.setPageProductIdDTOS(Lists.newArrayList(new PageProductIdDTO(pageId, productIds)));
        summaryReq.setProductMergeType(ProductMergeSourceEnum.PRODUCT_DETAIL);

        when(summaryProxy.generateSummaryRequest(chatContext, pageId, productIds)).thenReturn(summaryReq);

        SummaryBaseResponse<List<PageOldProductInfoDTO>> summaryBaseResponse = new SummaryBaseResponse<>();
        summaryBaseResponse.setSuccess(false);

        when(summaryClient.pageProductList(summaryReq)).thenReturn(summaryBaseResponse);

        // Act
        List<ProductInfoVO> result = summaryProxy.getProductInfoCardByProductIds(chatContext, pageId, productIds);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
