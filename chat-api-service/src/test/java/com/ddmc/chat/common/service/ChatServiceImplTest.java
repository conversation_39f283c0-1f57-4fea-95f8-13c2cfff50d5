package com.ddmc.chat.common.service;

import static com.ddmc.chat.common.constant.CacheKeyConstants.getAliyunTokenCacheKey;
import static com.ddmc.chat.common.domain.Constants.MSG_SENDER_ASSISTANT;
import static com.ddmc.chat.common.domain.Constants.MSG_TYPE_TEXT;
import static com.ddmc.chat.common.domain.Constants.SETTING_KEY_AUDIO_AUTOPLAY;
import static com.ddmc.chat.common.domain.Constants.SETTING_VALUE_ON;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.antispam.dto.ImgCheckDTO;
import com.ddmc.antispam.dto.TextCheckDTO;
import com.ddmc.chat.common.domain.bo.ChatEntryConfigBO;
import com.ddmc.chat.common.domain.bo.EntryConfigBO;
import com.ddmc.chat.common.domain.bo.EntrySceneConfigBO;
import com.ddmc.chat.common.domain.dto.req.chat.CancelQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatHistoryReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.EntryReq;
import com.ddmc.chat.common.domain.dto.req.chat.SessionReq;
import com.ddmc.chat.common.domain.dto.req.chat.StartReq;
import com.ddmc.chat.common.domain.entity.FunctionButton;
import com.ddmc.chat.common.domain.entity.MessageContext;
import com.ddmc.chat.common.domain.entity.Session;
import com.ddmc.chat.common.domain.entity.SugWords;
import com.ddmc.chat.common.domain.vo.ChatEntryVO;
import com.ddmc.chat.common.domain.vo.DailyTipsVO;
import com.ddmc.chat.common.domain.vo.MessageBoxVO;
import com.ddmc.chat.common.domain.vo.MessageVO;
import com.ddmc.chat.common.domain.vo.ProductInfoVO;
import com.ddmc.chat.common.domain.vo.SessionStartVO;
import com.ddmc.chat.common.enums.EntrySceneEnum;
import com.ddmc.chat.common.enums.MessageTypeEnum;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.dao.UserSettingsDAO;
import com.ddmc.chat.common.infra.handler.MessageBoxHandler;
import com.ddmc.chat.common.infra.proxy.ABTestProxy;
import com.ddmc.chat.common.infra.proxy.AliyunProxy;
import com.ddmc.chat.common.infra.proxy.AntispamProxy;
import com.ddmc.chat.common.infra.proxy.DdfsProxy;
import com.ddmc.chat.common.infra.proxy.NlpAiProxy;
import com.ddmc.chat.common.infra.proxy.SummaryProxy;
import com.ddmc.chat.common.infra.proxy.dto.TextCheckReason;
import com.ddmc.chat.common.infra.utils.FeatureControlUtil;
import com.ddmc.chat.common.service.impl.ChatServiceImpl;
import com.ddmc.chat.common.infra.utils.AsyncUtils;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.chat.common.util.MsgIdGenerator;
import com.ddmc.guide.enhance.json.JsonUtil;
import com.ddmc.guide.enhance.redis.RedisClient;
import com.ddmc.utils.date.DateUtils;
import com.google.common.collect.Lists;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import java.util.function.Supplier;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


@RunWith(PowerMockRunner.class)
@PrepareForTest({ChatServiceImpl.class, MonitorUtil.class})
class ChatServiceImplTest {

    @Mock
    private NlpAiProxy nlpAiProxy;

    @Mock
    private SummaryProxy summaryProxy;

    @Mock
    private DdfsProxy ddfsProxy;

    @Mock
    private MessageBoxHandler messageBoxHandler;

    @Mock
    private AntispamProxy antispamProxy;

    @Mock
    private AliyunProxy aliyunProxy;

    @Mock
    private ABTestProxy abTestProxy;

    @Mock
    private UserSettingsDAO userSettingsDAO;

    @Mock
    private GlobalApolloConfig globalApolloConfig;

    @Mock
    private RedisClient transformersRedisClient;

    @Mock
    private AsyncUtils asyncUtils;

    @Mock
    private FeatureControlUtil featureControlUtil;

    @Spy
    @InjectMocks
    private ChatServiceImpl chatService;

    private final Integer historyMessagePageSize = 20;

    private String entryConfig = "{\n"
        + "    \"enable\": true,\n"
        + "    \"whiteStationIds\": [\n"
        + "        \"all\"\n"
        + "    ],\n"
        + "    \"blackUIds\": [],\n"
        + "    \"blackPIds\": [],\n"
        + "    \"whitePIds\": [\n"
        + "        \"631992cd40ee7a001f37f904\",\n"
        + "        \"6095058c37970841e4baf27f\"\n"
        + "    ],\n"
        + "    \"whiteCategoryIds\": [\n"
        + "        \"1\",\n"
        + "        \"103\",\n"
        + "        \"236\",\n"
        + "        \"421\",\n"
        + "        \"450\",\n"
        + "        \"643\",\n"
        + "        \"736\",\n"
        + "        \"751\",\n"
        + "        \"858\",\n"
        + "        \"879\",\n"
        + "        \"914\",\n"
        + "        \"1009\",\n"
        + "        \"1045\",\n"
        + "        \"1161\",\n"
        + "        \"1471\",\n"
        + "        \"1600\"\n"
        + "    ],\n"
        + "    \"text\": \"AI助手\",\n"
        + "    \"link\": \"https://activity.m.ddxq.mobi/#/ai/chat?is_nav_hide=true&from=detail\",\n"
        + "    \"versionRangeList\": [\n"
        + "        {\n"
        + "            \"minVersion\": \"\",\n"
        + "            \"maxVersion\": \"11.40.99\",\n"
        + "            \"value\": {\n"
        + "                \"text\": \"\",\n"
        + "                \"url\": \"https://activity.m.ddxq.mobi/#/ai/chat?is_nav_hide=true&from=detail\",\n"
        + "                \"enable\": \"true\",\n"
        + "                \"icon\": \"\"\n"
        + "            }\n"
        + "            \n"
        + "        },{\n"
        + "            \"minVersion\": \"11.41.00\",\n"
        + "            \"maxVersion\": \"\",\n"
        + "            \"value\": {\n"
        + "                \"text\": \"\",\n"
        + "                \"url\": \"https://new.activity.m.ddxq.mobi/#/ai/chat?is_nav_hide=true&from=detail\",\n"
        + "                \"enable\": \"true\",\n"
        + "                \"icon\": \"\"\n"
        + "            }\n"
        + "        }\n"
        + "    ]\n"
        + "}";

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(globalApolloConfig, "historyMessagePageSize", historyMessagePageSize);
    }

    @Test
    void testEntryWithValidRequest() {
        EntryReq entryReq = new EntryReq();
        entryReq.setUid("user123");
        entryReq.setStationId("station123");
        entryReq.setProductId("product123");
        entryReq.setAppVersion("");
        entryReq.setBackendCategoryPath("category1,category2");

        ChatEntryConfigBO chatEntryConfigBO = JsonUtil.toObject(entryConfig, ChatEntryConfigBO.class);

        when(globalApolloConfig.getChatEntryConfigBO()).thenReturn(chatEntryConfigBO);

        ChatEntryVO expected = new ChatEntryVO(false, null, null);
        ChatEntryVO actual = chatService.entry(entryReq);

        assertEquals(expected, actual);
        verify(globalApolloConfig, times(2)).getChatEntryConfigBO();
    }

    @Test
    void testEntryWithInvalidRequest() {
        EntryReq entryReq = new EntryReq();
        entryReq.setUid("");
        entryReq.setStationId("station123");
        entryReq.setProductId("product123");

        ChatEntryVO expected = new ChatEntryVO(false, null, null);
        ChatEntryVO actual = chatService.entry(entryReq);

        assertEquals(expected, actual);
    }

    @Test
    void testStartWithValidRequest() throws Exception {
        StartReq startReq = new StartReq();
        startReq.setUid("user_id");
        startReq.setProductId("product_id");
        startReq.setFrom("dxd_new");
        startReq.setScene("home_page");

        when(globalApolloConfig.getHistoryMessagePageSize()).thenReturn(10);
        when(globalApolloConfig.getAvatar()).thenReturn("avatar_url");
        when(globalApolloConfig.getIsiDegradeSwitch()).thenReturn(0);
        when(globalApolloConfig.getAliyunAppKey()).thenReturn("app_key");
        when(globalApolloConfig.getAliyunAppIsiAsrStreamUrl()).thenReturn("asr_url");
        when(globalApolloConfig.getAliyunAppIsiTtsStreamUrl()).thenReturn("tts_url");
        when(globalApolloConfig.getAliyunAppTtsTone()).thenReturn("tone");
        when(globalApolloConfig.getAliyunAppAccessKeyId()).thenReturn("access_key_id");
        when(globalApolloConfig.getAliyunAppAccessKeySecret()).thenReturn("access_key_secret");
        when(globalApolloConfig.getAliyunAppTokenDomain()).thenReturn("token_domain");
        when(globalApolloConfig.getFunctionButtonList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getSugWordsList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getThreadTimeout()).thenReturn(1000);

        when(asyncUtils.supplyAsync(any(), anyString())).thenAnswer(invocation -> {
            Supplier<?> task = (Supplier<?>) invocation.getArguments()[0];
            Object result = task.get();
            return CompletableFuture.completedFuture(result);
        });

        when(messageBoxHandler.createSessionIdAndRecord()).thenReturn("session_id");
        when(messageBoxHandler.history(any())).thenReturn(new MessageBoxVO());
        when(nlpAiProxy.getFirstChatStartMessage(any(), any())).thenReturn(Triple.of("Welcome to chat!", Collections.emptyList(), Collections.emptyList()));
        when(summaryProxy.getProductInfoCardByProductIds(any(), anyString(), anyList())).thenReturn(Collections.emptyList());
        when(nlpAiProxy.queryDailyTips(any())).thenReturn(new DailyTipsVO());
        when(aliyunProxy.generateToken(anyString(), anyString(), anyString())).thenReturn(Pair.of("token", System.currentTimeMillis() / 1000 + 3600 * 36));

        SessionStartVO result = chatService.start(startReq);

        assertNotNull(result);
        assertNotNull(result.getSession());
        assertEquals("session_id", result.getSession().getSessionId());
        assertTrue(result.isAudioEnabled());
        assertNotNull(result.getIsiConfig());
        assertEquals("Welcome to chat!", result.getWelcome().getContent());
        assertTrue(CollectionUtils.isEmpty(result.getIntentGuideWords()));
        assertTrue(CollectionUtils.isEmpty(result.getQuickQuestionList()));
        assertFalse(result.isHasHistory());
        assertNotNull(result.getDailyTips());
        assertTrue(CollectionUtils.isEmpty(result.getFunctionButtonList()));
        assertTrue(CollectionUtils.isEmpty(result.getSugList()));
    }

    @Test
    void testStartWithNullRequest() {
        assertThrows(RuntimeException.class, () -> chatService.start(null));
    }

    @Test
    void testStartWithBlankUid() {
        StartReq startReq = new StartReq();
        startReq.setUid("");
        assertThrows(RuntimeException.class, () -> chatService.start(startReq));
    }

    @Test
    void testStartWithProductInfo() throws Exception {

        StartReq startReq = new StartReq();
        startReq.setUid("user_id");
        startReq.setProductId("product_id");
        startReq.setFrom("dxd_new");
        startReq.setScene("home_page");

        when(globalApolloConfig.getHistoryMessagePageSize()).thenReturn(10);
        when(globalApolloConfig.getAvatar()).thenReturn("avatar_url");
        when(globalApolloConfig.getIsiDegradeSwitch()).thenReturn(0);
        when(globalApolloConfig.getAliyunAppKey()).thenReturn("app_key");
        when(globalApolloConfig.getAliyunAppIsiAsrStreamUrl()).thenReturn("asr_url");
        when(globalApolloConfig.getAliyunAppIsiTtsStreamUrl()).thenReturn("tts_url");
        when(globalApolloConfig.getAliyunAppTtsTone()).thenReturn("tone");
        when(globalApolloConfig.getAliyunAppAccessKeyId()).thenReturn("access_key_id");
        when(globalApolloConfig.getAliyunAppAccessKeySecret()).thenReturn("access_key_secret");
        when(globalApolloConfig.getAliyunAppTokenDomain()).thenReturn("token_domain");
        when(globalApolloConfig.getFunctionButtonList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getSugWordsList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getThreadTimeout()).thenReturn(1000);
        when(asyncUtils.supplyAsync(any(), anyString())).thenAnswer(invocation -> {
            Supplier<?> task = (Supplier<?>) invocation.getArguments()[0];
            Object result = task.get();
            return CompletableFuture.completedFuture(result);
        });
        when(messageBoxHandler.createSessionIdAndRecord()).thenReturn("session_id");
        when(messageBoxHandler.history(any())).thenReturn(new MessageBoxVO());
        when(nlpAiProxy.getFirstChatStartMessage(any(), any())).thenReturn(Triple.of("Welcome to chat!", Collections.emptyList(), Collections.emptyList()));
        when(summaryProxy.getProductInfoCardByProductIds(any(), anyString(), anyList())).thenReturn(Collections.singletonList(new ProductInfoVO()));
        when(nlpAiProxy.queryDailyTips(any())).thenReturn(new DailyTipsVO());
        when(aliyunProxy.generateToken(anyString(), anyString(), anyString())).thenReturn(Pair.of("token", System.currentTimeMillis() / 1000 + 3600 * 36));

        SessionStartVO result = chatService.start(startReq);

        assertNotNull(result);
        assertNotNull(result.getProductInfoVO());
        assertTrue(result.getProductInfoVO().isStockOutReservedNew());
    }

    @Test
    void testStartWithoutProductId() throws Exception {

        StartReq startReq = new StartReq();
        startReq.setUid("user_id");
        startReq.setFrom("dxd_new");
        startReq.setScene("home_page");

        when(globalApolloConfig.getHistoryMessagePageSize()).thenReturn(10);
        when(globalApolloConfig.getAvatar()).thenReturn("avatar_url");
        when(globalApolloConfig.getIsiDegradeSwitch()).thenReturn(0);
        when(globalApolloConfig.getAliyunAppKey()).thenReturn("app_key");
        when(globalApolloConfig.getAliyunAppIsiAsrStreamUrl()).thenReturn("asr_url");
        when(globalApolloConfig.getAliyunAppIsiTtsStreamUrl()).thenReturn("tts_url");
        when(globalApolloConfig.getAliyunAppTtsTone()).thenReturn("tone");
        when(globalApolloConfig.getAliyunAppAccessKeyId()).thenReturn("access_key_id");
        when(globalApolloConfig.getAliyunAppAccessKeySecret()).thenReturn("access_key_secret");
        when(globalApolloConfig.getAliyunAppTokenDomain()).thenReturn("token_domain");
        when(globalApolloConfig.getFunctionButtonList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getSugWordsList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getThreadTimeout()).thenReturn(1000);
        when(asyncUtils.supplyAsync(any(), anyString())).thenAnswer(invocation -> {
            Supplier<?> task = (Supplier<?>) invocation.getArguments()[0];
            Object result = task.get();
            return CompletableFuture.completedFuture(result);
        });
        when(messageBoxHandler.createSessionIdAndRecord()).thenReturn("session_id");
        when(messageBoxHandler.history(any())).thenReturn(new MessageBoxVO());
        when(nlpAiProxy.getFirstChatStartMessage(any(), any())).thenReturn(Triple.of("Welcome to chat!", Collections.emptyList(), Collections.emptyList()));
        when(summaryProxy.getProductInfoCardByProductIds(any(), anyString(), anyList())).thenReturn(Collections.singletonList(new ProductInfoVO()));
        when(nlpAiProxy.queryDailyTips(any())).thenReturn(new DailyTipsVO());
        when(aliyunProxy.generateToken(anyString(), anyString(), anyString())).thenReturn(Pair.of("token", System.currentTimeMillis() / 1000 + 3600 * 36));

        SessionStartVO result = chatService.start(startReq);

        assertNotNull(result);
        assertNull(result.getProductInfoVO());
    }

    @Test
    void testStartWithDegradeAudio() {

        StartReq startReq = new StartReq();
        startReq.setUid("user_id");
        startReq.setProductId("product_id");
        startReq.setFrom("dxd_new");
        startReq.setScene("home_page");

        when(globalApolloConfig.getHistoryMessagePageSize()).thenReturn(10);
        when(globalApolloConfig.getAvatar()).thenReturn("avatar_url");
        when(globalApolloConfig.getIsiDegradeSwitch()).thenReturn(1);
        when(globalApolloConfig.getFunctionButtonList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getSugWordsList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getThreadTimeout()).thenReturn(1000);
        when(asyncUtils.supplyAsync(any(), anyString())).thenAnswer(invocation -> {
            Supplier<?> task = (Supplier<?>) invocation.getArguments()[0];
            Object result = task.get();
            return CompletableFuture.completedFuture(result);
        });
        when(messageBoxHandler.createSessionIdAndRecord()).thenReturn("session_id");
        when(messageBoxHandler.history(any())).thenReturn(new MessageBoxVO());
        when(nlpAiProxy.getFirstChatStartMessage(any(), any())).thenReturn(Triple.of("Welcome to chat!", Collections.emptyList(), Collections.emptyList()));
        when(summaryProxy.getProductInfoCardByProductIds(any(), anyString(), anyList())).thenReturn(Collections.singletonList(new ProductInfoVO()));
        when(nlpAiProxy.queryDailyTips(any())).thenReturn(new DailyTipsVO());

        SessionStartVO result = chatService.start(startReq);

        assertNotNull(result);
        assertFalse(result.isAudioEnabled());
        assertNull(result.getIsiConfig());
    }

    @Test
    void testStartWithHistory() {
        MessageBoxVO history = new MessageBoxVO();
        history.setMessageList(Collections.singletonList(new MessageVO()));
        when(messageBoxHandler.history(any())).thenReturn(history);

        StartReq startReq = new StartReq();
        startReq.setUid("user_id");
        startReq.setProductId("product_id");
        startReq.setFrom("dxd_new");
        startReq.setScene("home_page");

        when(globalApolloConfig.getHistoryMessagePageSize()).thenReturn(10);
        when(globalApolloConfig.getAvatar()).thenReturn("avatar_url");
        when(globalApolloConfig.getIsiDegradeSwitch()).thenReturn(0);
        when(globalApolloConfig.getAliyunAppKey()).thenReturn("app_key");
        when(globalApolloConfig.getAliyunAppIsiAsrStreamUrl()).thenReturn("asr_url");
        when(globalApolloConfig.getAliyunAppIsiTtsStreamUrl()).thenReturn("tts_url");
        when(globalApolloConfig.getAliyunAppTtsTone()).thenReturn("tone");
        when(globalApolloConfig.getAliyunAppAccessKeyId()).thenReturn("access_key_id");
        when(globalApolloConfig.getAliyunAppAccessKeySecret()).thenReturn("access_key_secret");
        when(globalApolloConfig.getAliyunAppTokenDomain()).thenReturn("token_domain");
        when(globalApolloConfig.getFunctionButtonList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getSugWordsList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getThreadTimeout()).thenReturn(1000);
        when(asyncUtils.supplyAsync(any(), anyString())).thenAnswer(invocation -> {
            Supplier<?> task = (Supplier<?>) invocation.getArguments()[0];
            Object result = task.get();
            return CompletableFuture.completedFuture(result);
        });
        when(messageBoxHandler.createSessionIdAndRecord()).thenReturn("session_id");
        when(nlpAiProxy.getFirstChatStartMessage(any(), any())).thenReturn(Triple.of("Welcome to chat!", Collections.emptyList(), Collections.emptyList()));
        when(summaryProxy.getProductInfoCardByProductIds(any(), anyString(), anyList())).thenReturn(Collections.singletonList(new ProductInfoVO()));
        when(nlpAiProxy.queryDailyTips(any())).thenReturn(new DailyTipsVO());
        when(aliyunProxy.generateToken(anyString(), anyString(), anyString())).thenReturn(Pair.of("token", System.currentTimeMillis() / 1000 + 3600 * 36));

        SessionStartVO result = chatService.start(startReq);

        assertNotNull(result);
        assertTrue(result.isHasHistory());
    }

    @Test
    void testStartWithDailyTips() throws Exception {

        StartReq startReq = new StartReq();
        startReq.setUid("user_id");
        startReq.setProductId("product_id");
        startReq.setFrom("dxd_new");
        startReq.setScene("home_page");

        when(globalApolloConfig.getHistoryMessagePageSize()).thenReturn(10);
        when(globalApolloConfig.getAvatar()).thenReturn("avatar_url");
        when(globalApolloConfig.getIsiDegradeSwitch()).thenReturn(0);
        when(globalApolloConfig.getAliyunAppKey()).thenReturn("app_key");
        when(globalApolloConfig.getAliyunAppIsiAsrStreamUrl()).thenReturn("asr_url");
        when(globalApolloConfig.getAliyunAppIsiTtsStreamUrl()).thenReturn("tts_url");
        when(globalApolloConfig.getAliyunAppTtsTone()).thenReturn("tone");
        when(globalApolloConfig.getAliyunAppAccessKeyId()).thenReturn("access_key_id");
        when(globalApolloConfig.getAliyunAppAccessKeySecret()).thenReturn("access_key_secret");
        when(globalApolloConfig.getAliyunAppTokenDomain()).thenReturn("token_domain");
        when(globalApolloConfig.getFunctionButtonList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getSugWordsList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getThreadTimeout()).thenReturn(1000);

        when(asyncUtils.supplyAsync(any(), anyString())).thenAnswer(invocation -> {
            Supplier<?> task = (Supplier<?>) invocation.getArguments()[0];
            Object result = task.get();
            return CompletableFuture.completedFuture(result);
        });

        when(messageBoxHandler.createSessionIdAndRecord()).thenReturn("session_id");
        when(messageBoxHandler.history(any())).thenReturn(new MessageBoxVO());
        when(nlpAiProxy.getFirstChatStartMessage(any(), any())).thenReturn(Triple.of("Welcome to chat!", Collections.emptyList(), Collections.emptyList()));
        when(summaryProxy.getProductInfoCardByProductIds(any(), anyString(), anyList())).thenReturn(Collections.emptyList());
        when(nlpAiProxy.queryDailyTips(any())).thenReturn(new DailyTipsVO());
        when(aliyunProxy.generateToken(anyString(), anyString(), anyString())).thenReturn(Pair.of("token", System.currentTimeMillis() / 1000 + 3600 * 36));

        SessionStartVO result = chatService.start(startReq);

        assertNotNull(result);
        assertNotNull(result.getDailyTips());
    }

    @Test
    void testStartWithoutDailyTips() throws Exception {

        StartReq startReq = new StartReq();
        startReq.setUid("user_id");
        startReq.setProductId("product_id");
        startReq.setFrom("dxd_new");
        startReq.setScene("home_page");

        when(globalApolloConfig.getHistoryMessagePageSize()).thenReturn(10);
        when(globalApolloConfig.getAvatar()).thenReturn("avatar_url");
        when(globalApolloConfig.getIsiDegradeSwitch()).thenReturn(0);
        when(globalApolloConfig.getAliyunAppKey()).thenReturn("app_key");
        when(globalApolloConfig.getAliyunAppIsiAsrStreamUrl()).thenReturn("asr_url");
        when(globalApolloConfig.getAliyunAppIsiTtsStreamUrl()).thenReturn("tts_url");
        when(globalApolloConfig.getAliyunAppTtsTone()).thenReturn("tone");
        when(globalApolloConfig.getAliyunAppAccessKeyId()).thenReturn("access_key_id");
        when(globalApolloConfig.getAliyunAppAccessKeySecret()).thenReturn("access_key_secret");
        when(globalApolloConfig.getAliyunAppTokenDomain()).thenReturn("token_domain");
        when(globalApolloConfig.getFunctionButtonList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getSugWordsList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getThreadTimeout()).thenReturn(1000);

        when(asyncUtils.supplyAsync(any(), anyString())).thenAnswer(invocation -> {
            Supplier<?> task = (Supplier<?>) invocation.getArguments()[0];
            Object result = task.get();
            return CompletableFuture.completedFuture(result);
        });

        when(messageBoxHandler.createSessionIdAndRecord()).thenReturn("session_id");
        when(messageBoxHandler.history(any())).thenReturn(new MessageBoxVO());
        when(nlpAiProxy.getFirstChatStartMessage(any(), any())).thenReturn(Triple.of("Welcome to chat!", Collections.emptyList(), Collections.emptyList()));
        when(summaryProxy.getProductInfoCardByProductIds(any(), anyString(), anyList())).thenReturn(Collections.emptyList());
        when(nlpAiProxy.queryDailyTips(any())).thenReturn(null);
        when(aliyunProxy.generateToken(anyString(), anyString(), anyString())).thenReturn(Pair.of("token", System.currentTimeMillis() / 1000 + 3600 * 36));

        SessionStartVO result = chatService.start(startReq);

        assertNotNull(result);
        assertNull(result.getDailyTips());
    }

    @Test
    void testStartWithTimeout() throws Exception {
        when(nlpAiProxy.getFirstChatStartMessage(any(), any())).thenAnswer(invocation -> {
            Thread.sleep(2000);
            return Triple.of("Welcome to chat!", Collections.emptyList(), Collections.emptyList());
        });

        StartReq startReq = new StartReq();
        startReq.setUid("user_id");
        startReq.setProductId("product_id");

        when(globalApolloConfig.getHistoryMessagePageSize()).thenReturn(10);
        when(globalApolloConfig.getAvatar()).thenReturn("avatar_url");
        when(globalApolloConfig.getIsiDegradeSwitch()).thenReturn(0);
        when(globalApolloConfig.getAliyunAppKey()).thenReturn("app_key");
        when(globalApolloConfig.getAliyunAppIsiAsrStreamUrl()).thenReturn("asr_url");
        when(globalApolloConfig.getAliyunAppIsiTtsStreamUrl()).thenReturn("tts_url");
        when(globalApolloConfig.getAliyunAppTtsTone()).thenReturn("tone");
        when(globalApolloConfig.getAliyunAppAccessKeyId()).thenReturn("access_key_id");
        when(globalApolloConfig.getAliyunAppAccessKeySecret()).thenReturn("access_key_secret");
        when(globalApolloConfig.getAliyunAppTokenDomain()).thenReturn("token_domain");
        when(globalApolloConfig.getFunctionButtonList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getSugWordsList()).thenReturn(Collections.emptyList());
        when(globalApolloConfig.getThreadTimeout()).thenReturn(1000);

        when(asyncUtils.supplyAsync(any(), anyString())).thenAnswer(invocation -> {
            Supplier<?> task = (Supplier<?>) invocation.getArguments()[0];
            Object result = task.get();
            return CompletableFuture.completedFuture(result);
        });

        when(messageBoxHandler.createSessionIdAndRecord()).thenReturn("session_id");
        when(messageBoxHandler.history(any())).thenReturn(new MessageBoxVO());
        when(summaryProxy.getProductInfoCardByProductIds(any(), anyString(), anyList())).thenReturn(Collections.emptyList());
        when(aliyunProxy.generateToken(anyString(), anyString(), anyString())).thenReturn(Pair.of("token", System.currentTimeMillis() / 1000 + 3600 * 36));

        SessionStartVO result = chatService.start(startReq);

        assertNotNull(result);
        assertNotNull(result.getWelcome());
        assertEquals("Welcome to chat!", result.getWelcome().getContent());
    }

    @Test
    void testStartWithInvalidRequest() {
        StartReq startReq = new StartReq();
        startReq.setUid("");

        assertThrows(RuntimeException.class, () -> chatService.start(startReq));
    }

    @Test
    void testCompletionsWithValidRequest() throws Exception {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setQuestion("Hello");
        chatQuestionReq.setProductId("product123");
        chatQuestionReq.setFrontCategoryName("categoryName");
        chatQuestionReq.setFrontCategoryId("categoryId");
        chatQuestionReq.setSearchKeyword("keyword");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setStream(true);

        MessageContext messageContext = MessageContext.builder()
            .uid("user123")
            .build();

        List<TextCheckReason> checkReasons = Collections.emptyList();
        MessageVO userMessageVO = new MessageVO();
        userMessageVO.setMessageId(MsgIdGenerator.genId());
        userMessageVO.setType(MSG_TYPE_TEXT);
        userMessageVO.setRole(MSG_SENDER_ASSISTANT);
        userMessageVO.setContent("Hello");
        userMessageVO.setSessionId("session123");
        userMessageVO.setTime(DateUtils.getCurTimestamp());

        SseEmitter sseEmitter = new SseEmitter();

        // Mock getSegmentMessageVO to return a non-null MessageVO
        MessageVO segmentMessageVO = new MessageVO();
        segmentMessageVO.setMessageId(MsgIdGenerator.genId());
        segmentMessageVO.setType(MSG_TYPE_TEXT);
        segmentMessageVO.setRole(MSG_SENDER_ASSISTANT);
        segmentMessageVO.setContent("Segment Message");
        segmentMessageVO.setSessionId("session123");
        segmentMessageVO.setTime(DateUtils.getCurTimestamp());

        when(messageBoxHandler.getUserMessageVO(checkReasons, chatQuestionReq.getQuestion(), messageContext)).thenReturn(userMessageVO);
        when(messageBoxHandler.saveDirectMessage(any(), any())).thenReturn(true);
        when(nlpAiProxy.sendRequest(chatQuestionReq, sseEmitter, messageContext)).thenReturn(sseEmitter);

        SseEmitter actual = chatService.completions(chatQuestionReq, sseEmitter);

        assertEquals(sseEmitter, actual);
        verify(messageBoxHandler, times(1)).checkSession(chatQuestionReq.getSessionId(), messageContext);
        verify(messageBoxHandler, times(1)).getUserMessageVO(checkReasons, chatQuestionReq.getQuestion(), messageContext);
        verify(messageBoxHandler, times(1)).saveDirectMessage(eq(userMessageVO), eq(chatQuestionReq));
        verify(nlpAiProxy, times(1)).sendRequest(chatQuestionReq, sseEmitter, messageContext);
    }

    @Test
    void testCompletionsWithValidRequest_HomePage() throws Exception {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setQuestion("Hello");
        chatQuestionReq.setProductId("product123");
        chatQuestionReq.setFrontCategoryName("categoryName");
        chatQuestionReq.setFrontCategoryId("categoryId");
        chatQuestionReq.setSearchKeyword("keyword");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setStream(true);
        chatQuestionReq.setFrom("dxd_new");
        chatQuestionReq.setScene("home_page");

        MessageContext messageContext = MessageContext.builder()
            .uid("user123")
            .build();

        List<TextCheckReason> checkReasons = Collections.emptyList();
        MessageVO userMessageVO = new MessageVO();
        userMessageVO.setMessageId(MsgIdGenerator.genId());
        userMessageVO.setType(MSG_TYPE_TEXT);
        userMessageVO.setRole(MSG_SENDER_ASSISTANT);
        userMessageVO.setContent("Hello");
        userMessageVO.setSessionId("session123");
        userMessageVO.setTime(DateUtils.getCurTimestamp());

        SseEmitter sseEmitter = new SseEmitter();

        // Mock getSegmentMessageVO to return a non-null MessageVO
        MessageVO segmentMessageVO = new MessageVO();
        segmentMessageVO.setMessageId(MsgIdGenerator.genId());
        segmentMessageVO.setType(MSG_TYPE_TEXT);
        segmentMessageVO.setRole(MSG_SENDER_ASSISTANT);
        segmentMessageVO.setContent("Segment Message");
        segmentMessageVO.setSessionId("session123");
        segmentMessageVO.setTime(DateUtils.getCurTimestamp());

        when(messageBoxHandler.getUserMessageVO(checkReasons, chatQuestionReq.getQuestion(), messageContext)).thenReturn(userMessageVO);
        when(messageBoxHandler.saveDirectMessage(any(), any())).thenReturn(true);
        when(nlpAiProxy.sendRequestV2(chatQuestionReq, sseEmitter, messageContext)).thenReturn(sseEmitter);
        when(nlpAiProxy.sendRequest(chatQuestionReq, sseEmitter, messageContext)).thenReturn(sseEmitter);

        SseEmitter actual = chatService.completions(chatQuestionReq, sseEmitter);

        assertEquals(sseEmitter, actual);
        verify(messageBoxHandler, times(1)).checkSession(chatQuestionReq.getSessionId(), messageContext);
        verify(messageBoxHandler, times(1)).getUserMessageVO(checkReasons, chatQuestionReq.getQuestion(), messageContext);
        verify(messageBoxHandler, times(1)).saveDirectMessage(eq(userMessageVO), eq(chatQuestionReq));
    }

    @Test
    void testCompletionsWithValidRequest_ImageUrl() throws Exception {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setQuestion("Hello");
        chatQuestionReq.setProductId("product123");
        chatQuestionReq.setFrontCategoryName("categoryName");
        chatQuestionReq.setFrontCategoryId("categoryId");
        chatQuestionReq.setSearchKeyword("keyword");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setStream(true);
        chatQuestionReq.setImageUrls(Collections.singletonList("https://example.com/image.jpg"));

        MessageContext messageContext = MessageContext.builder()
            .uid("user123")
            .build();

        List<TextCheckReason> checkReasons = Collections.emptyList();
        MessageVO userMessageVO = new MessageVO();
        userMessageVO.setMessageId(MsgIdGenerator.genId());
        userMessageVO.setType(MSG_TYPE_TEXT);
        userMessageVO.setRole(MSG_SENDER_ASSISTANT);
        userMessageVO.setContent("Hello");
        userMessageVO.setSessionId("session123");
        userMessageVO.setTime(DateUtils.getCurTimestamp());

        SseEmitter sseEmitter = new SseEmitter();

        // Mock getSegmentMessageVO to return a non-null MessageVO
        MessageVO segmentMessageVO = new MessageVO();
        segmentMessageVO.setMessageId(MsgIdGenerator.genId());
        segmentMessageVO.setType(MSG_TYPE_TEXT);
        segmentMessageVO.setRole(MSG_SENDER_ASSISTANT);
        segmentMessageVO.setContent("Segment Message");
        segmentMessageVO.setSessionId("session123");
        segmentMessageVO.setTime(DateUtils.getCurTimestamp());

        when(messageBoxHandler.getUserMessageVO(checkReasons, chatQuestionReq.getQuestion(), messageContext)).thenReturn(userMessageVO);
        when(messageBoxHandler.saveDirectMessage(any(), any())).thenReturn(true);
        when(nlpAiProxy.sendRequestV2(chatQuestionReq, sseEmitter, messageContext)).thenReturn(sseEmitter);
        when(nlpAiProxy.sendRequest(chatQuestionReq, sseEmitter, messageContext)).thenReturn(sseEmitter);

        SseEmitter actual = chatService.completions(chatQuestionReq, sseEmitter);

        assertEquals(sseEmitter, actual);
        verify(messageBoxHandler, times(1)).checkSession(chatQuestionReq.getSessionId(), messageContext);
        verify(messageBoxHandler, times(1)).getUserMessageVO(checkReasons, chatQuestionReq.getQuestion(), messageContext);
        verify(messageBoxHandler, times(1)).saveDirectMessage(eq(userMessageVO), eq(chatQuestionReq));
    }

    @Test
    void testCancelQuestion() {
        CancelQuestionReq cancelQuestionReq = new CancelQuestionReq();
        cancelQuestionReq.setMessageId(123456L);

        when(nlpAiProxy.stopStream("123456")).thenReturn(true);

        Boolean actual = chatService.cancelQuestion(cancelQuestionReq);

        assertTrue(actual);
        verify(nlpAiProxy, times(1)).stopStream("123456");
    }

    @Test
    void testSessionEnd() {
        SessionReq sessionReq = new SessionReq();
        sessionReq.setSessionId("session123");

        when(messageBoxHandler.removeSession("session123")).thenReturn(true);

        Boolean actual = chatService.sessionEnd(sessionReq);

        assertTrue(actual);
        verify(messageBoxHandler, times(1)).removeSession("session123");
    }

    @Test
    void testSessionEndWithInvalidRequest() {
        SessionReq sessionReq = new SessionReq();
        sessionReq.setSessionId(null);

        assertThrows(RuntimeException.class, () -> chatService.sessionEnd(sessionReq));
    }

    @Test
    void testHistoryWithValidRequest() {
        ChatHistoryReq chatHistoryReq = new ChatHistoryReq();
        chatHistoryReq.setUid("user123");
        chatHistoryReq.setLimit(10);
        chatHistoryReq.setLastMessageId(123456L);

        MessageBoxVO expected = new MessageBoxVO();
        expected.setMessageList(Lists.newArrayList(new MessageVO()));

        when(messageBoxHandler.history(chatHistoryReq)).thenReturn(expected);

        MessageBoxVO actual = chatService.history(chatHistoryReq);

        assertEquals(expected, actual);
        verify(messageBoxHandler, times(1)).history(chatHistoryReq);
    }

    @Test
    void testHistoryWithInvalidRequest() {
        ChatHistoryReq chatHistoryReq = new ChatHistoryReq();
        chatHistoryReq.setUid("");

        assertThrows(RuntimeException.class, () -> chatService.history(chatHistoryReq));
    }

    @Test
    void testSyncUnCommitMessage() {
        when(messageBoxHandler.syncUnCommitMessage()).thenReturn(1);

        Integer actual = chatService.syncUnCommitMessage();

        assertEquals(Integer.valueOf(1), actual);
        verify(messageBoxHandler, times(1)).syncUnCommitMessage();
    }

    @Test
    void testToggleSettingsWithValidRequest() {
        String uid = "user123";
        Integer autoAudioPlay = SETTING_VALUE_ON;

        when(userSettingsDAO.saveOrUpdateUserSettings(uid, Map.of(SETTING_KEY_AUDIO_AUTOPLAY, autoAudioPlay))).thenReturn(true);

        Boolean actual = chatService.toggleSettings(uid, autoAudioPlay);

        assertTrue(actual);
        verify(userSettingsDAO, times(1)).saveOrUpdateUserSettings(uid, Map.of(SETTING_KEY_AUDIO_AUTOPLAY, autoAudioPlay));
    }

    @Test
    void testToggleSettingsWithInvalidRequest() {
        String uid = "user123";
        Integer autoAudioPlay = 3;

        Boolean actual = chatService.toggleSettings(uid, autoAudioPlay);

        assertNull(actual);
        verify(userSettingsDAO, times(0)).saveOrUpdateUserSettings(anyString(), anyMap());
    }

    @Test
    void testQuerySettingsWithValidRequest() {
        String uid = "user123";
        com.ddmc.chat.common.mapper.DO.UserSettingsDO userSettingsDO = new com.ddmc.chat.common.mapper.DO.UserSettingsDO();
        userSettingsDO.setAudioAutoPlay(SETTING_VALUE_ON);

        when(userSettingsDAO.selectUserSettingsByOwner(uid)).thenReturn(userSettingsDO);

        Map<String, Integer> actual = chatService.querySettings(uid);

        assertEquals(Map.of(SETTING_KEY_AUDIO_AUTOPLAY, SETTING_VALUE_ON), actual);
        verify(userSettingsDAO, times(1)).selectUserSettingsByOwner(uid);
    }

    @Test
    void testQuerySettingsWithInvalidRequest() {
        String uid = "";

        Map<String, Integer> actual = chatService.querySettings(uid);

        assertEquals(Collections.emptyMap(), actual);
        verify(userSettingsDAO, times(0)).selectUserSettingsByOwner(anyString());
    }

    @Test
    void testUploadFile() {
        MultipartFile file = new MockMultipartFile("file", "test.txt", "text/plain", "test content".getBytes());
        String messageId = "123456";

        when(ddfsProxy.uploadUserAudioFile(file, messageId)).thenReturn("fileKey");

        String actual = chatService.uploadFile(file, messageId);

        assertEquals("fileKey", actual);
        verify(ddfsProxy, times(1)).uploadUserAudioFile(file, messageId);
    }

    @Test
    void testRecord() {
        String uid = "user123";
        Long messageId = 123456L;
        String fileKey = "fileKey";

        when(messageBoxHandler.updateMessageAudioFileKey(uid, messageId, fileKey)).thenReturn(true);

        Boolean actual = chatService.record(uid, messageId, fileKey);

        assertTrue(actual);
        verify(messageBoxHandler, times(1)).updateMessageAudioFileKey(uid, messageId, fileKey);
    }

    @Test
    void testGetTokenWithExistingValidToken() {
        String token = "aliyun_token";
        long expireTime = System.currentTimeMillis() / 1000 + 3600; // 1 hour from now

        when(transformersRedisClient.getStr(getAliyunTokenCacheKey())).thenReturn(token + "," + expireTime);
        when(aliyunProxy.generateToken(globalApolloConfig.getAliyunAppAccessKeyId(),
            globalApolloConfig.getAliyunAppAccessKeySecret(),
            globalApolloConfig.getAliyunAppTokenDomain()))
            .thenReturn(Pair.of("aliyun_token", expireTime)); // 2 hours from now

        Pair<String, Long> actual = chatService.getToken(false);

        assertEquals(Pair.of(token, expireTime), actual);
        verify(transformersRedisClient, times(1)).getStr(getAliyunTokenCacheKey());
        verify(aliyunProxy, times(0)).generateToken(anyString(), anyString(), anyString());
    }

    @Test
    void testGetTokenWithExistingExpiredToken() {
        String token = "existingToken";
        long expireTime = System.currentTimeMillis() / 1000 - 3600; // 1 hour ago

        when(transformersRedisClient.getStr(getAliyunTokenCacheKey())).thenReturn(token + "," + expireTime);
        when(aliyunProxy.generateToken(globalApolloConfig.getAliyunAppAccessKeyId(),
            globalApolloConfig.getAliyunAppAccessKeySecret(),
            globalApolloConfig.getAliyunAppTokenDomain()))
            .thenReturn(Pair.of("newToken", expireTime + 7200)); // 2 hours from now

        Pair<String, Long> actual = chatService.getToken(false);

        assertEquals(Pair.of("newToken", expireTime + 7200), actual);
        verify(transformersRedisClient, times(1)).getStr(getAliyunTokenCacheKey());
        verify(aliyunProxy, times(1)).generateToken(globalApolloConfig.getAliyunAppAccessKeyId(),
            globalApolloConfig.getAliyunAppAccessKeySecret(),
            globalApolloConfig.getAliyunAppTokenDomain());
        //verify(transformersRedisClient, times(1)).setStr(getAliyunTokenCacheKey(), "newToken," + (expireTime + 7200), Duration.ofSeconds(7170));
    }

    @Test
    void testGetTokenWithNoExistingToken() {
        long expireTime = System.currentTimeMillis() / 1000 + 7200; // 2 hours from now

        when(transformersRedisClient.getStr(getAliyunTokenCacheKey())).thenReturn(null);
        when(aliyunProxy.generateToken(globalApolloConfig.getAliyunAppAccessKeyId(),
            globalApolloConfig.getAliyunAppAccessKeySecret(),
            globalApolloConfig.getAliyunAppTokenDomain()))
            .thenReturn(Pair.of("newToken", expireTime));

        Pair<String, Long> actual = chatService.getToken(false);

        assertEquals(Pair.of("newToken", expireTime), actual);

        verify(transformersRedisClient, times(1)).getStr(getAliyunTokenCacheKey());
        verify(aliyunProxy, times(1)).generateToken(globalApolloConfig.getAliyunAppAccessKeyId(),
            globalApolloConfig.getAliyunAppAccessKeySecret(),
            globalApolloConfig.getAliyunAppTokenDomain());
        //verify(transformersRedisClient, times(1)).setStr(getAliyunTokenCacheKey(), "newToken," + expireTime, Duration.ofSeconds(7170));
    }

    private MessageVO constructWelcomeMessage(String sessionId, String content) {
        MessageVO messageVO = new MessageVO();
        messageVO.setMessageId(MsgIdGenerator.genId());
        messageVO.setType(MSG_TYPE_TEXT);
        messageVO.setRole(MSG_SENDER_ASSISTANT);
        messageVO.setContent(content);
        messageVO.setSessionId(sessionId);
        messageVO.setTime(DateUtils.getCurTimestamp());
        return messageVO;
    }

    @Test
    void testProcessImageMessageWithNoImages() throws IOException {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setImageUrls(Collections.emptyList());

        MessageContext messageContext = new MessageContext();
        messageContext.setUid("user123");
        messageContext.setSessionId("session123");

        SseEmitter sseEmitter = new SseEmitter();
        List<String> checkImgReasonList = Collections.emptyList();

        chatService.processImageMessage(chatQuestionReq, messageContext, sseEmitter, checkImgReasonList);

        // Verify that no message is saved or sent
        verify(messageBoxHandler, times(0)).getUserImageMessageVO(any(), any(), any());
        verify(messageBoxHandler, times(0)).saveDirectMessage(any(), any());
    }

    @Test
    void testProcessImageMessageWithImages() throws IOException {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setImageUrls(Collections.singletonList("https://example.com/image.jpg"));

        MessageContext messageContext = new MessageContext();
        messageContext.setUid("user123");
        messageContext.setSessionId("session123");

        SseEmitter sseEmitter = new SseEmitter();
        List<String> checkImgReasonList = Collections.emptyList();

        MessageVO expectedMessageVO = new MessageVO();
        expectedMessageVO.setQuestId("quest123");
        expectedMessageVO.setMessageId(123456L);
        expectedMessageVO.setSessionId("session123");
        expectedMessageVO.setTime(DateUtils.getCurTimestamp());
        expectedMessageVO.setType(MessageTypeEnum.IMAGE.getCode());
        expectedMessageVO.setRole("user");
        expectedMessageVO.setEnd(false);
        expectedMessageVO.setContent("[\"https://example.com/image.jpg\"]");

        when(messageBoxHandler.getUserImageMessageVO(checkImgReasonList, chatQuestionReq.getImageUrls(), messageContext))
            .thenReturn(expectedMessageVO);

        chatService.processImageMessage(chatQuestionReq, messageContext, sseEmitter, checkImgReasonList);

        // Verify that the message is saved and sent
        verify(messageBoxHandler, times(1)).getUserImageMessageVO(checkImgReasonList, chatQuestionReq.getImageUrls(), messageContext);
        verify(messageBoxHandler, times(1)).saveDirectMessage(expectedMessageVO, chatQuestionReq);
    }

    @Test
    void testProcessImageMessageWithImagesAndCheckReasons() throws IOException {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setImageUrls(Collections.singletonList("https://example.com/image.jpg"));

        MessageContext messageContext = new MessageContext();
        messageContext.setUid("user123");
        messageContext.setSessionId("session123");

        SseEmitter sseEmitter = new SseEmitter();
        List<String> checkImgReasonList = Collections.singletonList("sensitive");

        MessageVO expectedMessageVO = new MessageVO();
        expectedMessageVO.setQuestId("quest123");
        expectedMessageVO.setMessageId(123456L);
        expectedMessageVO.setSessionId("session123");
        expectedMessageVO.setTime(DateUtils.getCurTimestamp());
        expectedMessageVO.setType(MessageTypeEnum.IMAGE.getCode());
        expectedMessageVO.setRole("user");
        expectedMessageVO.setEnd(false);
        expectedMessageVO.setContent("[\"https://example.com/default_image.jpg\"]");

        when(messageBoxHandler.getUserImageMessageVO(checkImgReasonList, chatQuestionReq.getImageUrls(), messageContext))
            .thenReturn(expectedMessageVO);

        chatService.processImageMessage(chatQuestionReq, messageContext, sseEmitter, checkImgReasonList);

        // Verify that the message is saved and sent with the default image URL
        verify(messageBoxHandler, times(1)).getUserImageMessageVO(checkImgReasonList, chatQuestionReq.getImageUrls(), messageContext);
        verify(messageBoxHandler, times(1)).saveDirectMessage(expectedMessageVO, chatQuestionReq);
    }

    @Test
    void testSetDailyTipsFuture_Success() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        Session session = new Session();
        session.setSessionId("session123");
        sessionStartVO.setSession(session);

        DailyTipsVO dailyTipsVO = new DailyTipsVO();
        dailyTipsVO.setContent("Daily Tip Content");
        dailyTipsVO.setDate("2024-09-01");
        dailyTipsVO.setLunarDate("Lunar Date");

        StartReq startReq = new StartReq("123", "Category Name", "Category ID", "Keyword", 10, new HashMap<>());

        when(messageBoxHandler.saveDailyTips(dailyTipsVO, "session123", System.currentTimeMillis(), startReq)).thenReturn(true);

        CompletableFuture<DailyTipsVO> dailyTipsFuture =
            CompletableFuture.completedFuture(dailyTipsVO);

        // Act
        Whitebox.invokeMethod(
            chatService, "setDailyTipsFuture", dailyTipsFuture, sessionStartVO, startReq);

        // Assert
        assertEquals(dailyTipsVO, sessionStartVO.getDailyTips());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetDailyTipsFuture_Fail() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        Session session = new Session();
        session.setSessionId("session123");
        sessionStartVO.setSession(session);

        DailyTipsVO dailyTipsVO = new DailyTipsVO();
        dailyTipsVO.setContent("Daily Tip Content");
        dailyTipsVO.setDate("2024-09-01");
        dailyTipsVO.setLunarDate("Lunar Date");

        StartReq startReq = new StartReq("123", "Category Name", "Category ID", "Keyword", 10, new HashMap<>());

        when(messageBoxHandler.saveDailyTips(dailyTipsVO, "session123", System.currentTimeMillis(), startReq))
            .thenReturn(false);

        CompletableFuture<DailyTipsVO> dailyTipsFuture =
            CompletableFuture.completedFuture(dailyTipsVO);

        // Act
        Whitebox.invokeMethod(
            chatService, "setDailyTipsFuture", dailyTipsFuture, sessionStartVO, startReq);

        // Assert
        assertEquals(dailyTipsVO, sessionStartVO.getDailyTips());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetDailyTipsFuture_ExecutionException() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        Session session = new Session();
        session.setSessionId("session123");
        sessionStartVO.setSession(session);

        StartReq startReq = new StartReq("123", "Category Name", "Category ID", "Keyword", 10, new HashMap<>());

        CompletableFuture<DailyTipsVO> dailyTipsFuture = new CompletableFuture<>();
        dailyTipsFuture.completeExceptionally(new ExecutionException("Execution Exception", new Exception()));

        // Act
        Whitebox.invokeMethod(chatService, "setDailyTipsFuture", dailyTipsFuture, sessionStartVO, startReq);

        // Assert
        assertNull(sessionStartVO.getDailyTips());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetDailyTipsFuture_InterruptedException() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        Session session = new Session();
        session.setSessionId("session123");
        sessionStartVO.setSession(session);

        StartReq startReq = new StartReq("123", "Category Name", "Category ID", "Keyword", 10, new HashMap<>());

        CompletableFuture<DailyTipsVO> dailyTipsFuture = new CompletableFuture<>();
        dailyTipsFuture.completeExceptionally(new InterruptedException("Interrupted"));

        // Act
        Whitebox.invokeMethod(chatService, "setDailyTipsFuture", dailyTipsFuture, sessionStartVO, startReq);

        // Assert
        assertNull(sessionStartVO.getDailyTips());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetProductInfoFuture_Success() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        ProductInfoVO productInfoVO = new ProductInfoVO();
        productInfoVO.setId("123");
        productInfoVO.setShortTitle("Product Title");

        CompletableFuture<List<ProductInfoVO>> productInfoFuture = CompletableFuture.completedFuture(Collections.singletonList(productInfoVO));

        // Act
        Whitebox.invokeMethod(chatService, "setProductInfoFuture", productInfoFuture, sessionStartVO);

        // Assert
        assertEquals(productInfoVO, sessionStartVO.getProductInfoVO());
        assertTrue(sessionStartVO.getProductInfoVO().isStockOutReservedNew());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetProductInfoFuture_Timeout() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        CompletableFuture<List<ProductInfoVO>> productInfoFuture = new CompletableFuture<>();
        productInfoFuture.completeExceptionally(new TimeoutException("Timeout"));

        // Act
        Whitebox.invokeMethod(chatService, "setProductInfoFuture", productInfoFuture, sessionStartVO);

        assertNull(sessionStartVO.getProductInfoVO());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetProductInfoFuture_ExecutionException() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        CompletableFuture<List<ProductInfoVO>> productInfoFuture = new CompletableFuture<>();
        productInfoFuture.completeExceptionally(new ExecutionException("Execution Exception", new Exception()));

        // Act
        Whitebox.invokeMethod(chatService, "setProductInfoFuture", productInfoFuture, sessionStartVO);

        assertNull(sessionStartVO.getProductInfoVO());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetProductInfoFuture_InterruptedException() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        CompletableFuture<List<ProductInfoVO>> productInfoFuture = new CompletableFuture<>();
        productInfoFuture.completeExceptionally(new InterruptedException("Interrupted"));

        // Act
        Whitebox.invokeMethod(chatService, "setProductInfoFuture", productInfoFuture, sessionStartVO);

        assertNull(sessionStartVO.getProductInfoVO());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetStartMessageFuture_Success() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        Session newSession = new Session("session123", System.currentTimeMillis());
        Triple<String, List<String>, List<String>> chatStartMessage = Triple.of("Welcome!", Collections.singletonList("Guide Word"), Collections.singletonList("Question"));

        CompletableFuture<Triple<String, List<String>, List<String>>> chatStartMessageFuture = CompletableFuture.completedFuture(chatStartMessage);

        // Act
        Whitebox.invokeMethod(chatService, "setStartMessageFuture", chatStartMessageFuture, sessionStartVO, newSession);

        assertEquals("Welcome!", sessionStartVO.getWelcome().getContent());
        assertEquals(Collections.singletonList("Guide Word"), sessionStartVO.getIntentGuideWords());
        assertEquals(Collections.singletonList("Question"), sessionStartVO.getQuickQuestionList());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetStartMessageFuture_Null() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        Session newSession = new Session("session123", System.currentTimeMillis());

        CompletableFuture<Triple<String, List<String>, List<String>>> chatStartMessageFuture = CompletableFuture.completedFuture(null);
        when(globalApolloConfig.getWelcome()).thenReturn("Welcome!");
        // Act
        Whitebox.invokeMethod(chatService, "setStartMessageFuture", chatStartMessageFuture, sessionStartVO, newSession);

        assertEquals("Welcome!", sessionStartVO.getWelcome().getContent());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetStartMessageFuture_Left_Null() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        Session newSession = new Session("session123", System.currentTimeMillis());

        Triple<String, List<String>, List<String>> chatStartMessage = Triple.of(null, Collections.singletonList("Guide Word"), Collections.singletonList("Question"));

        CompletableFuture<Triple<String, List<String>, List<String>>> chatStartMessageFuture = CompletableFuture.completedFuture(chatStartMessage);

        when(globalApolloConfig.getWelcome()).thenReturn("Welcome!");
        // Act
        Whitebox.invokeMethod(chatService, "setStartMessageFuture", chatStartMessageFuture, sessionStartVO, newSession);

        assertEquals("Welcome!", sessionStartVO.getWelcome().getContent());
        assertEquals(Collections.singletonList("Guide Word"), sessionStartVO.getIntentGuideWords());
        assertEquals(Collections.singletonList("Question"), sessionStartVO.getQuickQuestionList());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetStartMessageFuture_Timeout() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        Session newSession = new Session("session123", System.currentTimeMillis());
        CompletableFuture<Triple<String, List<String>, List<String>>> chatStartMessageFuture = new CompletableFuture<>();
        chatStartMessageFuture.completeExceptionally(new TimeoutException("Timeout"));

        Whitebox.invokeMethod(chatService, "setStartMessageFuture", chatStartMessageFuture, sessionStartVO, newSession);

        assertNotNull(sessionStartVO.getWelcome());
        assertNull(sessionStartVO.getIntentGuideWords());
        assertNull(sessionStartVO.getQuickQuestionList());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetStartMessageFuture_ExecutionException() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        Session newSession = new Session("session123", System.currentTimeMillis());
        CompletableFuture<Triple<String, List<String>, List<String>>> chatStartMessageFuture = new CompletableFuture<>();
        chatStartMessageFuture.completeExceptionally(new ExecutionException("Execution Exception", new Exception()));

        Whitebox.invokeMethod(chatService, "setStartMessageFuture", chatStartMessageFuture, sessionStartVO, newSession);

        assertNotNull(sessionStartVO.getWelcome());
        assertNull(sessionStartVO.getIntentGuideWords());
        assertNull(sessionStartVO.getQuickQuestionList());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetStartMessageFuture_InterruptedException() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        Session newSession = new Session("session123", System.currentTimeMillis());
        CompletableFuture<Triple<String, List<String>, List<String>>> chatStartMessageFuture = new CompletableFuture<>();
        chatStartMessageFuture.completeExceptionally(new InterruptedException("Interrupted"));

        Whitebox.invokeMethod(chatService, "setStartMessageFuture", chatStartMessageFuture, sessionStartVO, newSession);

        assertNotNull(sessionStartVO.getWelcome());
        assertNull(sessionStartVO.getIntentGuideWords());
        assertNull(sessionStartVO.getQuickQuestionList());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetHistoryFuture_Success() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        MessageBoxVO history = new MessageBoxVO();
        history.setMessageList(Collections.singletonList(new MessageVO()));

        CompletableFuture<MessageBoxVO> historyFuture = CompletableFuture.completedFuture(history);
        Whitebox.invokeMethod(chatService, "setHistoryFuture", historyFuture, sessionStartVO);

        assertTrue(sessionStartVO.isHasHistory());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetHistoryFuture_Timeout() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        CompletableFuture<MessageBoxVO> historyFuture = new CompletableFuture<>();
        historyFuture.completeExceptionally(new TimeoutException("Timeout"));

        Whitebox.invokeMethod(chatService, "setHistoryFuture", historyFuture, sessionStartVO);

        assertFalse(sessionStartVO.isHasHistory());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetHistoryFuture_ExecutionException() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        CompletableFuture<MessageBoxVO> historyFuture = new CompletableFuture<>();
        historyFuture.completeExceptionally(new ExecutionException("Execution Exception", new Exception()));

        Whitebox.invokeMethod(chatService, "setHistoryFuture", historyFuture, sessionStartVO);

        assertFalse(sessionStartVO.isHasHistory());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testSetHistoryFuture_InterruptedException() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        CompletableFuture<MessageBoxVO> historyFuture = new CompletableFuture<>();
        historyFuture.completeExceptionally(new InterruptedException("Interrupted"));

        Whitebox.invokeMethod(chatService, "setHistoryFuture", historyFuture, sessionStartVO);

        assertFalse(sessionStartVO.isHasHistory());
        verify(globalApolloConfig, times(1)).getThreadTimeout();
    }

    @Test
    void testCompletions_RiskCheck_Hit() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setQuestion("Sensitive content");

        SseEmitter sseEmitter = new SseEmitter();

        MessageContext messageContext = new MessageContext();
        messageContext.setUid("user123");

        MessageVO userMessageVO = new MessageVO();
        userMessageVO.setMessageId(System.currentTimeMillis());
        userMessageVO.setType(MSG_TYPE_TEXT);
        userMessageVO.setRole(MSG_SENDER_ASSISTANT);
        userMessageVO.setContent("Sensitive content");
        userMessageVO.setSessionId("session123");
        userMessageVO.setTime(DateUtils.getCurTimestamp());

        MessageVO sensitiveMessageVO = new MessageVO();
        sensitiveMessageVO.setMessageId(System.currentTimeMillis());
        sensitiveMessageVO.setType(MSG_TYPE_TEXT);
        sensitiveMessageVO.setRole(MSG_SENDER_ASSISTANT);
        sensitiveMessageVO.setContent("Sensitive content detected");
        sensitiveMessageVO.setSessionId("session123");
        sensitiveMessageVO.setTime(DateUtils.getCurTimestamp());

        TextCheckReason reason = new TextCheckReason();
        reason.setLabel("涉政词汇");
        reason.setKeyword("sensitive_word");
        List<TextCheckReason> checkReasons = Collections.singletonList(reason);

        when(messageBoxHandler.getUserMessageVO(anyList(), anyString(), any(MessageContext.class))).thenReturn(userMessageVO);
        when(messageBoxHandler.saveDirectMessage(any(MessageVO.class), any(ChatQuestionReq.class))).thenReturn(true);
        when(antispamProxy.textCheck(any(TextCheckDTO.class))).thenReturn(checkReasons);
        when(messageBoxHandler.getSensitiveMessageVO(any(MessageContext.class))).thenReturn(sensitiveMessageVO);
        when(messageBoxHandler.saveDirectMessage(any(MessageVO.class), any(ChatQuestionReq.class))).thenReturn(true);

        // Act
        SseEmitter result = chatService.completions(chatQuestionReq, sseEmitter);

        // Assert
        assertNotNull(result);
        verify(messageBoxHandler, times(1)).checkSession("session123", messageContext);
        verify(messageBoxHandler, times(1)).getUserMessageVO(anyList(), anyString(), eq(messageContext));
        verify(messageBoxHandler, times(2)).saveDirectMessage(any(MessageVO.class), any(ChatQuestionReq.class));
        verify(antispamProxy, times(1)).textCheck(any(TextCheckDTO.class));
        verify(messageBoxHandler, times(1)).getSensitiveMessageVO(messageContext);
    }

    @Test
    void testCompletions_ImageCheck_Hit() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setQuestion("Hello, world!");
        chatQuestionReq.setImageUrls(Collections.singletonList("http://example.com/image.jpg"));

        SseEmitter sseEmitter = new SseEmitter();

        MessageContext messageContext = new MessageContext();
        messageContext.setUid("user123");

        MessageVO userMessageVO = new MessageVO();
        userMessageVO.setMessageId(System.currentTimeMillis());
        userMessageVO.setType(MSG_TYPE_TEXT);
        userMessageVO.setRole(MSG_SENDER_ASSISTANT);
        userMessageVO.setContent("Hello, world!");
        userMessageVO.setSessionId("session123");
        userMessageVO.setTime(DateUtils.getCurTimestamp());

        MessageVO imageMessageVO = new MessageVO();
        imageMessageVO.setMessageId(System.currentTimeMillis());
        imageMessageVO.setType(MSG_TYPE_TEXT);
        imageMessageVO.setRole(MSG_SENDER_ASSISTANT);
        imageMessageVO.setContent("Image detected");
        imageMessageVO.setSessionId("session123");
        imageMessageVO.setTime(DateUtils.getCurTimestamp());

        List<String> checkImgReasonList = Collections.singletonList("黄色词汇");

        ImgCheckDTO imgCheckDTO = new ImgCheckDTO();
        imgCheckDTO.setAppId("product_assistant");
        imgCheckDTO.setServerType(0);
        imgCheckDTO.setDataId(System.currentTimeMillis() + "");
        imgCheckDTO.setSceneCode("product_assistant");
        imgCheckDTO.setImgUrls(chatQuestionReq.getImageUrls());
        imgCheckDTO.setUser(chatQuestionReq.getUid());

        when(messageBoxHandler.getUserMessageVO(anyList(), anyString(), any(MessageContext.class))).thenReturn(userMessageVO);
        when(messageBoxHandler.saveDirectMessage(any(MessageVO.class), any(ChatQuestionReq.class))).thenReturn(true);
        when(antispamProxy.buildImgCheckDTO(any(ChatQuestionReq.class))).thenReturn(imgCheckDTO);
        when(antispamProxy.imageCheck(any(ImgCheckDTO.class))).thenReturn(checkImgReasonList);
        when(messageBoxHandler.getUserImageMessageVO(anyList(), anyList(), any(MessageContext.class))).thenReturn(imageMessageVO);
        when(messageBoxHandler.saveDirectMessage(any(MessageVO.class), any(ChatQuestionReq.class))).thenReturn(true);

        // Act
        SseEmitter result = chatService.completions(chatQuestionReq, sseEmitter);

        // Assert
        assertNotNull(result);
        verify(messageBoxHandler, times(1)).checkSession("session123", messageContext);
        verify(messageBoxHandler, times(1)).getUserMessageVO(anyList(), anyString(), eq(messageContext));
        verify(messageBoxHandler, times(2)).saveDirectMessage(any(MessageVO.class), any(ChatQuestionReq.class));
        verify(antispamProxy, times(1)).buildImgCheckDTO(chatQuestionReq);
        verify(antispamProxy, times(1)).imageCheck(imgCheckDTO);
        verify(messageBoxHandler, times(1)).getUserImageMessageVO(checkImgReasonList, chatQuestionReq.getImageUrls(), messageContext);
    }

    @Test
    void testCompletionsWithValidRequest_SaveDirectMessage_Error() throws Exception {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setQuestion("Hello");
        chatQuestionReq.setProductId("product123");
        chatQuestionReq.setFrontCategoryName("categoryName");
        chatQuestionReq.setFrontCategoryId("categoryId");
        chatQuestionReq.setSearchKeyword("keyword");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setStream(true);
        chatQuestionReq.setImageUrls(Collections.singletonList("https://example.com/image.jpg"));

        MessageContext messageContext = MessageContext.builder()
            .uid("user123")
            .build();

        List<TextCheckReason> checkReasons = Collections.emptyList();
        MessageVO userMessageVO = new MessageVO();
        userMessageVO.setMessageId(MsgIdGenerator.genId());
        userMessageVO.setType(MSG_TYPE_TEXT);
        userMessageVO.setRole(MSG_SENDER_ASSISTANT);
        userMessageVO.setContent("Hello");
        userMessageVO.setSessionId("session123");
        userMessageVO.setTime(DateUtils.getCurTimestamp());

        SseEmitter sseEmitter = new SseEmitter();

        // Mock getSegmentMessageVO to return a non-null MessageVO
        MessageVO segmentMessageVO = new MessageVO();
        segmentMessageVO.setMessageId(MsgIdGenerator.genId());
        segmentMessageVO.setType(MSG_TYPE_TEXT);
        segmentMessageVO.setRole(MSG_SENDER_ASSISTANT);
        segmentMessageVO.setContent("Segment Message");
        segmentMessageVO.setSessionId("session123");
        segmentMessageVO.setTime(DateUtils.getCurTimestamp());

        when(messageBoxHandler.getUserMessageVO(checkReasons, chatQuestionReq.getQuestion(), messageContext)).thenReturn(userMessageVO);
        when(messageBoxHandler.saveDirectMessage(any(), any())).thenReturn(false);
        when(nlpAiProxy.sendRequestV2(chatQuestionReq, sseEmitter, messageContext)).thenReturn(sseEmitter);
        when(nlpAiProxy.sendRequest(chatQuestionReq, sseEmitter, messageContext)).thenReturn(sseEmitter);

        SseEmitter actual = chatService.completions(chatQuestionReq, sseEmitter);

        assertEquals(sseEmitter, actual);
        verify(messageBoxHandler, times(1)).checkSession(chatQuestionReq.getSessionId(), messageContext);
        verify(messageBoxHandler, times(1)).getUserMessageVO(checkReasons, chatQuestionReq.getQuestion(), messageContext);
        verify(messageBoxHandler, times(1)).saveDirectMessage(eq(userMessageVO), eq(chatQuestionReq));
    }

    @Test
    void getEntryConfigByAppVersion_NullAppVersion_ReturnsNull() {
        when(globalApolloConfig.getChatEntryConfigBO()).thenReturn(new ChatEntryConfigBO());

        EntryConfigBO result = chatService.getEntryConfigByAppVersion(null);
        assertNull(result);
    }

    @Test
    void getEntryConfigByAppVersion_EmptyAppVersion_ReturnsNull() {
        when(globalApolloConfig.getChatEntryConfigBO()).thenReturn(new ChatEntryConfigBO());

        EntryConfigBO result = chatService.getEntryConfigByAppVersion("");
        assertNull(result);
    }

    @Test
    void getEntryConfigByAppVersion_NullChatEntryConfigBO_ReturnsNull() {
        when(globalApolloConfig.getChatEntryConfigBO()).thenReturn(null);

        EntryConfigBO result = chatService.getEntryConfigByAppVersion("1.0.0");
        assertNull(result);
    }

    @Test
    void getEntryConfigByAppVersion_EmptyVersionRangeList_ReturnsNull() {
        ChatEntryConfigBO chatEntryConfigBO = new ChatEntryConfigBO();
        chatEntryConfigBO.setVersionRangeList(new ArrayList<>());

        when(globalApolloConfig.getChatEntryConfigBO()).thenReturn(chatEntryConfigBO);

        EntryConfigBO result = chatService.getEntryConfigByAppVersion("1.0.0");
        assertNull(result);
    }

    @Test
    void getEntryConfigByAppVersion_MatchingVersionRange_ReturnsEntryConfigBO() {

        ChatEntryConfigBO chatEntryConfigBO = JsonUtil.toObject(entryConfig, ChatEntryConfigBO.class);

        when(globalApolloConfig.getChatEntryConfigBO()).thenReturn(chatEntryConfigBO);
        // 老版本
        EntryConfigBO result1 = chatService.getEntryConfigByAppVersion("11.40.0");
        assertNotNull(result1);
        assertEquals("https://activity.m.ddxq.mobi/#/ai/chat?is_nav_hide=true&from=detail", result1.getUrl());

        EntryConfigBO result2 = chatService.getEntryConfigByAppVersion("11.39.0");
        assertNotNull(result2);
        assertEquals("https://activity.m.ddxq.mobi/#/ai/chat?is_nav_hide=true&from=detail", result2.getUrl());
        EntryConfigBO result3 = chatService.getEntryConfigByAppVersion("11.40.1");
        assertNotNull(result3);
        assertEquals("https://activity.m.ddxq.mobi/#/ai/chat?is_nav_hide=true&from=detail", result3.getUrl());
        // 新版本
        EntryConfigBO result4 = chatService.getEntryConfigByAppVersion("11.41.0");
        assertNotNull(result4);
        assertEquals("https://new.activity.m.ddxq.mobi/#/ai/chat?is_nav_hide=true&from=detail", result4.getUrl());
        EntryConfigBO result5 = chatService.getEntryConfigByAppVersion("11.41.1");
        assertNotNull(result5);
        assertEquals("https://new.activity.m.ddxq.mobi/#/ai/chat?is_nav_hide=true&from=detail", result5.getUrl());
        EntryConfigBO result6 = chatService.getEntryConfigByAppVersion("11.42.0");
        assertNotNull(result6);
        assertEquals("https://new.activity.m.ddxq.mobi/#/ai/chat?is_nav_hide=true&from=detail", result6.getUrl());
    }

    @Test
    void entry_Degrade() {
        String entryConfig = "{\n"
            + "    \"enable\": true,\n"
            + "    \"whiteStationIds\": [\n"
            + "        \"all\"\n"
            + "    ],\n"
            + "    \"blackUIds\": [],\n"
            + "    \"blackPIds\": [],\n"
            + "    \"whitePIds\": [\n"
            + "        \"all\"\n"
            + "    ],\n"
            + "    \"whiteCategoryIds\": [\n"
            + "        \"1\",\n"
            + "        \"103\",\n"
            + "        \"236\",\n"
            + "        \"421\",\n"
            + "        \"450\",\n"
            + "        \"643\",\n"
            + "        \"736\",\n"
            + "        \"751\",\n"
            + "        \"858\",\n"
            + "        \"879\",\n"
            + "        \"914\",\n"
            + "        \"1009\",\n"
            + "        \"1045\",\n"
            + "        \"1161\",\n"
            + "        \"1471\",\n"
            + "        \"1600\"\n"
            + "    ],\n"
            + "    \"text\": \"AI助手\",\n"
            + "    \"link\": \"https://activity.m.ddxq.mobi/#/ai/chat?is_nav_hide=true&from=detail\",\n"
            + "    \"versionRangeList\": [\n"
            + "        {\n"
            + "            \"minVersion\": \"\",\n"
            + "            \"maxVersion\": \"11.40.99\",\n"
            + "            \"value\": {\n"
            + "                \"text\": \"\",\n"
            + "                \"url\": \"https://activity.m.ddxq.mobi/#/ai/chat?is_nav_hide=true&from=detail\",\n"
            + "                \"enable\": \"false\",\n"
            + "                \"icon\": \"\"\n"
            + "            }\n"
            + "            \n"
            + "        },{\n"
            + "            \"minVersion\": \"11.41.00\",\n"
            + "            \"maxVersion\": \"\",\n"
            + "            \"value\": {\n"
            + "                \"text\": \"\",\n"
            + "                \"url\": \"https://new.activity.m.ddxq.mobi/#/ai/chat?is_nav_hide=true&from=detail\",\n"
            + "                \"enable\": \"true\",\n"
            + "                \"icon\": \"\"\n"
            + "            }\n"
            + "        }\n"
            + "    ]\n"
            + "}";

        ChatEntryConfigBO chatEntryConfigBO = JsonUtil.toObject(entryConfig, ChatEntryConfigBO.class);

        when(globalApolloConfig.getChatEntryConfigBO()).thenReturn(chatEntryConfigBO);

        // 老版本
        EntryReq entryReq = new EntryReq();
        entryReq.setUid("user123");
        entryReq.setStationId("station123");
        entryReq.setProductId("product123");
        entryReq.setAppVersion("11.40.0");
        entryReq.setBackendCategoryPath("category1,category2");

        ChatEntryVO actual = chatService.entry(entryReq);
        assertNotNull(actual);
        assertNull(actual.getLink());

        // 新版本
        EntryReq entryReqNew = new EntryReq();
        entryReqNew.setUid("user123");
        entryReqNew.setStationId("station123");
        entryReqNew.setProductId("product123");
        entryReqNew.setAppVersion("11.41.0");
        entryReqNew.setBackendCategoryPath("category1,category2");

        ChatEntryVO actualNew = chatService.entry(entryReqNew);
        assertNotNull(actualNew);
        assertNotNull(actualNew.getLink());
    }

    @Test
    void testSetFunctionButtonList_Success() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        List<FunctionButton> functionButtonList = new ArrayList<>();
        FunctionButton functionButton = new FunctionButton();
        functionButton.setType(1);
        functionButton.setText("今天吃什么");
        functionButton.setDisplayText("今天吃什么");
        functionButtonList.add(functionButton);

        when(globalApolloConfig.getFunctionButtonList()).thenReturn(functionButtonList);
        // Act
        Whitebox.invokeMethod(chatService, "setFunctionButtonList", sessionStartVO);

        // Assert
        assertNotNull(sessionStartVO.getFunctionButtonList());
        assertNotNull(sessionStartVO.getFunctionButtonList().get(0));
        assertEquals("今天吃什么", sessionStartVO.getFunctionButtonList().get(0).getText());
        verify(globalApolloConfig, times(1)).getFunctionButtonList();
    }

    @Test
    void testSetSugWordsList_Success() throws Exception {
        SessionStartVO sessionStartVO = new SessionStartVO();
        List<SugWords> sugWordsList = new ArrayList<>();
        SugWords sugWords = new SugWords();
        sugWords.setText("这是什么");
        sugWords.setSort("1");
        SugWords sugWords2 = new SugWords();
        sugWords2.setText("拍照识卡路里");
        sugWords2.setSort("2");
        sugWordsList.add(sugWords);
        sugWordsList.add(sugWords2);

        when(globalApolloConfig.getSugWordsList()).thenReturn(sugWordsList);
        // Act
        Whitebox.invokeMethod(chatService, "setSugWordsList", sessionStartVO);

        // Assert
        assertEquals("这是什么", sessionStartVO.getSugList().get(0).getText());
        assertEquals("拍照识卡路里", sessionStartVO.getSugList().get(1).getText());
        verify(globalApolloConfig, times(1)).getSugWordsList();
    }

    @Test
    void testQueryEntryByScene_FeatureDisabled() {
        // 准备测试数据
        EntryReq entryReq = new EntryReq();
        entryReq.setScene(EntrySceneEnum.LOVE_QUALITY.getCode());
        entryReq.setAppClientId("1");
        entryReq.setApiVersion("11.41.0");
        entryReq.setChannel("test");
        entryReq.setAppVersion("11.41.0");

        // 设置 mock 行为
        when(featureControlUtil.isFeatureEnabled("1", "1.0", "test")).thenReturn(false);

        // 执行测试
        ChatEntryVO result = chatService.queryEntryByScene(entryReq);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isShowEntry());
        assertNull(result.getText());
        assertNull(result.getLink());

    }

    @Test
    void testQueryEntryByScene_ConfigEmpty() {
        // 准备测试数据
        EntryReq entryReq = new EntryReq();
        entryReq.setScene(EntrySceneEnum.LOVE_QUALITY.getCode());
        entryReq.setAppClientId("1");
        entryReq.setApiVersion("11.41.0");
        entryReq.setChannel("test");
        entryReq.setAppVersion("11.41.0");

        // 设置 mock 行为
        when(featureControlUtil.isFeatureEnabled("1", "1.0", "test")).thenReturn(true);
        when(globalApolloConfig.getEntrySceneConfigMap()).thenReturn(null);

        // 执行测试
        ChatEntryVO result = chatService.queryEntryByScene(entryReq);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isShowEntry());
        assertNull(result.getText());
        assertNull(result.getLink());

    }

    @Test
    void testQueryEntryByScene_SceneConfigNull() {
        // 准备测试数据
        EntryReq entryReq = new EntryReq();
        entryReq.setScene(EntrySceneEnum.LOVE_QUALITY.getCode());
        entryReq.setAppClientId("1");
        entryReq.setApiVersion("11.41.0");
        entryReq.setChannel("test");
        entryReq.setAppVersion("11.41.0");

        // 设置 mock 行为
        when(featureControlUtil.isFeatureEnabled("1", "11.41.0", "test")).thenReturn(true);

        Map<String, EntrySceneConfigBO> configMap = new HashMap<>();
        when(globalApolloConfig.getEntrySceneConfigMap()).thenReturn(configMap);

        // 执行测试
        ChatEntryVO result = chatService.queryEntryByScene(entryReq);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isShowEntry());
        assertNull(result.getText());
        assertNull(result.getLink());

    }

    @Test
    void testQueryEntryByScene_VersionMatched() {
        // 准备测试数据
        EntryReq entryReq = new EntryReq();
        entryReq.setScene(EntrySceneEnum.LOVE_QUALITY.getCode());
        entryReq.setAppClientId("1");
        entryReq.setApiVersion("11.41.0");
        entryReq.setChannel("test");
        entryReq.setAppVersion("11.41.0");

        // 设置 mock 行为
        when(featureControlUtil.isFeatureEnabled("1", "11.41.0", "test")).thenReturn(true);

        Map<String, EntrySceneConfigBO> configMap = new HashMap<>();
        EntrySceneConfigBO sceneConfig = new EntrySceneConfigBO();
        sceneConfig.setAfterUrl("http://after.example.com");
        sceneConfig.setBeforeUrl("http://before.example.com");
        sceneConfig.setText("Test Text");
        sceneConfig.setVersionThreshold("11.41.00");
        sceneConfig.setDegradeSwitch(0);
        configMap.put(EntrySceneEnum.LOVE_QUALITY.getCode(), sceneConfig);
        when(globalApolloConfig.getEntrySceneConfigMap()).thenReturn(configMap);

        // VersionUtils.isGreaterOrEqual 将使用真实实现
        // 因为我们已经导入了 VersionUtils 类

        // 执行测试
        ChatEntryVO result = chatService.queryEntryByScene(entryReq);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isShowEntry());
        assertEquals("Test Text", result.getText());
        assertEquals("http://after.example.com", result.getLink());
    }

    @Test
    void testQueryEntryByScene_VersionMatched_UrlEmpty() {
        // 准备测试数据
        EntryReq entryReq = new EntryReq();
        entryReq.setScene(EntrySceneEnum.LOVE_QUALITY.getCode());
        entryReq.setAppClientId("1");
        entryReq.setApiVersion("11.41.0");
        entryReq.setChannel("test");
        entryReq.setAppVersion("11.41.0");

        // 设置 mock 行为
        when(featureControlUtil.isFeatureEnabled("1", "11.41.0", "test")).thenReturn(true);

        Map<String, EntrySceneConfigBO> configMap = new HashMap<>();
        EntrySceneConfigBO sceneConfig = new EntrySceneConfigBO();
        sceneConfig.setAfterUrl(null);
        sceneConfig.setBeforeUrl(null);
        sceneConfig.setText("Test Text");
        sceneConfig.setVersionThreshold("11.41.00");
        sceneConfig.setDegradeSwitch(0);
        configMap.put(EntrySceneEnum.LOVE_QUALITY.getCode(), sceneConfig);
        when(globalApolloConfig.getEntrySceneConfigMap()).thenReturn(configMap);

        // VersionUtils.isGreaterOrEqual 将使用真实实现
        // 因为我们已经导入了 VersionUtils 类

        // 执行测试
        ChatEntryVO result = chatService.queryEntryByScene(entryReq);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isShowEntry());
        assertNull(result.getText());
        assertNull(result.getLink());
    }

    @Test
    void testQueryEntryByScene_VersionNotMatched() {
        // 准备测试数据
        EntryReq entryReq = new EntryReq();
        entryReq.setScene(EntrySceneEnum.LOVE_QUALITY.getCode());
        entryReq.setAppClientId("1");
        entryReq.setApiVersion("11.39.1");
        entryReq.setChannel("test");
        entryReq.setAppVersion("11.39.1");

        // 设置 mock 行为
        when(featureControlUtil.isFeatureEnabled("1", "11.39.1", "test")).thenReturn(true);

        Map<String, EntrySceneConfigBO> configMap = new HashMap<>();
        EntrySceneConfigBO sceneConfig = new EntrySceneConfigBO();
        sceneConfig.setAfterUrl("http://after.example.com");
        sceneConfig.setBeforeUrl("http://before.example.com");
        sceneConfig.setText("Test Text");
        sceneConfig.setVersionThreshold("11.41.00");
        sceneConfig.setDegradeSwitch(0);
        configMap.put(EntrySceneEnum.LOVE_QUALITY.getCode(), sceneConfig);
        when(globalApolloConfig.getEntrySceneConfigMap()).thenReturn(configMap);

        // VersionUtils.isGreaterOrEqual 将使用真实实现
        // 因为我们已经导入了 VersionUtils 类

        // 执行测试
        ChatEntryVO result = chatService.queryEntryByScene(entryReq);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isShowEntry());
        assertEquals("Test Text", result.getText());
        assertEquals("http://before.example.com", result.getLink());
    }

    @Test
    void testQueryEntryByScene_SceneDegrade() {
        // 准备测试数据
        EntryReq entryReq = new EntryReq();
        entryReq.setScene(EntrySceneEnum.LOVE_QUALITY.getCode());
        entryReq.setAppClientId("1");
        entryReq.setApiVersion("11.39.1");
        entryReq.setChannel("test");
        entryReq.setAppVersion("11.39.1");

        // 设置 mock 行为
        when(featureControlUtil.isFeatureEnabled("1", "11.39.1", "test")).thenReturn(true);

        Map<String, EntrySceneConfigBO> configMap = new HashMap<>();
        EntrySceneConfigBO sceneConfig = new EntrySceneConfigBO();
        sceneConfig.setAfterUrl("http://after.example.com");
        sceneConfig.setBeforeUrl("http://before.example.com");
        sceneConfig.setText("Test Text");
        sceneConfig.setVersionThreshold("11.41.00");
        sceneConfig.setDegradeSwitch(1);
        configMap.put(EntrySceneEnum.LOVE_QUALITY.getCode(), sceneConfig);
        when(globalApolloConfig.getEntrySceneConfigMap()).thenReturn(configMap);

        // VersionUtils.isGreaterOrEqual 将使用真实实现
        // 因为我们已经导入了 VersionUtils 类

        // 执行测试
        ChatEntryVO result = chatService.queryEntryByScene(entryReq);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isShowEntry());
        assertNull(result.getText());
        assertNull(result.getLink());
    }

}
