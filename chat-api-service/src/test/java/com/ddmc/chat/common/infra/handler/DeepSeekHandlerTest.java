package com.ddmc.chat.common.infra.handler;

import static com.ddmc.chat.common.domain.Constants.SETTING_VALUE_DEFAULT;
import static com.ddmc.chat.common.domain.Constants.SETTING_VALUE_OFF;
import static com.ddmc.chat.common.domain.Constants.SETTING_VALUE_ON;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

import com.ddmc.chat.common.constant.Constans;
import com.ddmc.chat.common.infra.config.DeepSeekConfig;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.dao.UserSettingsDAO;
import com.ddmc.chat.common.infra.proxy.ABTestProxy;
import com.ddmc.chat.common.mapper.DO.UserSettingsDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DeepSeekHandler.class})
class DeepSeekHandlerTest {

    @InjectMocks
    private DeepSeekHandler deepSeekHandler;

    @Mock
    private UserSettingsDAO userSettingsDAO;

    @Mock
    private GlobalApolloConfig globalApolloConfig;

    @Mock
    private DeepSeekConfig deepSeekConfig;

    @Mock
    private ABTestProxy abTestProxy;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void queryReasonSwitch_DegradeSwitchOn_ReturnsDefaultValue() {
        when(globalApolloConfig.getAiChatDsReasonDegradeSwitch()).thenReturn(Constans.ONE);
        when(globalApolloConfig.getDeepSeekConfig()).thenReturn(deepSeekConfig);
        when(deepSeekConfig.getReasonDefaultSwitch()).thenReturn(SETTING_VALUE_DEFAULT);

        Integer result = deepSeekHandler.queryReasonSwitch("testUid");

        assertEquals(SETTING_VALUE_DEFAULT, result);
    }

    @Test
    void queryReasonSwitch_UserSettingsFound_ReturnsUserSetting() {
        UserSettingsDO userSettingsDO = new UserSettingsDO();
        userSettingsDO.setReasonSwitch(1);

        when(globalApolloConfig.getAiChatDsReasonDegradeSwitch()).thenReturn(Constans.ZERO);
        when(userSettingsDAO.selectUserSettingsByOwner("testUid")).thenReturn(userSettingsDO);

        Integer result = deepSeekHandler.queryReasonSwitch("testUid");

        assertEquals(1, result);
    }

    @Test
    void queryReasonSwitch_UserSettingsNotFound_ReturnsDefaultValue() {
        when(globalApolloConfig.getAiChatDsReasonDegradeSwitch()).thenReturn(Constans.ZERO);
        when(userSettingsDAO.selectUserSettingsByOwner("testUid")).thenReturn(null);
        when(globalApolloConfig.getDeepSeekConfig()).thenReturn(deepSeekConfig);
        when(deepSeekConfig.getReasonDefaultSwitch()).thenReturn(SETTING_VALUE_DEFAULT);
        when(globalApolloConfig.getAiChatDsReasonSwitchAbtestExpId()).thenReturn("unit_test_ab_layer_id");
        when(abTestProxy.abTest(any(), any())).thenReturn("exp");

        Integer result = deepSeekHandler.queryReasonSwitch("testUid");

        assertEquals(SETTING_VALUE_OFF, result);
    }

    @Test
    void queryReasonSwitch_ExceptionOccurs_ReturnsDefaultValue() {
        when(globalApolloConfig.getAiChatDsReasonDegradeSwitch()).thenReturn(Constans.ZERO);
        when(userSettingsDAO.selectUserSettingsByOwner("testUid")).thenThrow(new RuntimeException("Test Exception"));
        when(globalApolloConfig.getDeepSeekConfig()).thenReturn(deepSeekConfig);
        when(deepSeekConfig.getReasonDefaultSwitch()).thenReturn(SETTING_VALUE_DEFAULT);
        when(globalApolloConfig.getAiChatDsReasonSwitchAbtestExpId()).thenReturn("unit_test_ab_layer_id");
        when(abTestProxy.abTest(any(), any())).thenReturn("exp");

        Integer result = deepSeekHandler.queryReasonSwitch("testUid");

        assertEquals(deepSeekConfig.getReasonDefaultSwitch(), result);
    }

    @Test
    void isDeepSeekReasonSwitchDegrade_WhenSwitchIsOne_ShouldReturnTrue() {
        when(globalApolloConfig.getAiChatDsReasonDegradeSwitch()).thenReturn(Constans.ONE);
        boolean result = deepSeekHandler.isDeepSeekReasonSwitchDegrade();
        assertTrue(result);
    }

    @Test
    void isDeepSeekReasonSwitchDegrade_WhenSwitchIsNotOne_ShouldReturnFalse() {
        when(globalApolloConfig.getAiChatDsReasonDegradeSwitch()).thenReturn(Constans.ZERO);
        boolean result = deepSeekHandler.isDeepSeekReasonSwitchDegrade();
        assertFalse(result);
    }

    @Test
    void queryReasonSwitch_DegradeOn_ReturnsOff() {
        when(globalApolloConfig.getAiChatDsReasonDegradeSwitch()).thenReturn(Constans.ONE);
        when(globalApolloConfig.getDeepSeekConfig()).thenReturn(deepSeekConfig);
        when(deepSeekConfig.getReasonDefaultSwitch()).thenReturn(SETTING_VALUE_DEFAULT);

        Integer result = deepSeekHandler.queryReasonSwitch("testUid");

        assertEquals(SETTING_VALUE_DEFAULT, result);
    }

    @Test
    void queryReasonSwitch_UserSetting_ReturnsUserSetting() {
        UserSettingsDO userSettingsDO = new UserSettingsDO();
        userSettingsDO.setReasonSwitch(SETTING_VALUE_ON);

        when(userSettingsDAO.selectUserSettingsByOwner("testUid")).thenReturn(userSettingsDO);

        Integer result = deepSeekHandler.queryReasonSwitch("testUid");

        assertEquals(SETTING_VALUE_ON, result);
    }

    @Test
    void queryReasonSwitch_NoUserSetting_ReturnsDefault() {
        when(userSettingsDAO.selectUserSettingsByOwner("testUid")).thenReturn(null);
        when(globalApolloConfig.getDeepSeekConfig()).thenReturn(deepSeekConfig);
        when(deepSeekConfig.getReasonDefaultSwitch()).thenReturn(SETTING_VALUE_ON);
        when(globalApolloConfig.getAiChatDsReasonSwitchAbtestExpId()).thenReturn("unit_test_ab_layer_id");
        when(abTestProxy.abTest(any(), any())).thenReturn("A");

        Integer result = deepSeekHandler.queryReasonSwitch("testUid");

        assertEquals(SETTING_VALUE_ON, result);
    }

}
