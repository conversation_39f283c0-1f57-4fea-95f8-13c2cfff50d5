package com.ddmc.chat.common.infra.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import com.ddmc.chat.common.domain.entity.AiChatOpConfig;
import com.ddmc.chat.common.domain.entity.AiChatOpMdTemplate;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LinkHandlerTest {

    @Mock
    private GlobalApolloConfig globalApolloConfig;

    @InjectMocks
    private LinkHandler linkHandler;

    private AiChatOpConfig queryOpConfig;
    private AiChatOpConfig recipeOpConfig;
    private AiChatOpConfig productOpConfig;

    @BeforeEach
    void setUp() {
        Map<String, String> queryAppClientUrlMap = new HashMap<>();
        queryAppClientUrlMap.put("1", "ddxq://product/searchResult?search_key=%s");
        queryAppClientUrlMap.put("2", "ddxq://product/searchResult?search_key=%s");
        AiChatOpMdTemplate queryTemplate = new AiChatOpMdTemplate();
        queryTemplate.setAction("jump");
        queryTemplate.setMdTemplate("[[%s|action=%s,url=%s,type=query]]");
        queryTemplate.setAppClientUrlMap(queryAppClientUrlMap);
        Map<String, AiChatOpMdTemplate> queryActionTemplates = new HashMap<>();
        queryActionTemplates.put("jump", queryTemplate);
        queryOpConfig = new AiChatOpConfig();
        queryOpConfig.setDefaultAction("jump");
        queryOpConfig.setActionTemplates(queryActionTemplates);

        Map<String, String> recipeAppClientUrlMap = new HashMap<>();
        recipeAppClientUrlMap.put("1", "ddxq://rn/entry?entry=CookbookDetail&cookbook_id=%s&pageId=cookbook_detail_new&needLogin=true");
        recipeAppClientUrlMap.put("2", "ddxq://rn/entry?entry=CookbookDetail&cookbook_id=%s&pageId=cookbook_detail_new&needLogin=true");
        AiChatOpMdTemplate recipeTemplate = new AiChatOpMdTemplate();
        recipeTemplate.setAction("jump");
        recipeTemplate.setMdTemplate("[[%s|action=%s,url=%s,type=recipe]]");
        recipeTemplate.setAppClientUrlMap(recipeAppClientUrlMap);
        Map<String, AiChatOpMdTemplate> recipeActionTemplates = new HashMap<>();
        recipeActionTemplates.put("jump", recipeTemplate);
        recipeOpConfig = new AiChatOpConfig();
        recipeOpConfig.setDefaultAction("jump");
        recipeOpConfig.setActionTemplates(recipeActionTemplates);

        Map<String, String> productAppClientUrlMap = new HashMap<>();
        productAppClientUrlMap.put("1", "ddxq://product/detail?id=%s");
        productAppClientUrlMap.put("2", "ddxq://product/detail?id=%s");
        AiChatOpMdTemplate productTemplate = new AiChatOpMdTemplate();
        productTemplate.setAction("jump");
        productTemplate.setMdTemplate("[[%s|action=%s,url=%s,type=product]]");
        productTemplate.setAppClientUrlMap(productAppClientUrlMap);
        Map<String, AiChatOpMdTemplate> productActionTemplates = new HashMap<>();
        productActionTemplates.put("jump", productTemplate);
        productOpConfig = new AiChatOpConfig();
        productOpConfig.setDefaultAction("jump");
        productOpConfig.setActionTemplates(productActionTemplates);
    }

    @Test
    void testConvert_Success() {
        String input = "[[牛奶|type=query]]";
        String appClientId = "1";

        when(globalApolloConfig.getAiChatOpConfig("query")).thenReturn(queryOpConfig);

        String expectedOutput = "[[牛奶|action=jump,url=ddxq%3A%2F%2Fproduct%2FsearchResult%3Fsearch_key%3D%E7%89%9B%E5%A5%B6,type=query]]";
        String result = linkHandler.convert(input, appClientId);

        assertEquals(expectedOutput, result);
    }

    @Test
    void testConvert_Multi_Success() {
        String input = "这个[[牛奶|type=query]]很好喝，还可以用来做[[牛奶蛋糕|type=recipe,id=123]],简直太棒了,推荐喝[[光明牌牛奶|type=product,id=456]]";
        String appClientId = "1";

        when(globalApolloConfig.getAiChatOpConfig("query")).thenReturn(queryOpConfig);
        when(globalApolloConfig.getAiChatOpConfig("recipe")).thenReturn(recipeOpConfig);
        when(globalApolloConfig.getAiChatOpConfig("product")).thenReturn(productOpConfig);

        String expectedOutput = "这个[[牛奶|action=jump,url=ddxq%3A%2F%2Fproduct%2FsearchResult%3Fsearch_key%3D%E7%89%9B%E5%A5%B6,type=query]]很好喝，还可以用来做[[牛奶蛋糕|action=jump,url=ddxq%3A%2F%2Frn%2Fentry%3Fentry%3DCookbookDetail%26cookbook_id%3D123%26pageId%3Dcookbook_detail_new%26needLogin%3Dtrue,type=recipe]],简直太棒了,推荐喝[[光明牌牛奶|action=jump,url=ddxq%3A%2F%2Fproduct%2Fdetail%3Fid%3D456,type=product]]";
        String result = linkHandler.convert(input, appClientId);

        assertEquals(expectedOutput, result);
    }

    @Test
    void testConvert_Input_Valid() {
        String input = "这个[[牛奶|type=query=2]]很好喝，还可以用来做[[牛奶蛋糕|type=recipe,id=123]],简直太棒了";
        String appClientId = "2";

        when(globalApolloConfig.getAiChatOpConfig("query")).thenReturn(queryOpConfig);
        when(globalApolloConfig.getAiChatOpConfig("recipe")).thenReturn(recipeOpConfig);

        String expectedOutput = "这个[[牛奶|type=query=2]]很好喝，还可以用来做[[牛奶蛋糕|type=recipe,id=123]],简直太棒了";
        String result = linkHandler.convert(input, appClientId);

        assertEquals(expectedOutput, result);
    }

    @Test
    void testConvert_Fallback_On() {
        String input = "[[牛奶|type=query]]";
        String appClientId = " 123";

        when(globalApolloConfig.getAiChatOpDegradeSwitch()).thenReturn(1);

        String expectedOutput = "牛奶";
        String result = linkHandler.convert(input, appClientId);

        assertEquals(expectedOutput, result);
    }

    @Test
    void testConvert_NoMatch() {
        String input = "No match here";
        String appClientId = "1";

        String result = linkHandler.convert(input, appClientId);

        assertEquals(input, result);
    }

    @Test
    void testConvert_TemplateNotFound() {
        String input = "[[牛奶|type=query]]";
        String appClientId = "1";

        when(globalApolloConfig.getAiChatOpConfig("query")).thenReturn(null);

        String result = linkHandler.convert(input, appClientId);

        assertEquals(input, result);
    }

    @Test
    void testConvert_Exception() {
        String input = "[[牛奶|type=query]]";
        String appClientId = "1";

        // 模拟异常
        lenient().when(globalApolloConfig.getAiChatOpConfig("query")).thenThrow(new RuntimeException("Test Exception"));

        String result = linkHandler.convert(input, appClientId);

        assertEquals(input, result);
    }

    @Test
    void testGetAttributesMap_Success() {
        String attributesStr = "type=query,id=123";

        Map<String, String> expectedAttributesMap = new HashMap<>();
        expectedAttributesMap.put("type", "query");
        expectedAttributesMap.put("id", "123");

        Map<String, String> result = linkHandler.getAttributesMap(attributesStr);
        assertEquals(expectedAttributesMap, result);
    }

    @Test
    void testGetAttributesMap_Unicode_Success() {
        String attributesStr = "type=query,id=123\uD83D\uDE0A";

        Map<String, String> expectedAttributesMap = new HashMap<>();
        expectedAttributesMap.put("type", "query");
        expectedAttributesMap.put("id", "123\uD83D\uDE0A");

        Map<String, String> result = linkHandler.getAttributesMap(attributesStr);
        assertEquals(expectedAttributesMap, result);
    }

    @Test
    void testGetAttributesMap_BlankAttributesStr() {
        String attributesStr = "";

        Map<String, String> expectedAttributesMap = new HashMap<>();

        Map<String, String> result = linkHandler.getAttributesMap(attributesStr);

        assertEquals(expectedAttributesMap, result);
    }

    @Test
    void testBuildUrl_Query() {

        String displayText = "牛奶";
        String id = "123";

        String expectedUrl = "ddxq://product/searchResult?search_key=牛奶";
        String result = linkHandler.buildUrl("query", displayText, "ddxq://product/searchResult?search_key=%s", id);

        assertEquals(expectedUrl, result);
    }

    @Test
    void testBuildUrl_Recipe() {
        String displayText = "西红柿炒鸡蛋";
        String id = "123";

        String expectedUrl = "ddxq://rn/entry?entry=CookbookDetail&cookbook_id=123&pageId=cookbook_detail_new&needLogin=true";
        String result = linkHandler.buildUrl("recipe", displayText, "ddxq://rn/entry?entry=CookbookDetail&cookbook_id=%s&pageId=cookbook_detail_new&needLogin=true", id);

        assertEquals(expectedUrl, result);
    }

    @Test
    void testBuildUrl_Product() {

        String displayText = "白菜";
        String id = "123";

        String expectedUrl = "ddxq://product/detail?id=123";
        String result = linkHandler.buildUrl("product", displayText, "ddxq://product/detail?id=%s", id);

        assertEquals(expectedUrl, result);
    }

    @Test
    void testEncodeUrl_Success() {
        String url = "ddxq://product/searchResult?search_key=牛奶";

        String expectedEncodedUrl = "ddxq%3A%2F%2Fproduct%2FsearchResult%3Fsearch_key%3D%E7%89%9B%E5%A5%B6";
        String result = linkHandler.encodeUrl(url);

        assertEquals(expectedEncodedUrl, result);
    }

    @Test
    void testEncodeUrl_BlankUrl() {
        String url = "";

        String expectedEncodedUrl = "";
        String result = linkHandler.encodeUrl(url);

        assertEquals(expectedEncodedUrl, result);
    }

}
