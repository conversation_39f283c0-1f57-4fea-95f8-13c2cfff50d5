package com.ddmc.chat.common.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.chat.common.domain.dto.req.feedback.FeedbackReq;
import com.ddmc.chat.common.domain.vo.FeedbackVO;
import com.ddmc.chat.common.mapper.DO.MessageFeedbackDO;
import com.ddmc.chat.common.mapper.MessageFeedbackMapper;
import java.util.List;
import org.apache.logging.log4j.util.Strings;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;


@ExtendWith(MockitoExtension.class)
class FeedBackServiceImplTest {

    @Mock
    private MessageFeedbackMapper feedbackMapper;

    @Spy
    @InjectMocks
    private FeedBackServiceImpl feedBackService;

    private final String positiveChatFeedbackToast = "谢谢您的鼓励，我们会继续加油哦~";

    private final String negativeChatFeedbackToast = "Negative feedback received successfully";

    @BeforeEach
    void setUp() {
        // mock apollo
        ReflectionTestUtils.setField(feedBackService, "positiveChatFeedbackToast", positiveChatFeedbackToast);
        ReflectionTestUtils.setField(feedBackService, "negativeChatFeedbackToast", negativeChatFeedbackToast);
    }

    @Test
    void testFeedBackWithPositiveComment() {
        FeedbackReq feedbackReq = new FeedbackReq();
        feedbackReq.setUid("user123");
        feedbackReq.setQuestId("quest123");
        feedbackReq.setMessageId(12345678L);
        feedbackReq.setComment(1); // Positive comment
        feedbackReq.setDetailComment("Great service!");

        FeedbackVO expectedFeedbackVO = new FeedbackVO(List.of(), positiveChatFeedbackToast);

        feedBackService.feedBack(feedbackReq);

        verify(feedbackMapper, times(1)).insert(any(MessageFeedbackDO.class));
        assertEquals(expectedFeedbackVO.getToast(), positiveChatFeedbackToast);
        assertEquals(0, expectedFeedbackVO.getTags().size());
    }

    @Test
    void testFeedBackWithNegativeComment() {
        FeedbackReq feedbackReq = new FeedbackReq();
        feedbackReq.setUid("user123");
        feedbackReq.setQuestId("quest123");
        feedbackReq.setMessageId(12345678L);
        feedbackReq.setComment(2); // Negative comment
        feedbackReq.setDetailComment("Poor service!");

        FeedbackVO expectedFeedbackVO = new FeedbackVO(List.of("Reason1", "Reason2"), Strings.EMPTY);

        when(feedBackService.feedBack(feedbackReq)).thenReturn(expectedFeedbackVO);

        FeedbackVO actualFeedbackVO = feedBackService.feedBack(feedbackReq);

        verify(feedbackMapper, times(1)).insert(any(MessageFeedbackDO.class));
        assertEquals(Strings.EMPTY, actualFeedbackVO.getToast());
        assertEquals(2, actualFeedbackVO.getTags().size());
    }

    @Test
    void testFeedBack2WithPositiveComment() {
        FeedbackReq feedbackReq = new FeedbackReq();
        feedbackReq.setUid("user123");
        feedbackReq.setQuestId("quest123");
        feedbackReq.setMessageId(12345678L);
        feedbackReq.setComment(1); // Positive comment
        feedbackReq.setDetailComment("Great service!");

        String actualResponse = feedBackService.feedBack2(feedbackReq);

        verify(feedbackMapper, times(1)).insert(any(MessageFeedbackDO.class));
        assertEquals(negativeChatFeedbackToast, actualResponse);
    }

    @Test
    void testFeedBack2WithNegativeComment() {
        FeedbackReq feedbackReq = new FeedbackReq();
        feedbackReq.setUid("user123");
        feedbackReq.setQuestId("quest123");
        feedbackReq.setMessageId(12345678L);
        feedbackReq.setComment(2); // Negative comment
        feedbackReq.setDetailComment("Poor service!");

        String actualResponse = feedBackService.feedBack2(feedbackReq);

        verify(feedbackMapper, times(1)).insert(any(MessageFeedbackDO.class));
        assertEquals(negativeChatFeedbackToast, actualResponse);
    }
}
