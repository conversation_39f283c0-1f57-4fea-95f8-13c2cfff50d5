package com.ddmc.chat.common.infra.handler;

import static com.ddmc.chat.common.constant.CacheKeyConstants.getMessageContextKey;
import static com.ddmc.chat.common.domain.Constants.MSG_CARD_PAGE_ID_RECOMMENDATION;
import static com.ddmc.chat.common.domain.Constants.MSG_CARD_PAGE_ID_SEARCH_RESULT_PAGE;
import static com.ddmc.chat.common.domain.Constants.MSG_CARD_PAGE_ID_TITLE;
import static com.ddmc.chat.common.domain.Constants.MSG_SENDER_ASSISTANT;
import static com.ddmc.chat.common.domain.Constants.MSG_SENDER_SYSTEM;
import static com.ddmc.chat.common.domain.Constants.MSG_SENDER_USER;
import static com.ddmc.chat.common.domain.Constants.MSG_TYPE_TEXT;
import static com.ddmc.chat.common.domain.Constants.REQ_SOURCE_DXD_NEW;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.chat.common.constant.CacheKeyConstants;
import com.ddmc.chat.common.domain.Constants;
import com.ddmc.chat.common.domain.convert.MessageConvert;
import com.ddmc.chat.common.domain.dto.req.ai.AiChatCompletionReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatHistoryReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.StartReq;
import com.ddmc.chat.common.domain.dto.res.sse.AppendContents;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatAppendContents;
import com.ddmc.chat.common.domain.dto.res.sse.chat.ProductItem;
import com.ddmc.chat.common.domain.entity.AiChatNlpParam;
import com.ddmc.chat.common.domain.entity.MessageContext;
import com.ddmc.chat.common.domain.vo.AiChatMessageVO;
import com.ddmc.chat.common.domain.vo.DailyTipsVO;
import com.ddmc.chat.common.domain.vo.MessageBoxVO;
import com.ddmc.chat.common.domain.vo.MessageVO;
import com.ddmc.chat.common.domain.vo.MultimodalContentVO;
import com.ddmc.chat.common.domain.vo.ProductInfoVO;
import com.ddmc.chat.common.enums.MessageTypeEnum;
import com.ddmc.chat.common.infra.config.DeepSeekConfig;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.dao.MessageBoxDAO;
import com.ddmc.chat.common.infra.proxy.ChatContext;
import com.ddmc.chat.common.infra.proxy.SummaryProxy;
import com.ddmc.chat.common.infra.proxy.dto.TextCheckReason;
import com.ddmc.chat.common.mapper.DO.MessageBoxDO;
import com.ddmc.chat.common.util.OUIDGenerator;
import com.ddmc.guide.enhance.json.JsonUtil;
import com.ddmc.guide.enhance.redis.RedisClient;
import com.ddmc.nlp.api.galaxy.entity.LLMChatMessage;
import com.ddmc.nlp.api.galaxy.entity.ProductAssistRequestVo;
import com.ddmc.recipe.searchrec.entity.request.ProductRequestVo;
import com.ddmc.summary.client.dto.PageOldProductInfoDTO;
import com.ddmc.summary.client.dto.ProductInfoOldBO;
import com.ddmc.summary.client.dto.request.ProductListRequest;
import com.ddmc.utils.date.DateUtils;
import com.google.common.collect.Lists;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MessageBoxHandlerTest {

    @Mock
    private RedisClient transformersRedisClient;

    @Mock
    private MessageBoxDAO messageBoxDAO;

    @Mock
    private MessageConvert messageConvert;

    @Mock
    private SummaryProxy summaryProxy;

    @Mock
    private GlobalApolloConfig globalApolloConfig;

    @Spy
    @InjectMocks
    private MessageBoxHandler messageBoxHandler;

    @Mock
    private DeepSeekHandler deepSeekHandler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSaveUnCommitMessage() {
        // Arrange
        String uid = "user1";
        String stationId = "station1";
        String cityCode = "city1";
        String correlatePid = "58b7bac4916edf1f4dc1ec21";
        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("s01jj14cg5bb2bt6yeabjjbdk05p1wr29");
        messageContext.setQuestId("q01jj14cy0sb2bt6yeabjjbdk05p1wr2a");
        messageContext.setMessageId(1L);

        MessageBoxDO expectedDO = new MessageBoxDO();
        expectedDO.setSessionId("s01jj14cg5bb2bt6yeabjjbdk05p1wr29");
        expectedDO.setQuestId("q01jj14cy0sb2bt6yeabjjbdk05p1wr2a");
        expectedDO.setMessageId(1L);
        expectedDO.setOwner(uid);
        expectedDO.setSender(MSG_SENDER_ASSISTANT);
        expectedDO.setStationId(stationId);
        expectedDO.setCityCode(cityCode);
        expectedDO.setContent("");
        expectedDO.setCommitStatus(Constants.MSG_STATUS_UNCOMMIT);
        expectedDO.setCorrelatePid(correlatePid);

        when(messageBoxDAO.insertMessage(any(MessageBoxDO.class))).thenReturn(1);

        // Act
        MessageBoxDO resultDO = messageBoxHandler.saveUnCommitMessage(uid, stationId, cityCode, correlatePid, messageContext);

        // Assert
        assertEquals(expectedDO, resultDO);
    }

    @Test
    void testAppendMessageContent() {
        // Arrange
        Long messageId = 1L;
        String msgContext = "Hello World";

        when(transformersRedisClient.exist(anyString())).thenReturn(true);

        // Act
        messageBoxHandler.appendMessageContent(messageId, msgContext);

        // Assert
        verify(transformersRedisClient).appendStr(eq(getMessageContextKey(messageId)), eq(msgContext));
    }

    @Test
    void testAppendMessageExtra() {
        // Arrange
        MessageVO messageVO = new MessageVO();
        messageVO.setMessageId(1L);
        messageVO.setRecommendProducts(Collections.singletonList(new ProductInfoVO()));

        when(globalApolloConfig.getMessageTempMaxExpireDay()).thenReturn(7L);

        // Act
        messageBoxHandler.appendMessageExtra(messageVO);

        // Assert
        verify(transformersRedisClient).setStr(eq(CacheKeyConstants.getMessageExtraKey(1L)), anyString(), eq(Duration.ofDays(7L)));
    }

    @Test
    void testUpdateMessageAudioFileKey() {
        // Arrange
        String uid = "user1";
        Long messageId = 1L;
        String audioFileKey = "audio1";

        when(messageBoxDAO.updateMessageWithAudioFileKey(uid, messageId, audioFileKey)).thenReturn(true);

        // Act
        boolean result = messageBoxHandler.updateMessageAudioFileKey(uid, messageId, audioFileKey);

        // Assert
        assertTrue(result);
    }

    @Test
    void testSaveDirectMessage() {
        // Arrange
        MessageVO messageVO = new MessageVO();
        messageVO.setRole(MSG_SENDER_USER);

        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("123459876");
        chatQuestionReq.setProductId("58b7bac4916edf1f4dc1ec21");
        chatQuestionReq.setCityNumber("city1");
        chatQuestionReq.setStationId("station1");

        MessageBoxDO messageBoxDO = new MessageBoxDO();
        messageBoxDO.setOwner("123459876");
        messageBoxDO.setCommitStatus(Constants.MSG_STATUS_COMMIT);
        messageBoxDO.setCorrelatePid("58b7bac4916edf1f4dc1ec21");
        messageBoxDO.setCityCode("city1");
        messageBoxDO.setStationId("station1");

        when(messageConvert.messageVO2DO(messageVO)).thenReturn(messageBoxDO);
        when(messageBoxDAO.insertMessage(messageBoxDO)).thenReturn(1);

        // Act
        boolean result = messageBoxHandler.saveDirectMessage(messageVO, chatQuestionReq);

        // Assert
        assertTrue(result);
    }

    @Test
    void testSaveCommitMessage() {
        // Arrange
        Long messageId = System.currentTimeMillis();
        String content = "Hello World";
        String extra = "{\"key\":\"value\"}";

        MessageContext messageContext = new MessageContext();
        messageContext.setMessageId(System.currentTimeMillis());
        messageContext.setUid("58b7bac4916edf1f4dc1ec21");
        messageContext.setSessionId("s123");
        messageContext.setQuestId("q12345");

        MessageBoxDO messageBoxDO = new MessageBoxDO();
        messageBoxDO.setMessageId(messageId);
        messageBoxDO.setCommitStatus(Constants.MSG_STATUS_UNCOMMIT);

        when(messageBoxDAO.selectByMessageIdAndOwner(messageId, messageContext.getUid())).thenReturn(messageBoxDO);
        when(transformersRedisClient.getStr(getMessageContextKey(messageId))).thenReturn(content);
        when(transformersRedisClient.getStr(CacheKeyConstants.getMessageExtraKey(messageId))).thenReturn(extra);
        when(messageBoxDAO.updateMessageByOwnerAndMessageId(any(MessageBoxDO.class))).thenReturn(true);

        // Act
        messageBoxHandler.saveCommitMessage(messageContext);

        // Assert
        assertEquals(content, messageBoxDO.getContent());
        assertEquals(extra, messageBoxDO.getExtraContent());
        assertEquals(Constants.MSG_STATUS_COMMIT, messageBoxDO.getCommitStatus());
        verify(transformersRedisClient).delete(getMessageContextKey(messageId));
        verify(transformersRedisClient).delete(CacheKeyConstants.getMessageExtraKey(messageId));
    }

    @Test
    void testCheckSession() {
        // Arrange
        String sessionId = "s01jhmvmwj0b2pd5heabjhmv98vy02g75";
        MessageContext messageContext = new MessageContext();

        when(transformersRedisClient.exist(CacheKeyConstants.getSessionCacheKey(sessionId))).thenReturn(true);

        // Act
        messageBoxHandler.checkSession(sessionId, messageContext);

        // Assert
        assertEquals(sessionId, messageContext.getSessionId());
    }

    @Test
    void testCreateSessionIdAndRecord() {
        // Arrange
        String sessionId = "s01jhmvmwj0b2pd5heabjhmv98vy02g75";

        try (MockedStatic<OUIDGenerator> mockedStatic = mockStatic(OUIDGenerator.class)) {
            mockedStatic.when(OUIDGenerator::generate).thenReturn(sessionId);
            when(OUIDGenerator.generate("s")).thenReturn(sessionId);
            when(globalApolloConfig.getSessionMaxExpireMinute()).thenReturn(15L);
            // Act
            String resultSessionId = messageBoxHandler.createSessionIdAndRecord();

            // Assert
            assertEquals(sessionId, resultSessionId);
        }
    }

    @Test
    void testRemoveSession_Success() {
        String sessionId = "testSessionId";
        String sessionCacheKey = "chat:session:testSessionId";

        when(transformersRedisClient.exist(sessionCacheKey)).thenReturn(true);
        when(transformersRedisClient.delete(sessionCacheKey)).thenReturn(true);

        boolean result = messageBoxHandler.removeSession(sessionId);

        assertTrue(result);
        verify(transformersRedisClient, times(1)).exist(sessionCacheKey);
        verify(transformersRedisClient, times(1)).delete(sessionCacheKey);
    }

    @Test
    void testRemoveSession_NotExist() {
        String sessionId = "testSessionId";
        String sessionCacheKey = "chat:session:testSessionId";

        when(transformersRedisClient.exist(sessionCacheKey)).thenReturn(false);

        boolean result = messageBoxHandler.removeSession(sessionId);

        assertTrue(result);
        verify(transformersRedisClient, times(1)).exist(sessionCacheKey);
        verify(transformersRedisClient, never()).delete(sessionCacheKey);
    }

    @Test
    void testRemoveSession_Failure() {
        String sessionId = "testSessionId";
        String sessionCacheKey = "chat:session:testSessionId";

        when(transformersRedisClient.exist(sessionCacheKey)).thenReturn(true);
        when(transformersRedisClient.delete(sessionCacheKey)).thenReturn(false);

        boolean result = messageBoxHandler.removeSession(sessionId);

        assertFalse(result);
        verify(transformersRedisClient, times(1)).exist(sessionCacheKey);
        verify(transformersRedisClient, times(1)).delete(sessionCacheKey);
    }

    @Test
    void testGetUserMessageVO_NoCheckReasons() {
        List<TextCheckReason> checkReasons = Collections.emptyList();
        String question = "Hello, how are you?";
        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("testSessionId");
        messageContext.setQuestId(null);

        MessageVO result = messageBoxHandler.getUserMessageVO(checkReasons, question, messageContext);

        assertNotNull(result);
        assertNotNull(result.getQuestId());
        assertNotNull(result.getMessageId());
        assertEquals("testSessionId", result.getSessionId());
        assertEquals(DateUtils.getCurTimestamp(), result.getTime());
        assertEquals(MSG_TYPE_TEXT, result.getType());
        assertEquals(MSG_SENDER_USER, result.getRole());
        assertFalse(result.getEnd());
        assertEquals(question, result.getContent());
    }

    @Test
    void testGetUserMessageVO_WithCheckReasons() {
        List<TextCheckReason> checkReasons = Collections.singletonList(new TextCheckReason());
        String question = "Hello, how are you?";
        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("testSessionId");
        messageContext.setQuestId(null);

        MessageVO result = messageBoxHandler.getUserMessageVO(checkReasons, question, messageContext);

        assertNotNull(result);
        assertNotNull(result.getQuestId());
        assertNotNull(result.getMessageId());
        assertEquals("testSessionId", result.getSessionId());
        assertEquals(DateUtils.getCurTimestamp(), result.getTime());
        assertEquals(MSG_TYPE_TEXT, result.getType());
        assertEquals(MSG_SENDER_USER, result.getRole());
        assertFalse(result.getEnd());
        assertEquals("***********", result.getContent());
    }

    @Test
    void testGetSensitiveMessageVO() {
        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("testSessionId");
        messageContext.setQuestId("testQuestId");

        MessageVO result = messageBoxHandler.getSensitiveMessageVO(messageContext);

        assertNotNull(result);
        assertEquals("testQuestId", result.getQuestId());
        assertNotNull(result.getMessageId());
        assertEquals("testSessionId", result.getSessionId());
        assertEquals(DateUtils.getCurTimestamp(), result.getTime());
        assertEquals(MSG_TYPE_TEXT, result.getType());
        assertEquals(MSG_SENDER_SYSTEM, result.getRole());
        assertTrue(result.getEnd());
        assertEquals(globalApolloConfig.getAntispamPrompt(), result.getContent());
    }

    @Test
    void testGetSegmentMessageVO_WithProductId() {
        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("testSessionId");
        messageContext.setQuestId(null);

        ChatContext chatContext = ChatContext.of(new ChatQuestionReq());
        chatContext.setProductId("testProductId");

        when(summaryProxy.generateSummaryRequest(chatContext, MSG_CARD_PAGE_ID_TITLE, Collections.singletonList("testProductId")))
            .thenReturn(new ProductListRequest());

        PageOldProductInfoDTO pageOldProductInfoDTO = new PageOldProductInfoDTO();
        pageOldProductInfoDTO.setProductInfoBOS(Collections.singletonList(new ProductInfoOldBO()));
        ProductInfoOldBO productInfoOldBO = new ProductInfoOldBO();
        productInfoOldBO.setShortTitle("Test Product");
        pageOldProductInfoDTO.getProductInfoBOS().get(0).setShortTitle("Test Product");

        when(summaryProxy.getProductList(any(ProductListRequest.class)))
            .thenReturn(Collections.singletonList(pageOldProductInfoDTO));
        when(globalApolloConfig.getGoodsSegmentText()).thenReturn("咨询商品：【%s】");

        MessageVO result = messageBoxHandler.getSegmentMessageVO(messageContext, chatContext);

        assertNotNull(result);
        assertNotNull(result.getQuestId());
        assertNotNull(result.getMessageId());
        assertEquals("testSessionId", result.getSessionId());
        assertEquals(DateUtils.getCurTimestamp(), result.getTime());
        assertEquals(MessageTypeEnum.SEGMENT.getCode(), result.getType());
        assertEquals(MSG_SENDER_SYSTEM, result.getRole());
        assertFalse(result.getEnd());
        assertEquals(String.format(globalApolloConfig.getGoodsSegmentText(), "Test Product"), result.getContent());
    }

    @Test
    void testGetSegmentMessageVO_WithoutProductId() {
        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("testSessionId");
        messageContext.setQuestId(null);

        ChatContext chatContext = ChatContext.of(new ChatQuestionReq());
        chatContext.setProductId(null);

        MessageVO result = messageBoxHandler.getSegmentMessageVO(messageContext, chatContext);

        assertNotNull(result);
        assertNotNull(result.getQuestId());
        assertNotNull(result.getMessageId());
        assertEquals("testSessionId", result.getSessionId());
        assertEquals(DateUtils.getCurTimestamp(), result.getTime());
        assertEquals(MessageTypeEnum.SEGMENT.getCode(), result.getType());
        assertEquals(MSG_SENDER_SYSTEM, result.getRole());
        assertFalse(result.getEnd());
        assertEquals("", result.getContent());
    }

    @Test
    void testGetAiTextMessageVO() {
        boolean end = true;
        String content = "Hello, this is an AI message.";
        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("testSessionId");
        messageContext.setQuestId("testQuestId");
        messageContext.setMessageId(12345L);

        MessageVO result = messageBoxHandler.getAiTextMessageVO(end, content, messageContext);

        assertNotNull(result);
        assertEquals("testQuestId", result.getQuestId());
        assertEquals(12345L, result.getMessageId());
        assertEquals("testSessionId", result.getSessionId());
        assertEquals(DateUtils.getCurTimestamp(), result.getTime());
        assertEquals(MSG_TYPE_TEXT, result.getType());
        assertEquals(MSG_SENDER_ASSISTANT, result.getRole());
        assertEquals(end, result.getEnd());
        assertEquals(content, result.getContent());
    }

    @Test
    void testGetAiUrlMessageVO_WithContent() {
        boolean end = true;
        AppendContents appendContents = new AppendContents();
        appendContents.setType("url");
        appendContents.setContent("http://example.com");
        appendContents.setDisplayText("Click here");
        appendContents.setPurpose("testPurpose");

        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("testSessionId");
        messageContext.setQuestId("testQuestId");
        messageContext.setMessageId(12345L);

        MessageVO result = messageBoxHandler.getAiUrlMessageVO(end, appendContents, messageContext);

        assertNotNull(result);
        assertEquals("testQuestId", result.getQuestId());
        assertEquals(12345L, result.getMessageId());
        assertEquals("testSessionId", result.getSessionId());
        assertEquals(DateUtils.getCurTimestamp(), result.getTime());
        assertEquals(MSG_TYPE_TEXT, result.getType());
        assertEquals(MSG_SENDER_ASSISTANT, result.getRole());
        assertEquals(end, result.getEnd());
        assertEquals("<button class=\"btn-link\" data-src=\"http://example.com\" >Click here</button>", result.getContent());
        assertEquals("testPurpose", result.getPurpose());
    }

    @Test
    void testGetAiUrlMessageVO_WithoutContent() {
        boolean end = true;
        AppendContents appendContents = new AppendContents();
        appendContents.setType("url");
        appendContents.setContent("");
        appendContents.setDisplayText("Click here");
        appendContents.setPurpose("testPurpose");

        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("testSessionId");
        messageContext.setQuestId("testQuestId");
        messageContext.setMessageId(12345L);

        MessageVO result = messageBoxHandler.getAiUrlMessageVO(end, appendContents, messageContext);

        assertNotNull(result);
        assertEquals("testQuestId", result.getQuestId());
        assertEquals(12345L, result.getMessageId());
        assertEquals("testSessionId", result.getSessionId());
        assertEquals(DateUtils.getCurTimestamp(), result.getTime());
        assertEquals(MSG_TYPE_TEXT, result.getType());
        assertEquals(MSG_SENDER_ASSISTANT, result.getRole());
        assertEquals(end, result.getEnd());
        assertEquals("<button class=\"btn-link\" >Click here</button>", result.getContent());
        assertEquals("testPurpose", result.getPurpose());
    }

    @Test
    void testGetAiProductCardMessageVO() {
        boolean end = true;
        AppendContents appendContents = new AppendContents();
        appendContents.setType("product");
        appendContents.setContent("[123,456]");
        appendContents.setDisplayText("Product List");
        appendContents.setPurpose("testPurpose");

        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("testSessionId");
        messageContext.setQuestId("testQuestId");
        messageContext.setMessageId(12345L);

        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setStationId("testStationId");
        chatQuestionReq.setCityNumber("testCityNumber");
        chatQuestionReq.setApiVersion("testApiVersion");
        chatQuestionReq.setAppClientId("testAppClientId");
        chatQuestionReq.setNativeVersion("testNativeVersion");
        chatQuestionReq.setDeviceId("testDeviceId");
        chatQuestionReq.setH5Source("testH5Source");
        chatQuestionReq.setLatitude("testLatitude");
        chatQuestionReq.setLongitude("testLongitude");
        chatQuestionReq.setFrom("testFrom");
        chatQuestionReq.setProductId("testProductId");
        chatQuestionReq.setFrontCategoryId("testFrontCategoryId");
        chatQuestionReq.setFrontCategoryName("testFrontCategoryName");

        List<ProductInfoVO> productInfoVOList = Collections.singletonList(new ProductInfoVO());
        when(summaryProxy.getProductInfoCardByProductIds(any(ChatContext.class), eq(MSG_CARD_PAGE_ID_RECOMMENDATION), anyList()))
            .thenReturn(productInfoVOList);

        MessageVO result = messageBoxHandler.getAiProductCardMessageVO(end, appendContents, messageContext, chatQuestionReq);

        assertNotNull(result);
        assertEquals("testQuestId", result.getQuestId());
        assertEquals(12345L, result.getMessageId());
        assertEquals("testSessionId", result.getSessionId());
        //assertEquals(DateUtils.getCurTimestamp(), result.getTime());
        assertEquals(MSG_TYPE_TEXT, result.getType());
        assertEquals(MSG_SENDER_ASSISTANT, result.getRole());
        assertEquals(end, result.getEnd());
        assertNull(result.getContent());
        assertEquals("[123,456]", result.getExtraContent());
        assertEquals(productInfoVOList, result.getRecommendProducts());
        assertEquals("testPurpose", result.getPurpose());
    }

    @Test
    void testGetProductAssistRequestVo() {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setQuestion("What is your name?");
        chatQuestionReq.setUid("testUid");
        chatQuestionReq.setStationId("testStationId");
        chatQuestionReq.setCityNumber("testCityNumber");
        chatQuestionReq.setApiVersion("testApiVersion");
        chatQuestionReq.setAppClientId("testAppClientId");
        chatQuestionReq.setNativeVersion("testNativeVersion");
        chatQuestionReq.setDeviceId("testDeviceId");
        chatQuestionReq.setH5Source("testH5Source");
        chatQuestionReq.setLatitude("testLatitude");
        chatQuestionReq.setLongitude("testLongitude");
        chatQuestionReq.setFrom("testFrom");
        chatQuestionReq.setProductId("testProductId");
        chatQuestionReq.setFrontCategoryId("testFrontCategoryId");
        chatQuestionReq.setFrontCategoryName("testFrontCategoryName");

        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("testSessionId");
        messageContext.setQuestId("testQuestId");

        ProductAssistRequestVo result = messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, false);

        assertNotNull(result);
        assertNotNull(result.getMessageId());
        assertEquals("testSessionId", result.getSessionId());
        assertEquals("testQuestId", result.getQid());
        assertEquals(globalApolloConfig.getToken(), result.getToken());
        assertEquals("What is your name?", result.getQuery());
        assertEquals("testUid", result.getUser());
        assertTrue(result.getStream());
        assertEquals("testStationId", result.getStationId());
        assertEquals("testCityNumber", result.getCityNumber());
        assertEquals("testApiVersion", result.getApiVersion());
        assertEquals("testAppClientId", result.getAppClientId());
        assertEquals("testNativeVersion", result.getNativeVersion());
        assertEquals("testDeviceId", result.getDeviceId());
        assertEquals("testH5Source", result.getH5Source());
        assertEquals("testLatitude", result.getLatitude());
        assertEquals("testLongitude", result.getLongitude());
        assertEquals("testFrom", result.getPageType());
        assertEquals("testProductId", result.getProductMongoId());
        assertEquals("testFrontCategoryId", result.getFrontendCategoryId());
        assertEquals("testFrontCategoryName", result.getFrontendCategoryName());
    }

    @Test
    void testHistory_WithMessages() {
        ChatHistoryReq chatHistoryReq = new ChatHistoryReq();
        chatHistoryReq.setUid("testUid");
        chatHistoryReq.setLimit(10);

        List<MessageBoxDO> messagesPerPage = Collections.singletonList(new MessageBoxDO());
        messagesPerPage.get(0).setMessageId(12345L);
        messagesPerPage.get(0).setSessionId("testSessionId");
        messagesPerPage.get(0).setQuestId("testQuestId");
        messagesPerPage.get(0).setMessageId(12345L);
        messagesPerPage.get(0).setOwner("testOwner");
        messagesPerPage.get(0).setSender(MSG_SENDER_ASSISTANT);
        messagesPerPage.get(0).setStationId("testStationId");
        messagesPerPage.get(0).setCityCode("testCityCode");
        messagesPerPage.get(0).setContent("Hello, this is a test message.");
        messagesPerPage.get(0).setCommitStatus(Constants.MSG_STATUS_COMMIT);
        messagesPerPage.get(0).setCorrelatePid("testCorrelatePid");
        messagesPerPage.get(0).setCreateTime(LocalDateTime.now());
        messagesPerPage.get(0).setUpdateTime(LocalDateTime.now());
        messagesPerPage.get(0).setType(Constants.MSG_TYPE_TEXT);

        List<MessageVO> messageVOS = Collections.singletonList(new MessageVO());
        messageVOS.get(0).setMessageId(12345L);
        messageVOS.get(0).setSessionId("testSessionId");
        messageVOS.get(0).setQuestId("testQuestId");
        messageVOS.get(0).setMessageId(12345L);
        messageVOS.get(0).setContent("Hello, this is a test message.");
        messageVOS.get(0).setType(Constants.MSG_TYPE_TEXT);

        when(messageBoxDAO.selectMessage(any(), any(), any()))
            .thenReturn(messagesPerPage);
        when(messageConvert.messageDO2VO(anyList())).thenReturn(messageVOS);

        MessageBoxVO result = messageBoxHandler.history(chatHistoryReq);

        assertNotNull(result);
        assertEquals("testUid", result.getUid());
        assertFalse(result.getHasMore());
        assertEquals(1, result.getMessageList().size());
        assertEquals(12345L, result.getLastMessageId());
        assertEquals("Hello, this is a test message.", result.getMessageList().get(0).getContent());
    }

    @Test
    void testSyncUnCommitMessage_Success() {
        LocalDateTime localDateTime = LocalDateTime.now().plusDays(-7);
        List<MessageBoxDO> messageBoxDOS = Collections.singletonList(new MessageBoxDO());
        messageBoxDOS.get(0).setMessageId(12345L);
        messageBoxDOS.get(0).setSessionId("testSessionId");
        messageBoxDOS.get(0).setQuestId("testQuestId");
        messageBoxDOS.get(0).setMessageId(12345L);
        messageBoxDOS.get(0).setOwner("testOwner");
        messageBoxDOS.get(0).setSender(MSG_SENDER_ASSISTANT);
        messageBoxDOS.get(0).setStationId("testStationId");
        messageBoxDOS.get(0).setCityCode("testCityCode");
        messageBoxDOS.get(0).setContent("");
        messageBoxDOS.get(0).setCommitStatus(Constants.MSG_STATUS_UNCOMMIT);
        messageBoxDOS.get(0).setCorrelatePid("testCorrelatePid");
        messageBoxDOS.get(0).setCreateTime(LocalDateTime.now());
        messageBoxDOS.get(0).setUpdateTime(LocalDateTime.now());

        when(messageBoxDAO.selectUnCommitMessage(any(), any()))
            .thenReturn(messageBoxDOS);
        when(transformersRedisClient.exist(getMessageContextKey(12345L)))
            .thenReturn(true);
        when(transformersRedisClient.getStr(getMessageContextKey(12345L)))
            .thenReturn("Hello, this is a test message.");
        when(messageBoxDAO.updateMessageByOwnerAndMessageId(any(MessageBoxDO.class)))
            .thenReturn(true);

        int result = messageBoxHandler.syncUnCommitMessage();

        assertEquals(1, result);
        verify(transformersRedisClient, times(1)).delete(getMessageContextKey(12345L));
    }

    @Test
    void testBuildProductAssistRequestVo() {
        AiChatCompletionReq chatQuestionReq = new AiChatCompletionReq();
        chatQuestionReq.setQuestion("What is your name?");
        chatQuestionReq.setUid("testUid");
        chatQuestionReq.setStationId("testStationId");
        chatQuestionReq.setCityNumber("testCityNumber");
        chatQuestionReq.setApiVersion("testApiVersion");
        chatQuestionReq.setAppClientId("testAppClientId");
        chatQuestionReq.setNativeVersion("testNativeVersion");
        chatQuestionReq.setDeviceId("testDeviceId");
        chatQuestionReq.setH5Source("testH5Source");
        chatQuestionReq.setLatitude("testLatitude");
        chatQuestionReq.setLongitude("testLongitude");
        chatQuestionReq.setFrom("testFrom");
        chatQuestionReq.setQid("qid");
        chatQuestionReq.setRefreshAnswer(true);
        chatQuestionReq.setScene("search_result_assistant");

        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("testSessionId");
        messageContext.setQuestId("testQuestId");

        AiChatNlpParam aiChatNlpParam = JsonUtil.toObject("{\n"
            + "        \"scene\": \"search_result_assistant\",\n"
            + "        \"token\": \"3f837cccc28d8e050504da0d\",\n"
            + "        \"pageType\": \"search_result_ds\"\n"
            + "    }", AiChatNlpParam.class);

        when(globalApolloConfig.getAiChatNlpParam("search_result_assistant")).thenReturn(aiChatNlpParam);
        when(deepSeekHandler.queryReasonSwitch("testUid")).thenReturn(1);
        ProductAssistRequestVo result = messageBoxHandler.buildProductAssistRequestVo(chatQuestionReq, messageContext);

        assertNotNull(result);
        assertNotNull(result.getMessageId());
        assertEquals("testSessionId", result.getSessionId());
        assertEquals("3f837cccc28d8e050504da0d", result.getToken());
        assertEquals("What is your name?", result.getSearchQuery());
        assertEquals("testUid", result.getUser());
        assertTrue(result.getStream());
        assertEquals("testStationId", result.getStationId());
        assertEquals("testCityNumber", result.getCityNumber());
        assertEquals("testApiVersion", result.getApiVersion());
        assertEquals("testAppClientId", result.getAppClientId());
        assertEquals("testNativeVersion", result.getNativeVersion());
        assertEquals("testDeviceId", result.getDeviceId());
        assertEquals("testH5Source", result.getH5Source());
        assertEquals("testLatitude", result.getLatitude());
        assertEquals("testLongitude", result.getLongitude());
        assertEquals("search_result_ds", result.getPageType());
        assertEquals("qid", result.getQid());
        assertTrue(result.getRefreshAnswer());
        // Todo 传给算法深度思考开关
        //assertEquals(1, result());
    }

    @Test
    void testBuildAiTextMessageVO() {
        boolean end = true;
        String content = "Hello, this is an AI message.";
        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("testSessionId");
        messageContext.setQuestId("testQuestId");
        messageContext.setMessageId(12345L);

        AiChatMessageVO result = messageBoxHandler.buildTextAiChatMessageVO(end, content, "", "qid", messageContext);

        assertNotNull(result);
        assertEquals("testQuestId", result.getQuestId());
        assertEquals("qid", result.getQid());
        assertEquals(12345L, result.getMessageId());
        assertEquals("testSessionId", result.getSessionId());
        assertEquals(DateUtils.getCurTimestamp(), result.getTime());
        assertEquals(MSG_TYPE_TEXT, result.getType());
        assertEquals(MSG_SENDER_ASSISTANT, result.getRole());
        assertEquals(end, result.getEnd());
        assertEquals(content, result.getContent());
    }

    @Test
    void testBuildAiUrlMessageVO() {
        MessageContext messageContext = MessageContext.builder()
            .questId("quest123")
            .messageId(123456L)
            .sessionId("session123")
            .build();

        AiChatAppendContents appendContents = new AiChatAppendContents();
        appendContents.setType("url");
        appendContents.setContent("https://example.com");
        appendContents.setDisplayText("Click here");
        appendContents.setPurpose("arrival_alert");

        MessageVO expected = new MessageVO();
        expected.setQuestId("quest123");
        expected.setMessageId(123456L);
        expected.setSessionId("session123");
        expected.setTime(DateUtils.getCurTimestamp());
        expected.setType(MSG_TYPE_TEXT);
        expected.setRole(MSG_SENDER_ASSISTANT);
        expected.setEnd(false);
        expected.setContent("<button class=\"btn-link\" data-src=\"https://example.com\" >Click here</button>");
        expected.setPurpose("arrival_alert");

        MessageVO actual = messageBoxHandler.buildAiUrlMessageVO(false, appendContents, messageContext);

        assertNotNull(actual);
        assertEquals(expected.getQuestId(), actual.getQuestId());
        assertEquals(expected.getMessageId(), actual.getMessageId());
        assertEquals(expected.getSessionId(), actual.getSessionId());
        assertEquals(expected.getContent(), actual.getContent());
        assertEquals(expected.getPurpose(), actual.getPurpose());
        assertEquals(expected.getType(), actual.getType());
    }

    @Test
    void testBuildAiUrlMessageVO_ArrivalAlert() {
        MessageContext messageContext = MessageContext.builder()
            .questId("quest123")
            .messageId(123456L)
            .sessionId("session123")
            .build();

        AiChatAppendContents appendContents = new AiChatAppendContents();
        appendContents.setType("url");
        appendContents.setContent("https://example.com");
        appendContents.setDisplayText("Click here");
        appendContents.setPurpose("到货提醒");

        MessageVO expected = new MessageVO();
        expected.setQuestId("quest123");
        expected.setMessageId(123456L);
        expected.setSessionId("session123");
        expected.setTime(DateUtils.getCurTimestamp());
        expected.setType(MSG_TYPE_TEXT);
        expected.setRole(MSG_SENDER_ASSISTANT);
        expected.setEnd(false);
        expected.setContent("<button class=\"btn-link btn-stock-notice\" data-src=\"https://example.com\" >Click here</button>");
        expected.setPurpose("到货提醒");

        MessageVO actual = messageBoxHandler.buildAiUrlMessageVO(false, appendContents, messageContext);

        assertNotNull(actual);
        assertEquals(expected.getQuestId(), actual.getQuestId());
        assertEquals(expected.getMessageId(), actual.getMessageId());
        assertEquals(expected.getSessionId(), actual.getSessionId());
        assertEquals(expected.getContent(), actual.getContent());
        assertEquals(expected.getPurpose(), actual.getPurpose());
        assertEquals(expected.getType(), actual.getType());
    }

    @Test
    void testGetUserImageMessageVO() {
        List<String> checkReasons = Collections.emptyList();
        List<String> imageUrlList = Collections.singletonList("https://example.com/image.jpg");
        MessageContext messageContext = MessageContext.builder()
            .questId("quest123")
            .messageId(123456L)
            .sessionId("session123")
            .build();

        MessageVO expected = new MessageVO();
        expected.setQuestId("quest123");
        expected.setMessageId(123456L);
        expected.setSessionId("session123");
        expected.setTime(DateUtils.getCurTimestamp());
        expected.setType(MessageTypeEnum.IMAGE.getCode());
        expected.setRole(MSG_SENDER_USER);
        expected.setEnd(false);
        expected.setContent("[\"https://example.com/image.jpg\"]");

        MessageVO actual = messageBoxHandler.getUserImageMessageVO(checkReasons, imageUrlList, messageContext);

        assertNotNull(actual);
        assertEquals(expected.getQuestId(), actual.getQuestId());
        assertEquals(expected.getSessionId(), actual.getSessionId());
        assertEquals(expected.getContent(), actual.getContent());
        assertEquals(expected.getType(), actual.getType());
    }

    @Test
    void testGetUserImageMessageVOWithCheckReasons() {
        List<String> checkReasons = Collections.singletonList("sensitive");
        List<String> imageUrlList = Collections.singletonList("https://example.com/image.jpg");
        MessageContext messageContext = MessageContext.builder()
            .questId("quest123")
            .messageId(123456L)
            .sessionId("session123")
            .build();

        MessageVO expected = new MessageVO();
        expected.setQuestId("quest123");
        expected.setMessageId(123456L);
        expected.setSessionId("session123");
        expected.setTime(DateUtils.getCurTimestamp());
        expected.setType(MessageTypeEnum.IMAGE.getCode());
        expected.setRole(MSG_SENDER_USER);
        expected.setEnd(false);
        expected.setContent("[\"https://example.com/default_image.jpg\"]");

        when(globalApolloConfig.getChatDefaultImageUrl()).thenReturn("https://example.com/default_image.jpg");

        MessageVO actual = messageBoxHandler.getUserImageMessageVO(checkReasons, imageUrlList, messageContext);

        assertNotNull(actual);
        assertEquals(expected.getQuestId(), actual.getQuestId());
        assertEquals(expected.getSessionId(), actual.getSessionId());
        assertEquals(expected.getContent(), actual.getContent());
        assertEquals(expected.getType(), actual.getType());
    }

    @Test
    void testBuildAiProductCardMessageVO() {
        MessageContext messageContext = MessageContext.builder()
            .questId("quest123")
            .messageId(123456L)
            .sessionId("session123")
            .build();

        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setStationId("station123");
        chatQuestionReq.setCityNumber("city123");
        chatQuestionReq.setApiVersion("v1");
        chatQuestionReq.setAppClientId("client123");
        chatQuestionReq.setNativeVersion("native123");
        chatQuestionReq.setDeviceId("device123");
        chatQuestionReq.setH5Source("source123");
        chatQuestionReq.setLatitude("30.0");
        chatQuestionReq.setLongitude("120.0");
        chatQuestionReq.setCityName("city_name");
        chatQuestionReq.setFrom("source");
        chatQuestionReq.setProductId("product123");
        chatQuestionReq.setFrontCategoryId("category123");
        chatQuestionReq.setFrontCategoryName("category_name");
        chatQuestionReq.setSearchKeyword("keyword");

        AiChatAppendContents appendContents = new AiChatAppendContents();
        appendContents.setType("product");
        appendContents.setContent("[\"product123\"]");
        appendContents.setDisplayText("Product Card");
        appendContents.setPurpose("recommendation");

        ProductInfoVO productInfoVO = new ProductInfoVO();
        productInfoVO.setId("product123");
        productInfoVO.setProductName("Product Name");
        productInfoVO.setShortTitle("Product Description");

        when(summaryProxy.getProductInfoCardByProductIds(any(), anyString(), anyList()))
            .thenReturn(Collections.singletonList(productInfoVO));

        MessageVO expected = new MessageVO();
        expected.setQuestId("quest123");
        expected.setMessageId(123456L);
        expected.setSessionId("session123");
        expected.setTime(DateUtils.getCurTimestamp());
        expected.setType(MessageTypeEnum.TEXT.getCode());
        expected.setRole(MSG_SENDER_ASSISTANT);
        expected.setEnd(false);
        expected.setPurpose("recommendation");
        expected.setRecommendProducts(Collections.singletonList(productInfoVO));

        MessageVO actual = messageBoxHandler.buildAiProductCardMessageVO(false, appendContents, messageContext, chatQuestionReq);

        assertNotNull(actual);
        assertEquals(expected.getQuestId(), actual.getQuestId());
        assertEquals(expected.getSessionId(), actual.getSessionId());
        assertEquals(expected.getContent(), actual.getContent());
        assertEquals(expected.getType(), actual.getType());
        assertNotNull(actual.getRecommendProducts());
        assertEquals(expected.getRecommendProducts(), actual.getRecommendProducts());
        assertEquals(expected.getRecommendProducts().get(0).getProductName(), actual.getRecommendProducts().get(0).getProductName());

    }

    @Test
    void testBuildAiQuestionMessageVO() {
        MessageContext messageContext = MessageContext.builder()
            .questId("quest123")
            .messageId(123456L)
            .sessionId("session123")
            .build();

        AiChatAppendContents appendContents = new AiChatAppendContents();
        appendContents.setType("question");
        appendContents.setContent("[\"question1\", \"question2\"]");
        appendContents.setDisplayText("Questions");
        appendContents.setPurpose("quick_questions");

        MessageVO expected = new MessageVO();
        expected.setQuestId("quest123");
        expected.setMessageId(123456L);
        expected.setSessionId("session123");
        expected.setTime(DateUtils.getCurTimestamp());
        expected.setType(MessageTypeEnum.TEXT.getCode());
        expected.setRole(MSG_SENDER_ASSISTANT);
        expected.setEnd(false);
        expected.setPurpose("quick_questions");
        expected.setQuickQuestionList(Arrays.asList("question1", "question2"));

        MessageVO actual = messageBoxHandler.buildAiQuestionMessageVO(false, appendContents, messageContext);

        assertNotNull(actual);
        assertEquals(expected.getQuestId(), actual.getQuestId());
        assertEquals(expected.getSessionId(), actual.getSessionId());
        assertEquals(expected.getContent(), actual.getContent());
        assertEquals(expected.getType(), actual.getType());
    }

    @Test
    void testProcessMultimodalParamWithNoImages() {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setImageUrls(Collections.emptyList());

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();

        messageBoxHandler.processMultimodalParam(productAssistRequestVo, chatQuestionReq);

        List<LLMChatMessage> messages = productAssistRequestVo.getMessages();
        assertEquals(1, messages.size());

        LLMChatMessage message = messages.get(0);
        assertEquals("user", message.getRole());
        assertEquals("Hello, how are you?", message.getContent());
    }

    @Test
    void testProcessMultimodalParamWithImages() {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setImageUrls(Collections.singletonList("https://example.com/image.jpg"));

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();

        messageBoxHandler.processMultimodalParam(productAssistRequestVo, chatQuestionReq);

        List<LLMChatMessage> messages = productAssistRequestVo.getMessages();
        assertEquals(1, messages.size());

        LLMChatMessage message = messages.get(0);
        assertEquals("user", message.getRole());
        assertEquals(2, ((List<?>) message.getContent()).size());

        List<MultimodalContentVO> contentVOList = (List<MultimodalContentVO>) message.getContent();

        MultimodalContentVO textContentVO = contentVOList.get(0);
        assertEquals("text", textContentVO.getType());
        assertEquals("Hello, how are you?", textContentVO.getText());

        MultimodalContentVO imageContentVO = contentVOList.get(1);
        assertEquals("image_url", imageContentVO.getType());
        assertEquals("https://example.com/image.jpg", imageContentVO.getImageUrl().getUrl());
    }

    @Test
    void isProcessMultimodalParam_FromEqualsReqSourceDxdNew_ReturnsTrue() {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setFrom(REQ_SOURCE_DXD_NEW);
        chatQuestionReq.setImageUrls(Collections.emptyList());

        assertTrue(messageBoxHandler.isProcessMultimodalParam(chatQuestionReq));
    }

    @Test
    void isProcessMultimodalParam_FromEqualsReqSourceDxdNewAndImageUrlsNotEmpty_ReturnsTrue() {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setFrom(REQ_SOURCE_DXD_NEW);
        chatQuestionReq.setImageUrls(Collections.singletonList("http://example.com/image.jpg"));

        assertTrue(messageBoxHandler.isProcessMultimodalParam(chatQuestionReq));
    }

    @Test
    void isProcessMultimodalParam_FromNotEqualsReqSourceDxdNewAndImageUrlsNotEmpty_ReturnsTrue() {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setFrom("someOtherSource");
        chatQuestionReq.setImageUrls(Collections.singletonList("http://example.com/image.jpg"));

        assertTrue(messageBoxHandler.isProcessMultimodalParam(chatQuestionReq));
    }

    @Test
    void isProcessMultimodalParam_FromNotEqualsReqSourceDxdNewAndImageUrlsEmpty_ReturnsFalse() {
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setFrom("someOtherSource");
        chatQuestionReq.setImageUrls(Collections.emptyList());

        assertFalse(messageBoxHandler.isProcessMultimodalParam(chatQuestionReq));
    }

    @Test
    void testBuildAiProductCardMessageVO_WithProductIds() {
        // Arrange
        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("sessionId1");
        messageContext.setQuestId("questId1");
        messageContext.setMessageId(1L);

        AiChatCompletionReq chatQuestionReq = new AiChatCompletionReq();
        chatQuestionReq.setUid("uid1");
        chatQuestionReq.setStationId("station1");
        chatQuestionReq.setCityNumber("city1");
        chatQuestionReq.setApiVersion("apiVersion1");
        chatQuestionReq.setAppClientId("appClientId1");
        chatQuestionReq.setNativeVersion("nativeVersion1");
        chatQuestionReq.setDeviceId("deviceId1");
        chatQuestionReq.setH5Source("h5Source1");
        chatQuestionReq.setLatitude("latitude1");
        chatQuestionReq.setLongitude("longitude1");
        chatQuestionReq.setFrom("from1");

        AiChatAppendContents appendContents = new AiChatAppendContents();
        appendContents.setType("product");
        appendContents.setContent("[\"productId1\", \"productId2\"]");
        appendContents.setDisplayText("");
        appendContents.setPurpose("商卡");

        ProductItem item1 = new ProductItem();
        item1.setPMongoId("productId1");
        item1.setScm("{\"algo_id\":\"zdl_202504231355-0bffbd3943914afb80d54997fbc1cddc\",\"recall_type\":\"op\",\"scene\":\"ai_search\"}");
        ProductItem item2 = new ProductItem();
        item2.setPMongoId("productId2");
        item2.setScm("{\"algo_id\":\"zdl_202504231355-0bffbd3943914afb80d54997fbc1cddc\",\"recall_type\":\"op\",\"scene\":\"ai_search\"}");
        List<ProductItem> itemList = new ArrayList<>();
        itemList.add(item1);
        itemList.add(item2);
        appendContents.setItemList(itemList);

        appendContents.setItemList(itemList);

        ProductInfoVO productInfoVO1 = new ProductInfoVO();
        productInfoVO1.setId("productId1");
        productInfoVO1.setStockNumber(1);
        productInfoVO1.setShortTitle("Short Title 1");

        ProductInfoVO productInfoVO2 = new ProductInfoVO();
        productInfoVO2.setId("productId2");
        productInfoVO2.setStockNumber(1);
        productInfoVO2.setShortTitle("Short Title 2");

        when(summaryProxy.getProductInfoCardByProductIds(any(), eq(MSG_CARD_PAGE_ID_SEARCH_RESULT_PAGE), any()))
            .thenReturn(Lists.newArrayList(productInfoVO1, productInfoVO2));
        when(globalApolloConfig.getDeepSeekConfig()).thenReturn(JsonUtil.toObject("{\n"
            + "    \"reasonDefaultSwitch\": 1,\n"
            + "    \"modelNameMap\": {\n"
            + "        \"0\": \"DeepSeek\",\n"
            + "        \"1\": \"DeepSeek-R1\",\n"
            + "        \"-1\": \"DeepSeek\"\n"
            + "    },\n"
            + "    \"productHomePageShowTotalNumber\": 3,\n"
            + "    \"productTotalNumber\": 3,\n"
            + "    \"tips\": \"你好，很高新为你服务！当前内容是根据你的搜索词生成；该功能目前处于测试期，生成内容仅供参考，希望能获得你的反馈～\"\n"
            + "}", DeepSeekConfig.class));
        //when(globalApolloConfig.getDeepSeekConfig().getProductHomePageShowTotalNumber()).thenReturn(3);

        // Act
        AiChatMessageVO result = messageBoxHandler.buildAiProductCardMessageVO(false, appendContents, messageContext, chatQuestionReq);

        // Assert
        assertEquals("questId1", result.getQuestId());
        assertEquals(1L, result.getMessageId().longValue());
        assertEquals("sessionId1", result.getSessionId());
        assertFalse(result.getEnd());
        assertEquals(MessageTypeEnum.GOODS_CARD.getCode(), result.getType());
        assertEquals(MSG_SENDER_ASSISTANT, result.getRole());
        assertEquals(2, result.getRecommendProducts().size());
        assertEquals("Short Title 1", result.getRecommendProducts().get(0).getShortTitle());
        assertEquals("Short Title 2", result.getRecommendProducts().get(1).getShortTitle());
    }

    @Test
    void testBuildAiProductCardMessageVO_WithoutProductIds() {
        // Arrange
        MessageContext messageContext = new MessageContext();
        messageContext.setSessionId("sessionId1");
        messageContext.setQuestId("questId1");
        messageContext.setMessageId(1L);

        AiChatCompletionReq chatQuestionReq = new AiChatCompletionReq();
        chatQuestionReq.setUid("uid1");
        chatQuestionReq.setStationId("station1");
        chatQuestionReq.setCityNumber("city1");
        chatQuestionReq.setApiVersion("apiVersion1");
        chatQuestionReq.setAppClientId("appClientId1");
        chatQuestionReq.setNativeVersion("nativeVersion1");
        chatQuestionReq.setDeviceId("deviceId1");
        chatQuestionReq.setH5Source("h5Source1");
        chatQuestionReq.setLatitude("latitude1");
        chatQuestionReq.setLongitude("longitude1");
        chatQuestionReq.setFrom("from1");

        AiChatAppendContents appendContents = new AiChatAppendContents();
        appendContents.setType("product");
        appendContents.setContent("[\"productId1\", \"productId2\"]");
        appendContents.setDisplayText("");
        appendContents.setPurpose("商卡");

        ProductInfoVO productInfoVO1 = new ProductInfoVO();
        productInfoVO1.setId("productId1");
        productInfoVO1.setStockNumber(1);
        productInfoVO1.setShortTitle("Short Title 1");

        ProductInfoVO productInfoVO2 = new ProductInfoVO();
        productInfoVO2.setId("productId2");
        productInfoVO2.setStockNumber(1);
        productInfoVO2.setShortTitle("Short Title 2");

        // Act
        AiChatMessageVO result = messageBoxHandler.buildAiProductCardMessageVO(false, appendContents, messageContext, chatQuestionReq);

        // Assert
        assertEquals("questId1", result.getQuestId());
        assertEquals(1L, result.getMessageId().longValue());
        assertEquals("sessionId1", result.getSessionId());
        assertFalse(result.getEnd());
        assertEquals(MessageTypeEnum.GOODS_CARD.getCode(), result.getType());
        assertEquals(MSG_SENDER_ASSISTANT, result.getRole());
        assertEquals(0, result.getRecommendProducts().size());
    }

    @Test
    void buildProductRequestVo_ValidInput_ShouldReturnCorrectProductRequestVo() {
        AiChatCompletionReq chatQuestionReq = new AiChatCompletionReq();
        chatQuestionReq.setUid("uid123");
        chatQuestionReq.setStationId("station123");
        chatQuestionReq.setCityNumber("city456");
        chatQuestionReq.setQuestion("test question");
        chatQuestionReq.setQid("qid789");
        chatQuestionReq.setAppClientId("client101");
        chatQuestionReq.setApiVersion("v1");
        chatQuestionReq.setAiParam("{\"intentionInfo\":{\"intentionWord\":\"testWord\"}}");

        MessageContext messageContext = new MessageContext();

        when(deepSeekHandler.queryReasonSwitch("uid123")).thenReturn(1);

        ProductRequestVo productRequestVo = messageBoxHandler.buildProductRequestVo(chatQuestionReq, messageContext);

        assertEquals(chatQuestionReq.getStationId(), productRequestVo.getStationId());
        assertEquals(chatQuestionReq.getCityNumber(), productRequestVo.getCityNumber());
        assertEquals(chatQuestionReq.getQid(), productRequestVo.getQid());
        assertEquals(chatQuestionReq.getAppClientId(), productRequestVo.getCommonParam().getAppClientId());
        assertEquals(chatQuestionReq.getApiVersion(), productRequestVo.getCommonParam().getApiVersion());
        // Todo 传给算法深度思考开关
        //assertEquals(1, productRequestVo.getCommonParam().get());
    }

    @Test
    void buildProductRequestVo_EmptyAiParam_NoIntentionInfo() {
        AiChatCompletionReq chatQuestionReq = new AiChatCompletionReq();
        chatQuestionReq.setUid("uid123");
        chatQuestionReq.setStationId("station123");
        chatQuestionReq.setCityNumber("city456");
        chatQuestionReq.setQuestion("test question");
        chatQuestionReq.setQid("qid789");
        chatQuestionReq.setAppClientId("client101");
        chatQuestionReq.setApiVersion("v1");

        MessageContext messageContext = new MessageContext();

        when(deepSeekHandler.queryReasonSwitch("uid123")).thenReturn(0);

        ProductRequestVo productRequestVo = messageBoxHandler.buildProductRequestVo(chatQuestionReq, messageContext);

        assertEquals(chatQuestionReq.getStationId(), productRequestVo.getStationId());
        assertEquals(chatQuestionReq.getCityNumber(), productRequestVo.getCityNumber());
        assertEquals(chatQuestionReq.getQuestion(), productRequestVo.getQuestion());
        assertEquals(chatQuestionReq.getQid(), productRequestVo.getQid());
        assertEquals(chatQuestionReq.getAppClientId(), productRequestVo.getCommonParam().getAppClientId());
        assertEquals(chatQuestionReq.getApiVersion(), productRequestVo.getCommonParam().getApiVersion());
        //assertEquals(0, productRequestVo.getCommonParam().get());
    }

    @Test
    void buildProductRequestVo_Composite_Scene() {
        AiChatCompletionReq chatQuestionReq = new AiChatCompletionReq();
        chatQuestionReq.setUid("uid123");
        chatQuestionReq.setStationId("station123");
        chatQuestionReq.setCityNumber("city456");
        chatQuestionReq.setQuestion("test question");
        chatQuestionReq.setQid("qid789");
        chatQuestionReq.setAppClientId("client101");
        chatQuestionReq.setApiVersion("v1");
        chatQuestionReq.setScene("search_result_assistant_composite");

        MessageContext messageContext = new MessageContext();

        when(deepSeekHandler.queryReasonSwitch("uid123")).thenReturn(0);

        ProductRequestVo productRequestVo = messageBoxHandler.buildProductRequestVo(chatQuestionReq, messageContext);

        assertEquals(chatQuestionReq.getStationId(), productRequestVo.getStationId());
        assertEquals(chatQuestionReq.getCityNumber(), productRequestVo.getCityNumber());
        assertEquals(chatQuestionReq.getQuestion(), productRequestVo.getQuestion());
        assertEquals(chatQuestionReq.getQid(), productRequestVo.getQid());
        assertEquals(chatQuestionReq.getAppClientId(), productRequestVo.getCommonParam().getAppClientId());
        assertEquals(chatQuestionReq.getApiVersion(), productRequestVo.getCommonParam().getApiVersion());
        assertEquals("common", productRequestVo.getScene());
    }

    @Test
    void saveDailyTips_SuccessfulSave_ReturnsTrue() {
        DailyTipsVO dailyTipsVO = new DailyTipsVO("Content", "2023-10-01", "Lunar Date", System.currentTimeMillis());
        StartReq startReq = new StartReq("123", "Category Name", "Category ID", "Keyword", 10, new HashMap<>());
        String sessionId = "session123";

        MessageVO messageVO = new MessageVO();
        messageVO.setQuestId("q123");
        messageVO.setMessageId(123456L);
        messageVO.setSessionId(sessionId);
        messageVO.setEnd(true);
        messageVO.setContent(
            "{\"content\":\"Content\",\"date\":\"2023-10-01\",\"lunarDate\":\"Lunar Date\"}");
        messageVO.setTime("2023-10-01T00:00:00Z");
        messageVO.setType(1);
        messageVO.setRole("assistant");

        MessageBoxDO messageBoxDO = new MessageBoxDO();
        messageBoxDO.setQuestId("q123");
        messageBoxDO.setMessageId(123456L);
        messageBoxDO.setSessionId(sessionId);
        messageBoxDO.setContent(
            "{\"content\":\"Content\",\"date\":\"2023-10-01\",\"lunarDate\":\"Lunar Date\"}");
        messageBoxDO.setCreateTime(null);
        messageBoxDO.setUpdateTime(null);
        messageBoxDO.setOwner("user123");
        messageBoxDO.setCommitStatus(1);
        messageBoxDO.setCorrelatePid("123");
        messageBoxDO.setCityCode("1001");
        messageBoxDO.setStationId("station123");

        when(messageConvert.messageVO2DO(any(MessageVO.class))).thenReturn(messageBoxDO);
        when(messageBoxDAO.insertMessage(any(MessageBoxDO.class))).thenReturn(1);

        boolean result = messageBoxHandler.saveDailyTips(dailyTipsVO, sessionId, System.currentTimeMillis(), startReq);

        assertTrue(result);
        verify(messageBoxDAO, times(1)).insertMessage(any(MessageBoxDO.class));
    }

    @Test
    void saveDailyTips_NullProductId_CorrelatePidNotSet() {
        DailyTipsVO dailyTipsVO = new DailyTipsVO("Content", "2023-10-01", "Lunar Date", System.currentTimeMillis());
        StartReq startReq = new StartReq("123", "Category Name", "Category ID", "Keyword", 10, new HashMap<>());

        String sessionId = "session123";

        MessageVO messageVO = new MessageVO();
        messageVO.setQuestId("q123");
        messageVO.setMessageId(123456L);
        messageVO.setSessionId(sessionId);
        messageVO.setEnd(true);
        messageVO.setContent(
            "{\"content\":\"Content\",\"date\":\"2023-10-01\",\"lunarDate\":\"Lunar Date\"}");
        messageVO.setTime("2023-10-01T00:00:00Z");
        messageVO.setType(1);
        messageVO.setRole("assistant");

        MessageBoxDO messageBoxDO = new MessageBoxDO();
        messageBoxDO.setQuestId("q123");
        messageBoxDO.setMessageId(123456L);
        messageBoxDO.setSessionId(sessionId);
        messageBoxDO.setContent(
            "{\"content\":\"Content\",\"date\":\"2023-10-01\",\"lunarDate\":\"Lunar Date\"}");
        messageBoxDO.setCreateTime(null);
        messageBoxDO.setUpdateTime(null);
        messageBoxDO.setOwner("user123");
        messageBoxDO.setCommitStatus(1);
        messageBoxDO.setCorrelatePid(null);
        messageBoxDO.setCityCode("1001");
        messageBoxDO.setStationId("station123");

        when(messageConvert.messageVO2DO(any(MessageVO.class))).thenReturn(messageBoxDO);
        when(messageBoxDAO.insertMessage(any(MessageBoxDO.class))).thenReturn(1);

        boolean result = messageBoxHandler.saveDailyTips(dailyTipsVO, sessionId, System.currentTimeMillis(), startReq);

        assertTrue(result);
        verify(messageBoxDAO, times(1)).insertMessage(any(MessageBoxDO.class));
    }

    @Test
    void saveDailyTips_InsertionFails_ReturnsFalse() {
        DailyTipsVO dailyTipsVO = new DailyTipsVO("Content", "2023-10-01", "Lunar Date", System.currentTimeMillis());
        StartReq startReq = new StartReq("123", "Category Name", "Category ID", "Keyword", 10, new HashMap<>());

        String sessionId = "session123";

        MessageVO messageVO = new MessageVO();
        messageVO.setQuestId("q123");
        messageVO.setMessageId(123456L);
        messageVO.setSessionId(sessionId);
        messageVO.setEnd(true);
        messageVO.setContent(
            "{\"content\":\"Content\",\"date\":\"2023-10-01\",\"lunarDate\":\"Lunar Date\"}");
        messageVO.setTime("2023-10-01T00:00:00Z");
        messageVO.setType(1);
        messageVO.setRole("assistant");

        MessageBoxDO messageBoxDO = new MessageBoxDO();
        messageBoxDO.setQuestId("q123");
        messageBoxDO.setMessageId(123456L);
        messageBoxDO.setSessionId(sessionId);
        messageBoxDO.setContent(
            "{\"content\":\"Content\",\"date\":\"2023-10-01\",\"lunarDate\":\"Lunar Date\"}");
        messageBoxDO.setCreateTime(null);
        messageBoxDO.setUpdateTime(null);
        messageBoxDO.setOwner("user123");
        messageBoxDO.setCommitStatus(1);
        messageBoxDO.setCorrelatePid("123");
        messageBoxDO.setCityCode("1001");
        messageBoxDO.setStationId("station123");

        when(messageConvert.messageVO2DO(any(MessageVO.class))).thenReturn(messageBoxDO);
        when(messageBoxDAO.insertMessage(any(MessageBoxDO.class))).thenReturn(0);

        boolean result = messageBoxHandler.saveDailyTips(dailyTipsVO, sessionId, System.currentTimeMillis(), startReq);

        assertFalse(result);
        verify(messageBoxDAO, times(1)).insertMessage(any(MessageBoxDO.class));
    }

    @Test
    void processScene_SceneProvided_ReturnsScene() {
        String result1 = messageBoxHandler.processScene("detail", "detail");
        assertEquals("detail", result1);
        String result2 = messageBoxHandler.processScene("category", "category");
        assertEquals("category", result2);
        String result3 = messageBoxHandler.processScene("search_result", "search_result");
        assertEquals("search_result", result3);
        String result4 = messageBoxHandler.processScene("dxd_new", "review");
        assertEquals("review", result4);
    }

    @Test
    void processScene_SceneNotProvidedFromDxdNew_ReturnsHomePageScene() {
        String result1 = messageBoxHandler.processScene(Constants.REQ_SOURCE_DXD_NEW, "");
        assertEquals(Constants.COMPLETIONS_HOME_PAGE_SCENE, result1);
        String result2 = messageBoxHandler.processScene("detail", "");
        assertEquals("detail", result2);
    }
}
