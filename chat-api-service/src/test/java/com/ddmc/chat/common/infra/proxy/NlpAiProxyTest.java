package com.ddmc.chat.common.infra.proxy;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.ddmc.chat.common.constant.Constans;
import com.ddmc.chat.common.domain.dto.req.ai.AiChatCompletionReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.StartReq;
import com.ddmc.chat.common.domain.dto.res.sse.AiData;
import com.ddmc.chat.common.domain.dto.res.sse.AiMessage;
import com.ddmc.chat.common.domain.dto.res.sse.AppendContents;
import com.ddmc.chat.common.domain.dto.res.sse.Choice;
import com.ddmc.chat.common.domain.dto.res.sse.Output;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatAppendContents;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatChoice;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatData;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatDelta;
import com.ddmc.chat.common.domain.entity.MessageContext;
import com.ddmc.chat.common.domain.vo.DailyTipsVO;
import com.ddmc.chat.common.domain.vo.MessageVO;
import com.ddmc.chat.common.domain.vo.MultimodalContentVO;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.handler.LinkHandler;
import com.ddmc.chat.common.infra.handler.MessageBoxHandler;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.guide.enhance.json.JsonUtil;
import com.ddmc.nlp.api.galaxy.client.NLPApiClient;
import com.ddmc.nlp.api.galaxy.client.ProductAssistSseClient;
import com.ddmc.nlp.api.galaxy.entity.LLMChatMessage;
import com.ddmc.nlp.api.galaxy.entity.ProductAssistDailyKnowledge;
import com.ddmc.nlp.api.galaxy.entity.ProductAssistDietDailyKnowledgeRequestVo;
import com.ddmc.nlp.api.galaxy.entity.ProductAssistDietResponseVo;
import com.ddmc.nlp.api.galaxy.entity.ProductAssistPossibleQRequestVo;
import com.ddmc.nlp.api.galaxy.entity.ProductAssistPossibleQResponseVo;
import com.ddmc.nlp.api.galaxy.entity.ProductAssistRequestVo;
import com.ddmc.nlp.api.galaxy.entity.ProductAssistResponseVo;
import com.ddmc.nlp.api.galaxy.entity.ProductAssistResultCallback;
import com.google.common.collect.Lists;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@RunWith(PowerMockRunner.class)
@PrepareForTest({NlpAiProxy.class, MonitorUtil.class})
class NlpAiProxyTest {
    @Mock
    private ProductAssistSseClient sseClient;

    @Mock
    private NLPApiClient nlpApiClient;

    @Mock
    private MessageBoxHandler messageBoxHandler;

    @Mock
    private GlobalApolloConfig globalApolloConfig;

    @Mock
    private LinkHandler linkHandler;

    @InjectMocks
    private NlpAiProxy nlpAiProxy;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testStopStream_Success() {
        // Arrange
        String messageId = "testMessageId";
        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setMessageId(messageId);

        ProductAssistResponseVo productAssistResponseVo = new ProductAssistResponseVo();
        productAssistResponseVo.setCode(0);

        when(sseClient.stopStream(productAssistRequestVo)).thenReturn(productAssistResponseVo);

        // Act
        Boolean result = nlpAiProxy.stopStream(messageId);

        // Assert
        assertTrue(result);
    }

    @Test
    void testStopStream_Failure() {
        // Arrange
        String messageId = "testMessageId";
        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setMessageId(messageId);

        when(sseClient.stopStream(productAssistRequestVo)).thenThrow(new RuntimeException("Test exception"));

        // Act
        Boolean result = nlpAiProxy.stopStream(messageId);

        // Assert
        assertFalse(result);
    }

    @Test
    void testGetFirstChatStartMessage_Success() {
        // Arrange
        ChatContext chatContext = new ChatContext();
        chatContext.setUid("testUid");
        chatContext.setReferPage("testReferPage");
        chatContext.setNativeVersion("testNativeVersion");
        chatContext.setProductId("testProductId");
        chatContext.setFrontCategoryName("testFrontCategoryName");
        chatContext.setFrontCategoryId("testFrontCategoryId");
        chatContext.setSearchKeyword("");
        chatContext.setSearchResultCnt(1);
        chatContext.setCityName("上海市");
        chatContext.setStationId("stationId");

        String sessionId = "testSessionId";

        ProductAssistPossibleQRequestVo productAssistRequestVo = new ProductAssistPossibleQRequestVo();
        productAssistRequestVo.setUser(chatContext.getUid());
        productAssistRequestVo.setPageType(chatContext.getReferPage());
        productAssistRequestVo.setNativeVersion(chatContext.getNativeVersion());
        productAssistRequestVo.setProductMongoId(chatContext.getProductId());
        productAssistRequestVo.setFrontendCategoryName(chatContext.getFrontCategoryName());
        productAssistRequestVo.setFrontendCategoryId(chatContext.getFrontCategoryId());
        productAssistRequestVo.setSearchQuery("");
        productAssistRequestVo.setSearchResultsCount(1);
        productAssistRequestVo.setCityName("上海市");
        productAssistRequestVo.setTimestamp(System.currentTimeMillis());
        productAssistRequestVo.setStationId("stationId");

        ProductAssistPossibleQResponseVo productAssistPossibleQResponseVo = new ProductAssistPossibleQResponseVo();
        productAssistPossibleQResponseVo.setCode(0);
        productAssistPossibleQResponseVo.setGreeting("Welcome!");
        productAssistPossibleQResponseVo.setKeywords(Arrays.asList("keyword1", "keyword2"));
        productAssistPossibleQResponseVo.setQuestions(Arrays.asList("question1", "question2"));

        when(nlpApiClient.getPossibleQuestion(any(ProductAssistPossibleQRequestVo.class))).thenReturn(productAssistPossibleQResponseVo);

        // Act
        Triple<String, List<String>, List<String>> result = nlpAiProxy.getFirstChatStartMessage(chatContext, new StartReq());

        // Assert
        assertNotNull(result);
        assertEquals("Welcome!", result.getLeft());
        assertEquals(Arrays.asList("keyword1", "keyword2"), result.getMiddle());
        assertEquals(Arrays.asList("question1", "question2"), result.getRight());
    }

    @Test
    void testGetFirstChatStartMessage_Failure() {
        // Arrange
        ChatContext chatContext = new ChatContext();
        chatContext.setUid("testUid");
        chatContext.setReferPage("testReferPage");
        chatContext.setNativeVersion("testNativeVersion");
        chatContext.setProductId("testProductId");
        chatContext.setFrontCategoryName("testFrontCategoryName");
        chatContext.setFrontCategoryId("testFrontCategoryId");

        String sessionId = "testSessionId";

        ProductAssistPossibleQRequestVo productAssistRequestVo = new ProductAssistPossibleQRequestVo();
        productAssistRequestVo.setUser(chatContext.getUid());
        productAssistRequestVo.setPageType(chatContext.getReferPage());
        productAssistRequestVo.setNativeVersion(chatContext.getNativeVersion());
        productAssistRequestVo.setProductMongoId(chatContext.getProductId());
        productAssistRequestVo.setFrontendCategoryName(chatContext.getFrontCategoryName());
        productAssistRequestVo.setFrontendCategoryId(chatContext.getFrontCategoryId());

        when(nlpApiClient.getPossibleQuestion(productAssistRequestVo)).thenThrow(new RuntimeException("Test exception"));

        // Act
        Triple<String, List<String>, List<String>> result = nlpAiProxy.getFirstChatStartMessage(chatContext, new StartReq());

        // Assert
        assertNotNull(result);
        assertNull(result.getLeft());
        assertNull(result.getMiddle());
        assertNull(result.getRight());
    }

    @Test
    void testGetFirstChatStartMessage_FallbackOn() {
        // Arrange
        ChatContext chatContext = new ChatContext();
        chatContext.setUid("testUid");
        chatContext.setReferPage("testReferPage");
        chatContext.setNativeVersion("testNativeVersion");
        chatContext.setProductId("testProductId");
        chatContext.setFrontCategoryName("testFrontCategoryName");
        chatContext.setFrontCategoryId("testFrontCategoryId");

        String sessionId = "testSessionId";

        when(globalApolloConfig.getNlpSwitch()).thenReturn(Constans.ONE);

        // Act
        Triple<String, List<String>, List<String>> result = nlpAiProxy.getFirstChatStartMessage(chatContext, new StartReq());

        // Assert
        assertNotNull(result);
        assertNull(result.getLeft());
        assertNull(result.getMiddle());
        assertNull(result.getRight());
    }

    @Test
    void testIsFallbackOn_True() {
        // Arrange
        when(globalApolloConfig.getNlpSwitch()).thenReturn(Constans.ONE);

        // Act
        boolean result = nlpAiProxy.isFallbackOn();

        // Assert
        assertTrue(result);
    }

    @Test
    void testIsFallbackOn_False() {
        // Arrange
        when(globalApolloConfig.getNlpSwitch()).thenReturn(0);

        // Act
        boolean result = nlpAiProxy.isFallbackOn();

        // Assert
        assertFalse(result);
    }

    @Test
    void testSendRequest_Success_WithAppendContents() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, false)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        AiData aiData = new AiData();
        AppendContents appendContents = new AppendContents();
        appendContents.setType("text");
        appendContents.setContent("Hello! How can I assist you today?");
        aiData.setAppendContents(Arrays.asList(appendContents));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequest(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCall(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

        verify(messageBoxHandler).saveUnCommitMessage(eq("user123"), eq("station456"), eq("city789"), eq("product101"), eq(messageContext));
        verify(messageBoxHandler).getAiTextMessageVO(eq(true), eq("Hello! How can I assist you today?"), eq(messageContext));
    }

    @Test
    void testSendRequest_Success_WithAppendContents_Url() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("quest123");
        productAssistRequestVo.setSessionId("session123");

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, false)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        AiData aiData = new AiData();
        AppendContents appendContents = new AppendContents();
        appendContents.setType("url");
        appendContents.setContent("");
        appendContents.setPurpose("到货提醒");
        aiData.setAppendContents(Arrays.asList(appendContents));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequest(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCall(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

        verify(messageBoxHandler).saveUnCommitMessage(eq("user123"), eq("station456"), eq("city789"), eq("product101"), eq(messageContext));
        verify(messageBoxHandler).getAiUrlMessageVO(eq(true), eq(appendContents), eq(messageContext));
    }

    @Test
    void testSendRequest_Success_WithAppendContents_Product() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, false)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        AiData aiData = new AiData();
        AppendContents appendContents = new AppendContents();
        appendContents.setType("product");
        appendContents.setContent("[\"product101\",\"product102\"]");
        appendContents.setPurpose("到货提醒");
        aiData.setAppendContents(Arrays.asList(appendContents));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequest(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCall(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

        verify(messageBoxHandler).saveUnCommitMessage(eq("user123"), eq("station456"), eq("city789"), eq("product101"), eq(messageContext));
        verify(messageBoxHandler).getAiProductCardMessageVO(eq(true), eq(appendContents), eq(messageContext), eq(chatQuestionReq));
    }

    @Test
    void testSendRequest_Success_WithAppendContents_Unknown() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, false)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        AiData aiData = new AiData();
        AppendContents appendContents = new AppendContents();
        appendContents.setType("unknown");
        appendContents.setContent("Hello! How can I assist you today?");
        aiData.setAppendContents(Arrays.asList(appendContents));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequest(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCall(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

        verify(messageBoxHandler).saveUnCommitMessage(eq("user123"), eq("station456"), eq("city789"), eq("product101"), eq(messageContext));
        verify(messageBoxHandler).getAiTextMessageVO(eq(true), eq("Hello! How can I assist you today?"), eq(messageContext));
    }

    @Test
    void testSendRequest_Success_WithOutput() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("qId");
        chatQuestionReq.setSessionId("session123");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, false)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);


        AiData aiData = new AiData();
        Choice choice = new Choice();
        choice.setFinishReason("stop");
        AiMessage aiMessage = new AiMessage();
        aiMessage.setContent("Hello!");
        aiMessage.setRole("assist");
        choice.setMessage(aiMessage);
        Output output = new Output();
        output.setChoices(Arrays.asList(choice));
        aiData.setOutput(output);

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequest(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCall(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

        verify(messageBoxHandler).saveUnCommitMessage(eq("user123"), eq("station456"), eq("city789"), eq("product101"), eq(messageContext));
        verify(messageBoxHandler).appendMessageContent(eq(12345L), eq("Hello!"));
        verify(messageBoxHandler).getAiTextMessageVO(eq(true), eq("Hello!"), eq(messageContext));
    }

    @Test
    void testSendRequest_Failure() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, false)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequest(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCall(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onFailure(eventSource, new IOException("SSE connection failed"), response);

        verify(messageBoxHandler).saveCommitMessage(eq(messageContext));
    }

    @Test
    void testSendRequest_Timeout() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, false)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequest(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCall(eq(productAssistRequestVo), callbackCaptor.capture());

        originSseEmitter.onTimeout(() -> MonitorUtil.counterOnce("originSeeEmitter", "timeout"));

    }

    @Test
    void testSendRequest_NullChatQuestionReq() {
        // Arrange
        ChatQuestionReq chatQuestionReq = null;
        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        // Act
        SseEmitter result = nlpAiProxy.sendRequest(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNull(result);
        verifyNoInteractions(sseClient);
    }

    @Test
    void testSendRequest_NullOriginSseEmitter() {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .build();

        SseEmitter originSseEmitter = null;

        // Act
        SseEmitter result = nlpAiProxy.sendRequest(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNull(result);
        verifyNoInteractions(sseClient);
    }

    @Test
    void testSendRequest_OnClose() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, false)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequest(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCall(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onClosed(eventSource);

        verify(messageBoxHandler).saveCommitMessage(eq(messageContext));
        //verify(originSseEmitter).complete();
    }

    @Test
    void testStreamCall_Success() throws IOException {
        // Arrange
        AiChatCompletionReq chatQuestionReq = new AiChatCompletionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setQuestion("qId");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setScene("search_result_assistant");
        chatQuestionReq.setAppClientId("1");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");

        when(messageBoxHandler.buildProductAssistRequestVo(chatQuestionReq, messageContext)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        AiChatData aiData = new AiChatData();
        AiChatChoice choice = new AiChatChoice();
        choice.setFinishReason("stop");
        AiChatDelta delta = new AiChatDelta();
        delta.setContent("Hello!");
        choice.setDelta(delta);
        aiData.setChoices(Collections.singletonList(choice));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.streamCall(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCall(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));
    }

    @Test
    void testStreamCall_Success_WithAppendContents() throws IOException {
        // Arrange
        AiChatCompletionReq chatQuestionReq = new AiChatCompletionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setQuestion("qId");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setScene("search_result_assistant");
        chatQuestionReq.setAppClientId("1");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");

        when(messageBoxHandler.buildProductAssistRequestVo(chatQuestionReq, messageContext)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        AiChatData aiData = new AiChatData();
        AiChatChoice choice = new AiChatChoice();
        choice.setFinishReason("stop");
        AiChatDelta delta = new AiChatDelta();
        delta.setContent("Hello!");
        choice.setDelta(delta);
        AiChatAppendContents appendContents = new AiChatAppendContents();
        appendContents.setType("text");
        appendContents.setContent("### 小贴士\\n叮咚在售[[配料|type=query]]、[[鲜牛奶|type=query]]、[[调制乳|type=query]]哦，去看看吧！");
        appendContents.setPurpose("deepseek小贴士");
        aiData.setAppendContents(Collections.singletonList(appendContents));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.streamCall(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCall(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));
    }

    @Test
    void testStreamCallWithNullData() throws IOException {
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        completionReq.setAppClientId("client123");

        SseEmitter originSseEmitter = new SseEmitter();
        MessageContext messageContext = new MessageContext();
        messageContext.setMessageId(System.currentTimeMillis());

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        when(messageBoxHandler.buildProductAssistRequestVo(completionReq, messageContext))
            .thenReturn(productAssistRequestVo);

        doAnswer(invocation -> {
            ProductAssistResultCallback callback = invocation.getArgument(1);
            callback.onEvent(null, null, null,
                "{\"created\":1741605065,\"usage\":null,\"model\":\"deepseek-v3\",\"id\":\"chatcmpl-9c55b85e-18c4-9978-b95d-2430dc74be3f\",\"choices\":[{\"finish_reason\":null,\"delta\":{\"content\":\"该牛奶新鲜、营养丰富，适合日常饮用，\"},\"index\":0,\"logprobs\":null}],\"system_fingerprint\":null,\"object\":\"chat.completion.chunk\"}");
            callback.onClosed(null);
            return null;
        }).when(sseClient).streamCall(eq(productAssistRequestVo), any(ProductAssistResultCallback.class));

        nlpAiProxy.streamCall(completionReq, originSseEmitter, messageContext);

    }

    @Test
    void testStreamCallWithFailure() throws IOException {
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        completionReq.setAppClientId("client123");

        SseEmitter originSseEmitter = new SseEmitter();
        MessageContext messageContext = new MessageContext();
        messageContext.setMessageId(System.currentTimeMillis());

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        when(messageBoxHandler.buildProductAssistRequestVo(completionReq, messageContext))
            .thenReturn(productAssistRequestVo);

        Response response = mock(Response.class);
        doAnswer(invocation -> {
            ProductAssistResultCallback callback = invocation.getArgument(1);
            callback.onFailure(null, new Exception("Test failure"), response);
            return null;
        }).when(sseClient).streamCall(eq(productAssistRequestVo), any(ProductAssistResultCallback.class));

        nlpAiProxy.streamCall(completionReq, originSseEmitter, messageContext);

    }


    @Test
    void testStreamCall_Success_WithAppendContents_Product() throws IOException {
        // Arrange
        AiChatCompletionReq chatQuestionReq = new AiChatCompletionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setQuestion("qId");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setScene("search_result_assistant");
        chatQuestionReq.setAppClientId("1");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");

        when(messageBoxHandler.buildProductAssistRequestVo(chatQuestionReq, messageContext)).thenReturn(productAssistRequestVo);
        //when(messageBoxHandler.buildAiProductCardMessageVO(chatQuestionReq, messageContext)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        AiChatData aiData = new AiChatData();
        AiChatChoice choice = new AiChatChoice();
        choice.setFinishReason("stop");
        AiChatDelta delta = new AiChatDelta();
        delta.setContent("Hello");
        choice.setDelta(delta);
        AiChatAppendContents appendContents = new AiChatAppendContents();
        appendContents.setType("product");
        appendContents.setContent("5b8d22f8c0a1ea3a278b8b7c,5b8d22f8c0a1ea3a278b8b7d");
        appendContents.setPurpose("商卡");
        aiData.setAppendContents(Collections.singletonList(appendContents));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.streamCall(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCall(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));
    }

    @Test
    void testProcessAiChatContent_Success() {
        String content = "Hello, World!";
        String appClientId = "2";
        String expectedContent = "Processed: Hello, World!";

        when(linkHandler.convert(eq(content), eq(appClientId))).thenReturn(expectedContent);

        String result = nlpAiProxy.processAiChatContent(content, appClientId);

        assertEquals(expectedContent, result);
    }

    @Test
    void testProcessAiChatContent_BlankContent() {
        String content = "";
        String appClientId = "1";

        String result = nlpAiProxy.processAiChatContent(content, appClientId);

        assertEquals(content, result);
    }

    @Test
    void testProcessAiChatContent_BlankAppClientId() {
        String content = "Hello, World!";
        String appClientId = "";

        String result = nlpAiProxy.processAiChatContent(content, appClientId);

        assertEquals(content, result);
    }

    @Test
    void testSendRequestV2_Success_WithAppendContents() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setImageUrls(Lists.newArrayList("https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"));

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");
        // 有图片，创建多模态请求参数
        List<LLMChatMessage> messagesList = new ArrayList<>();
        LLMChatMessage llmChatMessage = new LLMChatMessage();
        llmChatMessage.setRole("user");
        // 多模态 content 内容
        List<MultimodalContentVO> contentVOList = new ArrayList<>();
        // 文本
        MultimodalContentVO textContentVO = new MultimodalContentVO();
        textContentVO.setType("text");
        textContentVO.setText(chatQuestionReq.getQuestion());
        contentVOList.add(textContentVO);
        // 图片
        MultimodalContentVO imageContentVO = new MultimodalContentVO();
        imageContentVO.setType("image_url");
        MultimodalContentVO.ImageData imageData = new MultimodalContentVO.ImageData();
        imageData.setUrl(chatQuestionReq.getImageUrls().get(0));
        imageContentVO.setImageUrl(imageData);
        contentVOList.add(imageContentVO);

        llmChatMessage.setContent(contentVOList);
        messagesList.add(llmChatMessage);
        productAssistRequestVo.setMessages(messagesList);

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, true)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        AiChatData aiData = new AiChatData();
        AiChatAppendContents appendContents = new AiChatAppendContents();
        appendContents.setType("text");
        appendContents.setContent("Hello! How can I assist you today?");
        aiData.setAppendContents(Arrays.asList(appendContents));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCallV2(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

    }

    @Test
    void testSendRequestV2_Success_WithAppendContents_Url() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setImageUrls(Lists.newArrayList("https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"));

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("quest123");
        productAssistRequestVo.setSessionId("session123");
        // 有图片，创建多模态请求参数
        List<LLMChatMessage> messagesList = new ArrayList<>();
        LLMChatMessage llmChatMessage = new LLMChatMessage();
        llmChatMessage.setRole("user");
        // 多模态 content 内容
        List<MultimodalContentVO> contentVOList = new ArrayList<>();
        // 文本
        MultimodalContentVO textContentVO = new MultimodalContentVO();
        textContentVO.setType("text");
        textContentVO.setText(chatQuestionReq.getQuestion());
        contentVOList.add(textContentVO);
        // 图片
        MultimodalContentVO imageContentVO = new MultimodalContentVO();
        imageContentVO.setType("image_url");
        MultimodalContentVO.ImageData imageData = new MultimodalContentVO.ImageData();
        imageData.setUrl(chatQuestionReq.getImageUrls().get(0));
        imageContentVO.setImageUrl(imageData);
        contentVOList.add(imageContentVO);

        llmChatMessage.setContent(contentVOList);
        messagesList.add(llmChatMessage);
        productAssistRequestVo.setMessages(messagesList);

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, true)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        AiChatData aiData = new AiChatData();
        AiChatAppendContents appendContents = new AiChatAppendContents();
        appendContents.setType("url");
        appendContents.setContent("");
        appendContents.setPurpose("到货提醒");
        aiData.setAppendContents(Arrays.asList(appendContents));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCallV2(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

//        verify(messageBoxHandler).saveUnCommitMessage(eq("user123"), eq("station456"), eq("city789"), eq("product101"), eq(messageContext));
//        verify(messageBoxHandler).getAiUrlMessageVO(eq(true), eq(appendContents), eq(messageContext));
    }

    @Test
    void testSendRequestV2_Success_WithAppendContents_Product() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setImageUrls(Lists.newArrayList("https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"));

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");
        // 有图片，创建多模态请求参数
        List<LLMChatMessage> messagesList = new ArrayList<>();
        LLMChatMessage llmChatMessage = new LLMChatMessage();
        llmChatMessage.setRole("user");
        // 多模态 content 内容
        List<MultimodalContentVO> contentVOList = new ArrayList<>();
        // 文本
        MultimodalContentVO textContentVO = new MultimodalContentVO();
        textContentVO.setType("text");
        textContentVO.setText(chatQuestionReq.getQuestion());
        contentVOList.add(textContentVO);
        // 图片
        MultimodalContentVO imageContentVO = new MultimodalContentVO();
        imageContentVO.setType("image_url");
        MultimodalContentVO.ImageData imageData = new MultimodalContentVO.ImageData();
        imageData.setUrl(chatQuestionReq.getImageUrls().get(0));
        imageContentVO.setImageUrl(imageData);
        contentVOList.add(imageContentVO);

        llmChatMessage.setContent(contentVOList);
        messagesList.add(llmChatMessage);
        productAssistRequestVo.setMessages(messagesList);

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, true)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        AiChatData aiData = new AiChatData();
        AiChatAppendContents appendContents = new AiChatAppendContents();
        appendContents.setType("product");
        appendContents.setContent("[\"product101\",\"product102\"]");
        appendContents.setPurpose("到货提醒");
        aiData.setAppendContents(Arrays.asList(appendContents));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCallV2(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

//        verify(messageBoxHandler).saveUnCommitMessage(eq("user123"), eq("station456"), eq("city789"), eq("product101"), eq(messageContext));
//        verify(messageBoxHandler).getAiProductCardMessageVO(eq(true), eq(appendContents), eq(messageContext), eq(chatQuestionReq));
    }

    @Test
    void testSendRequestV2_Success_WithOutput() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("qId");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setImageUrls(Lists.newArrayList("https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"));

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");
        // 有图片，创建多模态请求参数
        List<LLMChatMessage> messagesList = new ArrayList<>();
        LLMChatMessage llmChatMessage = new LLMChatMessage();
        llmChatMessage.setRole("user");
        // 多模态 content 内容
        List<MultimodalContentVO> contentVOList = new ArrayList<>();
        // 文本
        MultimodalContentVO textContentVO = new MultimodalContentVO();
        textContentVO.setType("text");
        textContentVO.setText(chatQuestionReq.getQuestion());
        contentVOList.add(textContentVO);
        // 图片
        MultimodalContentVO imageContentVO = new MultimodalContentVO();
        imageContentVO.setType("image_url");
        MultimodalContentVO.ImageData imageData = new MultimodalContentVO.ImageData();
        imageData.setUrl(chatQuestionReq.getImageUrls().get(0));
        imageContentVO.setImageUrl(imageData);
        contentVOList.add(imageContentVO);

        llmChatMessage.setContent(contentVOList);
        messagesList.add(llmChatMessage);
        productAssistRequestVo.setMessages(messagesList);

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, true)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);


        AiChatData aiData = new AiChatData();
        AiChatDelta delta = new AiChatDelta();
        delta.setContent("Hello!");
        delta.setReasoningContent("thinking");
        AiChatChoice choice = new AiChatChoice();
        choice.setFinishReason("stop");
        choice.setDelta(delta);
        aiData.setChoices(Lists.newArrayList(choice));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCallV2(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

    }

    @Test
    void testSendRequestV2_AiData_Null() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("qId");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setImageUrls(Lists.newArrayList("https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"));

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");
        // 有图片，创建多模态请求参数
        List<LLMChatMessage> messagesList = new ArrayList<>();
        LLMChatMessage llmChatMessage = new LLMChatMessage();
        llmChatMessage.setRole("user");
        // 多模态 content 内容
        List<MultimodalContentVO> contentVOList = new ArrayList<>();
        // 文本
        MultimodalContentVO textContentVO = new MultimodalContentVO();
        textContentVO.setType("text");
        textContentVO.setText(chatQuestionReq.getQuestion());
        contentVOList.add(textContentVO);
        // 图片
        MultimodalContentVO imageContentVO = new MultimodalContentVO();
        imageContentVO.setType("image_url");
        MultimodalContentVO.ImageData imageData = new MultimodalContentVO.ImageData();
        imageData.setUrl(chatQuestionReq.getImageUrls().get(0));
        imageContentVO.setImageUrl(imageData);
        contentVOList.add(imageContentVO);

        llmChatMessage.setContent(contentVOList);
        messagesList.add(llmChatMessage);
        productAssistRequestVo.setMessages(messagesList);

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, true)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);


        AiChatData aiData = null;

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCallV2(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

    }

    @Test
    void testSendRequestV2_AiData_Choice_Null() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("qId");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setImageUrls(Lists.newArrayList("https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"));

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");
        // 有图片，创建多模态请求参数
        List<LLMChatMessage> messagesList = new ArrayList<>();
        LLMChatMessage llmChatMessage = new LLMChatMessage();
        llmChatMessage.setRole("user");
        // 多模态 content 内容
        List<MultimodalContentVO> contentVOList = new ArrayList<>();
        // 文本
        MultimodalContentVO textContentVO = new MultimodalContentVO();
        textContentVO.setType("text");
        textContentVO.setText(chatQuestionReq.getQuestion());
        contentVOList.add(textContentVO);
        // 图片
        MultimodalContentVO imageContentVO = new MultimodalContentVO();
        imageContentVO.setType("image_url");
        MultimodalContentVO.ImageData imageData = new MultimodalContentVO.ImageData();
        imageData.setUrl(chatQuestionReq.getImageUrls().get(0));
        imageContentVO.setImageUrl(imageData);
        contentVOList.add(imageContentVO);

        llmChatMessage.setContent(contentVOList);
        messagesList.add(llmChatMessage);
        productAssistRequestVo.setMessages(messagesList);

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, true)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);


        AiChatData aiData = new AiChatData();
        AiChatDelta delta = new AiChatDelta();
        delta.setContent("Hello!");
        delta.setReasoningContent("thinking");
        AiChatChoice choice = new AiChatChoice();
        choice.setFinishReason("stop");
        choice.setDelta(delta);

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCallV2(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

    }

    @Test
    void testSendRequestV2_AiData_Choice_Delta_Null() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("qId");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setImageUrls(Lists.newArrayList("https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"));

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");
        // 有图片，创建多模态请求参数
        List<LLMChatMessage> messagesList = new ArrayList<>();
        LLMChatMessage llmChatMessage = new LLMChatMessage();
        llmChatMessage.setRole("user");
        // 多模态 content 内容
        List<MultimodalContentVO> contentVOList = new ArrayList<>();
        // 文本
        MultimodalContentVO textContentVO = new MultimodalContentVO();
        textContentVO.setType("text");
        textContentVO.setText(chatQuestionReq.getQuestion());
        contentVOList.add(textContentVO);
        // 图片
        MultimodalContentVO imageContentVO = new MultimodalContentVO();
        imageContentVO.setType("image_url");
        MultimodalContentVO.ImageData imageData = new MultimodalContentVO.ImageData();
        imageData.setUrl(chatQuestionReq.getImageUrls().get(0));
        imageContentVO.setImageUrl(imageData);
        contentVOList.add(imageContentVO);

        llmChatMessage.setContent(contentVOList);
        messagesList.add(llmChatMessage);
        productAssistRequestVo.setMessages(messagesList);

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, true)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);


        AiChatData aiData = new AiChatData();
        AiChatDelta delta = new AiChatDelta();
        delta.setContent("Hello!");
        delta.setReasoningContent("thinking");
        AiChatChoice choice = new AiChatChoice();
        choice.setFinishReason("stop");
        aiData.setChoices(Lists.newArrayList(choice));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCallV2(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

    }

    @Test
    void testSendRequestV2_Failure() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setImageUrls(Lists.newArrayList("https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"));

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");
        // 有图片，创建多模态请求参数
        List<LLMChatMessage> messagesList = new ArrayList<>();
        LLMChatMessage llmChatMessage = new LLMChatMessage();
        llmChatMessage.setRole("user");
        // 多模态 content 内容
        List<MultimodalContentVO> contentVOList = new ArrayList<>();
        // 文本
        MultimodalContentVO textContentVO = new MultimodalContentVO();
        textContentVO.setType("text");
        textContentVO.setText(chatQuestionReq.getQuestion());
        contentVOList.add(textContentVO);
        // 图片
        MultimodalContentVO imageContentVO = new MultimodalContentVO();
        imageContentVO.setType("image_url");
        MultimodalContentVO.ImageData imageData = new MultimodalContentVO.ImageData();
        imageData.setUrl(chatQuestionReq.getImageUrls().get(0));
        imageContentVO.setImageUrl(imageData);
        contentVOList.add(imageContentVO);

        llmChatMessage.setContent(contentVOList);
        messagesList.add(llmChatMessage);
        productAssistRequestVo.setMessages(messagesList);

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, true)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCallV2(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onFailure(eventSource, new IOException("SSE connection failed"), response);
    }

    @Test
    void testSendRequestV2_Timeout() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setImageUrls(Lists.newArrayList("https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"));

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");
        // 有图片，创建多模态请求参数
        List<LLMChatMessage> messagesList = new ArrayList<>();
        LLMChatMessage llmChatMessage = new LLMChatMessage();
        llmChatMessage.setRole("user");
        // 多模态 content 内容
        List<MultimodalContentVO> contentVOList = new ArrayList<>();
        // 文本
        MultimodalContentVO textContentVO = new MultimodalContentVO();
        textContentVO.setType("text");
        textContentVO.setText(chatQuestionReq.getQuestion());
        contentVOList.add(textContentVO);
        // 图片
        MultimodalContentVO imageContentVO = new MultimodalContentVO();
        imageContentVO.setType("image_url");
        MultimodalContentVO.ImageData imageData = new MultimodalContentVO.ImageData();
        imageData.setUrl(chatQuestionReq.getImageUrls().get(0));
        imageContentVO.setImageUrl(imageData);
        contentVOList.add(imageContentVO);

        llmChatMessage.setContent(contentVOList);
        messagesList.add(llmChatMessage);
        productAssistRequestVo.setMessages(messagesList);

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, true)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCallV2(eq(productAssistRequestVo), callbackCaptor.capture());

        originSseEmitter.onTimeout(() -> MonitorUtil.counterOnce("originSeeEmitter", "timeout"));

    }

    @Test
    void testSendRequestV2_NullChatQuestionReq() {
        // Arrange
        ChatQuestionReq chatQuestionReq = null;
        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNull(result);
        verifyNoInteractions(sseClient);
    }

    @Test
    void testSendRequestV2_NullOriginSseEmitter() {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .build();

        SseEmitter originSseEmitter = null;

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNull(result);
        verifyNoInteractions(sseClient);
    }

    @Test
    void testSendRequestV2_OnClose() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");
        chatQuestionReq.setImageUrls(Lists.newArrayList("https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"));

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");
        // 有图片，创建多模态请求参数
        List<LLMChatMessage> messagesList = new ArrayList<>();
        LLMChatMessage llmChatMessage = new LLMChatMessage();
        llmChatMessage.setRole("user");
        // 多模态 content 内容
        List<MultimodalContentVO> contentVOList = new ArrayList<>();
        // 文本
        MultimodalContentVO textContentVO = new MultimodalContentVO();
        textContentVO.setType("text");
        textContentVO.setText(chatQuestionReq.getQuestion());
        contentVOList.add(textContentVO);
        // 图片
        MultimodalContentVO imageContentVO = new MultimodalContentVO();
        imageContentVO.setType("image_url");
        MultimodalContentVO.ImageData imageData = new MultimodalContentVO.ImageData();
        imageData.setUrl(chatQuestionReq.getImageUrls().get(0));
        imageContentVO.setImageUrl(imageData);
        contentVOList.add(imageContentVO);

        llmChatMessage.setContent(contentVOList);
        messagesList.add(llmChatMessage);
        productAssistRequestVo.setMessages(messagesList);

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, true)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCallV2(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onClosed(eventSource);

        verify(messageBoxHandler).saveCommitMessage(eq(messageContext));
        //verify(originSseEmitter).complete();
    }

    @Test
    void testSendRequestV2_Success_WithAppendContents_Question() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setCityName("上海");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setCityName("上海");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");
        // 无图片消息，创建纯文本请求参数
        List<LLMChatMessage> messagesList = new ArrayList<>();
        LLMChatMessage llmChatMessage = new LLMChatMessage();
        llmChatMessage.setRole("user");
        llmChatMessage.setContent(chatQuestionReq.getQuestion());
        messagesList.add(llmChatMessage);
        productAssistRequestVo.setMessages(messagesList);

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, true)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        AiData aiData = new AiData();
        AppendContents appendContents = new AppendContents();
        appendContents.setType("question");
        appendContents.setContent("{\"questions\":[\"猜问 1\",\"猜问 2\"]}");
        appendContents.setPurpose("猜你想问");
        aiData.setAppendContents(Arrays.asList(appendContents));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCallV2(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

    }

    @Test
    void testSendRequestV2_Success_WithAppendContents_Unknown() throws Exception {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("user123");
        chatQuestionReq.setStationId("station456");
        chatQuestionReq.setCityNumber("city789");
        chatQuestionReq.setCityName("上海");
        chatQuestionReq.setProductId("product101");
        chatQuestionReq.setQuestion("Hello, how are you?");
        chatQuestionReq.setSessionId("session123");

        MessageContext messageContext = MessageContext.builder()
            .messageId(12345L)
            .sessionId("session123")
            .questId("quest123")
            .uid("user123")
            .build();

        SseEmitter originSseEmitter = new SseEmitter();

        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setUser("user123");
        productAssistRequestVo.setStationId("station456");
        productAssistRequestVo.setCityNumber("city789");
        productAssistRequestVo.setCityName("上海");
        productAssistRequestVo.setProductMongoId("product101");
        productAssistRequestVo.setQid("qId");
        productAssistRequestVo.setSessionId("session123");
        // 无图片消息，创建纯文本请求参数
        List<LLMChatMessage> messagesList = new ArrayList<>();
        LLMChatMessage llmChatMessage = new LLMChatMessage();
        llmChatMessage.setRole("user");
        llmChatMessage.setContent(chatQuestionReq.getQuestion());
        messagesList.add(llmChatMessage);
        productAssistRequestVo.setMessages(messagesList);

        when(messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, true)).thenReturn(productAssistRequestVo);

        ArgumentCaptor<ProductAssistResultCallback> callbackCaptor = ArgumentCaptor.forClass(ProductAssistResultCallback.class);

        AiData aiData = new AiData();
        AppendContents appendContents = new AppendContents();
        appendContents.setType("unkonw");
        appendContents.setContent("{\"questions\":[\"猜问 1\",\"猜问 2\"]}");
        appendContents.setPurpose("猜你想问");
        aiData.setAppendContents(Arrays.asList(appendContents));

        Response response = mock(Response.class);
        EventSource eventSource = mock(EventSource.class);

        // Act
        SseEmitter result = nlpAiProxy.sendRequestV2(chatQuestionReq, originSseEmitter, messageContext);

        // Assert
        assertNotNull(result);
        verify(sseClient).streamCallV2(eq(productAssistRequestVo), callbackCaptor.capture());

        ProductAssistResultCallback callback = callbackCaptor.getValue();
        callback.onOpen(eventSource, response);
        callback.onEvent(eventSource, "id1", "type1", JsonUtil.toJson(aiData));

    }

    @Test
    void testQueryDailyTips_Success() {
        // Arrange
        ChatContext chatContext = new ChatContext();
        chatContext.setUid("user123");
        chatContext.setStationId("station123");

        ProductAssistDietDailyKnowledgeRequestVo requestVo = new ProductAssistDietDailyKnowledgeRequestVo();
        requestVo.setQid(UUID.randomUUID().toString());
        requestVo.setTimestamp(System.currentTimeMillis());
        requestVo.setUser(chatContext.getUid());
        requestVo.setStationId(chatContext.getStationId());

        ProductAssistDailyKnowledge dailyKnowledge = new ProductAssistDailyKnowledge();
        dailyKnowledge.setContent("Daily Tip Content");
        dailyKnowledge.setDate("2023-10-01");
        dailyKnowledge.setLunarDate("Lunar Date");

        ProductAssistDietResponseVo responseVo = new ProductAssistDietResponseVo();
        responseVo.setCode(0);
        responseVo.setDailyKnowledge(dailyKnowledge);

        when(nlpApiClient.getDailyKnowledge(any())).thenReturn(responseVo);

        // Act
        DailyTipsVO actual = nlpAiProxy.queryDailyTips(chatContext);

        // Assert
        assertNotNull(actual);
        assertEquals("Daily Tip Content", actual.getContent());
        assertEquals("2023-10-01", actual.getDate());
        assertEquals("Lunar Date", actual.getLunarDate());
    }

    @Test
    void testQueryDailyTips_Failure() {
        // Arrange
        ChatContext chatContext = new ChatContext();
        chatContext.setUid("user123");
        chatContext.setStationId("station123");

        ProductAssistDietDailyKnowledgeRequestVo requestVo = new ProductAssistDietDailyKnowledgeRequestVo();
        requestVo.setQid(UUID.randomUUID().toString());
        requestVo.setTimestamp(System.currentTimeMillis());
        requestVo.setUser(chatContext.getUid());
        requestVo.setStationId(chatContext.getStationId());

        ProductAssistDietResponseVo responseVo = new ProductAssistDietResponseVo();
        responseVo.setCode(1);
        responseVo.setDailyKnowledge(null);

        when(nlpApiClient.getDailyKnowledge(requestVo)).thenReturn(responseVo);

        // Act
        DailyTipsVO actual = nlpAiProxy.queryDailyTips(chatContext);

        // Assert
        assertNull(actual);
    }

    @Test
    void testQueryDailyTips_NullResponse() {
        // Arrange
        ChatContext chatContext = new ChatContext();
        chatContext.setUid("user123");
        chatContext.setStationId("station123");

        ProductAssistDietDailyKnowledgeRequestVo requestVo = new ProductAssistDietDailyKnowledgeRequestVo();
        requestVo.setQid(UUID.randomUUID().toString());
        requestVo.setTimestamp(System.currentTimeMillis());
        requestVo.setUser(chatContext.getUid());
        requestVo.setStationId(chatContext.getStationId());

        when(nlpApiClient.getDailyKnowledge(requestVo)).thenReturn(null);

        // Act
        DailyTipsVO actual = nlpAiProxy.queryDailyTips(chatContext);

        // Assert
        assertNull(actual);
    }

    @Test
    void testQueryDailyTips_NullDailyKnowledge() {
        // Arrange
        ChatContext chatContext = new ChatContext();
        chatContext.setUid("user123");
        chatContext.setStationId("station123");

        ProductAssistDietDailyKnowledgeRequestVo requestVo = new ProductAssistDietDailyKnowledgeRequestVo();
        requestVo.setQid(UUID.randomUUID().toString());
        requestVo.setTimestamp(System.currentTimeMillis());
        requestVo.setUser(chatContext.getUid());
        requestVo.setStationId(chatContext.getStationId());

        ProductAssistDietResponseVo responseVo = new ProductAssistDietResponseVo();
        responseVo.setCode(0);
        responseVo.setDailyKnowledge(null);

        when(nlpApiClient.getDailyKnowledge(requestVo)).thenReturn(responseVo);

        // Act
        DailyTipsVO actual = nlpAiProxy.queryDailyTips(chatContext);

        // Assert
        assertNull(actual);
    }

    @Test
    void queryDailyTips_ExceptionThrown_ReturnsNull() {
        ChatContext chatContext = ChatContext.of("uid", "deviceId", "stationId", "cityNumber", "apiVersion", "appVersion", "nativeVersion", 1, "longitude", "latitude", "referPage");

        when(nlpApiClient.getDailyKnowledge(any(ProductAssistDietDailyKnowledgeRequestVo.class))).thenThrow(new RuntimeException("Test exception"));

        DailyTipsVO result = nlpAiProxy.queryDailyTips(chatContext);

        assertNull(result);
    }

    @Test
    void testProcessAppendContentUrl_Persistent() throws Exception {
        when(globalApolloConfig.isUrlPurposePersistent(anyString())).thenReturn(true);

        AppendContents appendContent = new AppendContents();
        appendContent.setPurpose("到货提醒");
        appendContent.setType("url");
        appendContent.setContent("http://example.com");

        MessageVO messageVO = new MessageVO();
        messageVO.setMessageId(System.currentTimeMillis());
        messageVO.setContent("http://example.com");

        Whitebox.invokeMethod(nlpAiProxy, "processAppendContentUrl", appendContent, messageVO);

        verify(globalApolloConfig, times(1)).isUrlPurposePersistent(anyString());
        // Assert
        verify(messageBoxHandler, times(1)).appendMessageContent(any(), any());

    }

    @Test
    void testProcessAppendContentUrl_NotPersistent() throws Exception {
        when(globalApolloConfig.isUrlPurposePersistent(anyString())).thenReturn(false);

        AppendContents appendContent = new AppendContents();
        appendContent.setPurpose("到货提醒");
        appendContent.setType("url");
        appendContent.setContent("http://example.com");

        MessageVO messageVO = new MessageVO();
        messageVO.setMessageId(System.currentTimeMillis());
        messageVO.setContent("http://example.com");

        Whitebox.invokeMethod(nlpAiProxy, "processAppendContentUrl", appendContent, messageVO);

        verify(globalApolloConfig, times(1)).isUrlPurposePersistent(anyString());
        // Assert
        verify(messageBoxHandler, times(0)).appendMessageContent(any(), any());

    }

}
