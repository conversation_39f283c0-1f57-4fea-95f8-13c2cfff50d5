package com.ddmc.chat.common.infra.proxy;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.ddmc.chat.common.domain.dto.req.chat.ChatQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.StartReq;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ChatContextTest {

    private ChatContext chatContext;

    @BeforeEach
    public void setUp() {
        chatContext = new ChatContext();
    }

    @Test
    void testOfWithBasicInfo() {
        // Arrange
        String uid = "testUid";
        String deviceId = "testDeviceId";
        String stationId = "testStationId";
        String cityNumber = "testCityNumber";
        String apiVersion = "testApiVersion";
        String appVersion = "testAppVersion";
        String nativeVersion = "testNativeVersion";
        Integer appClientId = 1;
        String longitude = "123.456";
        String latitude = "789.012";
        String referPage = "testReferPage";

        // Act
        ChatContext result = ChatContext.of(uid, deviceId, stationId, cityNumber, apiVersion, appVersion, nativeVersion, appClientId, longitude, latitude, referPage);

        // Assert
        assertNotNull(result);
        assertEquals(uid, result.getUid());
        assertEquals(deviceId, result.getDeviceId());
        assertEquals(stationId, result.getStationId());
        assertEquals(cityNumber, result.getCityNumber());
        assertEquals(apiVersion, result.getApiVersion());
        assertEquals(appVersion, result.getAppVersion());
        assertEquals(nativeVersion, result.getNativeVersion());
        assertEquals(appClientId, result.getAppClientId());
        assertEquals(longitude, result.getLongitude());
        assertEquals(latitude, result.getLatitude());
        assertEquals(referPage, result.getReferPage());
    }

    @Test
    void testOfWithStartReq() {
        // Arrange
        StartReq startReq = new StartReq();
        startReq.setUid("testUid");
        startReq.setDeviceId("testDeviceId");
        startReq.setStationId("testStationId");
        startReq.setCityNumber("testCityNumber");
        startReq.setApiVersion("testApiVersion");
        startReq.setAppVersion("testAppVersion");
        startReq.setNativeVersion("testNativeVersion");
        startReq.setAppClientId("1");
        startReq.setLongitude("123.456");
        startReq.setLatitude("789.012");
        startReq.setFrom("testReferPage");
        startReq.setProductId("testProductId");
        startReq.setFrontCategoryName("testFrontCategoryName");
        startReq.setFrontCategoryId("testFrontCategoryId");
        startReq.setSearchKeyword("testSearchKeyword");

        // Act
        ChatContext result = ChatContext.of(startReq);

        // Assert
        assertNotNull(result);
        assertEquals(startReq.getUid(), result.getUid());
        assertEquals(startReq.getDeviceId(), result.getDeviceId());
        assertEquals(startReq.getStationId(), result.getStationId());
        assertEquals(startReq.getCityNumber(), result.getCityNumber());
        assertEquals(startReq.getApiVersion(), result.getApiVersion());
        assertEquals(startReq.getAppVersion(), result.getAppVersion());
        assertEquals(startReq.getNativeVersion(), result.getNativeVersion());
        assertEquals(1, result.getAppClientId());
        assertEquals(startReq.getLongitude(), result.getLongitude());
        assertEquals(startReq.getLatitude(), result.getLatitude());
        assertEquals(startReq.getFrom(), result.getReferPage());
        assertEquals(startReq.getProductId(), result.getProductId());
        assertEquals(startReq.getFrontCategoryName(), result.getFrontCategoryName());
        assertEquals(startReq.getFrontCategoryId(), result.getFrontCategoryId());
        assertEquals(startReq.getSearchKeyword(), result.getSearchKeyword());
    }

    @Test
    void testOfWithChatQuestionReq() {
        // Arrange
        ChatQuestionReq chatQuestionReq = new ChatQuestionReq();
        chatQuestionReq.setUid("testUid");
        chatQuestionReq.setDeviceId("testDeviceId");
        chatQuestionReq.setStationId("testStationId");
        chatQuestionReq.setCityNumber("testCityNumber");
        chatQuestionReq.setApiVersion("testApiVersion");
        chatQuestionReq.setAppVersion("testAppVersion");
        chatQuestionReq.setNativeVersion("testNativeVersion");
        chatQuestionReq.setAppClientId("1");
        chatQuestionReq.setLongitude("123.456");
        chatQuestionReq.setLatitude("789.012");
        chatQuestionReq.setFrom("testReferPage");
        chatQuestionReq.setProductId("testProductId");

        // Act
        ChatContext result = ChatContext.of(chatQuestionReq);

        // Assert
        assertNotNull(result);
        assertEquals(chatQuestionReq.getUid(), result.getUid());
        assertEquals(chatQuestionReq.getDeviceId(), result.getDeviceId());
        assertEquals(chatQuestionReq.getStationId(), result.getStationId());
        assertEquals(chatQuestionReq.getCityNumber(), result.getCityNumber());
        assertEquals(chatQuestionReq.getApiVersion(), result.getApiVersion());
        assertEquals(chatQuestionReq.getAppVersion(), result.getAppVersion());
        assertEquals(chatQuestionReq.getNativeVersion(), result.getNativeVersion());
        assertEquals(1, result.getAppClientId());
        assertEquals(chatQuestionReq.getLongitude(), result.getLongitude());
        assertEquals(chatQuestionReq.getLatitude(), result.getLatitude());
        assertEquals(chatQuestionReq.getFrom(), result.getReferPage());
        assertEquals(chatQuestionReq.getProductId(), result.getProductId());
    }
}
