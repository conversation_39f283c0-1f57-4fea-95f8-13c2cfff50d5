package com.ddmc.chat.common.infra.proxy;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.chat.common.domain.dto.req.ai.AiChatCompletionReq;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatAppendContents;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatChoice;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatData;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatDelta;
import com.ddmc.chat.common.domain.entity.MessageContext;
import com.ddmc.chat.common.infra.handler.LinkHandler;
import com.ddmc.chat.common.infra.handler.MessageBoxHandler;
import com.ddmc.guide.enhance.json.JsonUtil;
import com.ddmc.recipe.searchrec.client.AISearchSSEClient;
import com.ddmc.recipe.searchrec.client.SSEResultCallback;
import com.ddmc.recipe.searchrec.entity.request.ProductRequestVo;
import java.io.IOException;
import java.util.Collections;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import org.apache.catalina.connector.ClientAbortException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@ExtendWith(MockitoExtension.class)
class SearchRecProxyTest {
    @Mock
    private AISearchSSEClient aiSearchSseClient;

    @Mock
    private MessageBoxHandler messageBoxHandler;

    @Mock
    private LinkHandler linkHandler;

    @InjectMocks
    private SearchRecProxy searchRecProxy;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testAiSearch_Success() throws IOException {
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        completionReq.setAppClientId("testAppClientId");

        MessageContext messageContext = new MessageContext();
        messageContext.setMessageId(System.currentTimeMillis());

        ProductRequestVo productRequestVo = new ProductRequestVo();
        when(messageBoxHandler.buildProductRequestVo(completionReq, messageContext)).thenReturn(productRequestVo);

        SseEmitter originSseEmitter = new SseEmitter();

        AiChatData aiData = new AiChatData();
        AiChatChoice choice = new AiChatChoice();
        choice.setFinishReason("stop");
        AiChatDelta delta = new AiChatDelta();
        delta.setContent("Hello!");
        choice.setDelta(delta);
        aiData.setChoices(Collections.singletonList(choice));

        String jsonData = JsonUtil.toJson(aiData);
        SSEResultCallback callback = mock(SSEResultCallback.class);
        searchRecProxy.aiSearch(completionReq, originSseEmitter, messageContext);

        ArgumentCaptor<SSEResultCallback> callbackCaptor = ArgumentCaptor.forClass(SSEResultCallback.class);
        verify(aiSearchSseClient).aiSearch(eq(productRequestVo), callbackCaptor.capture());

        SSEResultCallback capturedCallback = callbackCaptor.getValue();
        assertNotNull(capturedCallback);

        Response response = mock(Response.class);
        capturedCallback.onOpen(mock(EventSource.class), response);

        capturedCallback.onEvent(mock(EventSource.class), "id", "type", jsonData);

    }

    @Test
    void testAiSearch_Choices_Null() throws IOException {
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        completionReq.setAppClientId("testAppClientId");

        MessageContext messageContext = new MessageContext();
        messageContext.setMessageId(System.currentTimeMillis());

        ProductRequestVo productRequestVo = new ProductRequestVo();
        when(messageBoxHandler.buildProductRequestVo(completionReq, messageContext)).thenReturn(productRequestVo);

        SseEmitter originSseEmitter = new SseEmitter();

        AiChatData aiData = new AiChatData();
        AiChatChoice choice = new AiChatChoice();
        choice.setFinishReason("stop");
        AiChatDelta delta = new AiChatDelta();
        delta.setContent("Hello!");
        choice.setDelta(delta);

        String jsonData = JsonUtil.toJson(aiData);
        searchRecProxy.aiSearch(completionReq, originSseEmitter, messageContext);

        ArgumentCaptor<SSEResultCallback> callbackCaptor = ArgumentCaptor.forClass(SSEResultCallback.class);
        verify(aiSearchSseClient).aiSearch(eq(productRequestVo), callbackCaptor.capture());

        SSEResultCallback capturedCallback = callbackCaptor.getValue();
        assertNotNull(capturedCallback);

        Response response = mock(Response.class);
        capturedCallback.onOpen(mock(EventSource.class), response);

        capturedCallback.onEvent(mock(EventSource.class), "id", "type", jsonData);

    }

    @Test
    void testAiSearch_Choices_Delta_Null() throws IOException {
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        completionReq.setAppClientId("testAppClientId");

        MessageContext messageContext = new MessageContext();
        messageContext.setMessageId(System.currentTimeMillis());

        ProductRequestVo productRequestVo = new ProductRequestVo();
        when(messageBoxHandler.buildProductRequestVo(completionReq, messageContext)).thenReturn(productRequestVo);

        SseEmitter originSseEmitter = new SseEmitter();

        AiChatData aiData = new AiChatData();
        AiChatChoice choice = new AiChatChoice();
        choice.setFinishReason("stop");
        AiChatDelta delta = new AiChatDelta();
        delta.setContent("Hello!");
        aiData.setChoices(Collections.singletonList(choice));

        String jsonData = JsonUtil.toJson(aiData);
        searchRecProxy.aiSearch(completionReq, originSseEmitter, messageContext);

        ArgumentCaptor<SSEResultCallback> callbackCaptor = ArgumentCaptor.forClass(SSEResultCallback.class);
        verify(aiSearchSseClient).aiSearch(eq(productRequestVo), callbackCaptor.capture());

        SSEResultCallback capturedCallback = callbackCaptor.getValue();
        assertNotNull(capturedCallback);

        Response response = mock(Response.class);
        capturedCallback.onOpen(mock(EventSource.class), response);

        capturedCallback.onEvent(mock(EventSource.class), "id", "type", jsonData);

    }

    @Test
    void testAiSearch_With_AppendContents_Unknown() throws IOException {
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        completionReq.setAppClientId("testAppClientId");

        MessageContext messageContext = new MessageContext();
        messageContext.setMessageId(System.currentTimeMillis());

        ProductRequestVo productRequestVo = new ProductRequestVo();
        when(messageBoxHandler.buildProductRequestVo(completionReq, messageContext)).thenReturn(productRequestVo);

        SseEmitter originSseEmitter = new SseEmitter();

        AiChatData aiData = new AiChatData();
        AiChatAppendContents appendContent = new AiChatAppendContents();
        appendContent.setType("unknown");
        appendContent.setContent("未知");
        appendContent.setPurpose("unknown");
        aiData.setAppendContents(Collections.singletonList(appendContent));

        String jsonData = JsonUtil.toJson(aiData);
        SSEResultCallback callback = mock(SSEResultCallback.class);
        searchRecProxy.aiSearch(completionReq, originSseEmitter, messageContext);

        ArgumentCaptor<SSEResultCallback> callbackCaptor = ArgumentCaptor.forClass(SSEResultCallback.class);
        verify(aiSearchSseClient).aiSearch(eq(productRequestVo), callbackCaptor.capture());

        SSEResultCallback capturedCallback = callbackCaptor.getValue();
        assertNotNull(capturedCallback);

        Response response = mock(Response.class);
        capturedCallback.onOpen(mock(EventSource.class), response);

        capturedCallback.onEvent(mock(EventSource.class), "id", "type", jsonData);

    }

    @Test
    void testAiSearch_ReqNull() throws IOException {
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        completionReq.setAppClientId("testAppClientId");

        MessageContext messageContext = new MessageContext();
        messageContext.setMessageId(System.currentTimeMillis());

        SseEmitter originSseEmitter = new SseEmitter();

        SseEmitter sseEmitter = searchRecProxy.aiSearch(null, originSseEmitter, messageContext);
        assertNull(sseEmitter);

    }

    @Test
    void testAiSearch_Product_Success() throws IOException {
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        completionReq.setAppClientId("testAppClientId");

        MessageContext messageContext = new MessageContext();
        messageContext.setMessageId(System.currentTimeMillis());

        ProductRequestVo productRequestVo = new ProductRequestVo();
        when(messageBoxHandler.buildProductRequestVo(completionReq, messageContext)).thenReturn(productRequestVo);

        SseEmitter originSseEmitter = new SseEmitter();

        AiChatData aiData = new AiChatData();
        AiChatAppendContents appendContent = new AiChatAppendContents();
        appendContent.setType("product");
        appendContent.setContent("[\"product101\",\"product102\"]");
        appendContent.setPurpose("推荐商品");
        aiData.setAppendContents(Collections.singletonList(appendContent));

        String jsonData = JsonUtil.toJson(aiData);
        SSEResultCallback callback = mock(SSEResultCallback.class);
        searchRecProxy.aiSearch(completionReq, originSseEmitter, messageContext);

        ArgumentCaptor<SSEResultCallback> callbackCaptor = ArgumentCaptor.forClass(SSEResultCallback.class);
        verify(aiSearchSseClient).aiSearch(eq(productRequestVo), callbackCaptor.capture());

        SSEResultCallback capturedCallback = callbackCaptor.getValue();
        assertNotNull(capturedCallback);

        Response response = mock(Response.class);
        capturedCallback.onOpen(mock(EventSource.class), response);

        capturedCallback.onEvent(mock(EventSource.class), "id", "type", jsonData);

    }

    @Test
    void testAiSearch_ClientAbortException() throws IOException {
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        completionReq.setAppClientId("testAppClientId");

        MessageContext messageContext = new MessageContext();
        messageContext.setMessageId(System.currentTimeMillis());

        ProductRequestVo productRequestVo = new ProductRequestVo();
        when(messageBoxHandler.buildProductRequestVo(completionReq, messageContext)).thenReturn(productRequestVo);

        SseEmitter originSseEmitter = new SseEmitter();

        AiChatData aiData = new AiChatData();
        AiChatAppendContents appendContent = new AiChatAppendContents();
        appendContent.setType("text");
        appendContent.setContent("testContent");
        appendContent.setQid("testQid");
        aiData.setAppendContents(Collections.singletonList(appendContent));

        String jsonData = JsonUtil.toJson(aiData);
        SSEResultCallback callback = mock(SSEResultCallback.class);
        searchRecProxy.aiSearch(completionReq, originSseEmitter, messageContext);

        ArgumentCaptor<SSEResultCallback> callbackCaptor = ArgumentCaptor.forClass(SSEResultCallback.class);
        verify(aiSearchSseClient).aiSearch(eq(productRequestVo), callbackCaptor.capture());

        SSEResultCallback capturedCallback = callbackCaptor.getValue();
        assertNotNull(capturedCallback);

        Response response = mock(Response.class);
        capturedCallback.onOpen(mock(EventSource.class), response);

        capturedCallback.onEvent(mock(EventSource.class), "id", "type", jsonData);

        capturedCallback.onFailure(mock(EventSource.class), new ClientAbortException("Client aborted"), response);

    }

    @Test
    void aiSearch_OnEvent_NullAiData_CounterOnce() {
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        SseEmitter sseEmitter = new SseEmitter();

        MessageContext messageContext = new MessageContext();

        ProductRequestVo productRequestVo = new ProductRequestVo();
        when(messageBoxHandler.buildProductRequestVo(completionReq, messageContext)).thenReturn(productRequestVo);

        searchRecProxy.aiSearch(completionReq, sseEmitter, messageContext);

        ArgumentCaptor<SSEResultCallback> callbackCaptor = ArgumentCaptor.forClass(SSEResultCallback.class);
        verify(aiSearchSseClient).aiSearch(eq(productRequestVo), callbackCaptor.capture());
        SSEResultCallback capturedCallback = callbackCaptor.getValue();
        capturedCallback.onEvent(mock(EventSource.class), "id", "type", null);

    }

    @Test
    void aiSearch_OnEvent_ValidAppendContents_SendsMessage() throws IOException {
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        SseEmitter sseEmitter = new SseEmitter();

        MessageContext messageContext = new MessageContext();

        ProductRequestVo productRequestVo = new ProductRequestVo();
        when(messageBoxHandler.buildProductRequestVo(completionReq, messageContext)).thenReturn(productRequestVo);

        AiChatAppendContents appendContent = new AiChatAppendContents();
        appendContent.setType("text");
        appendContent.setContent("test content");

        AiChatData aiData = new AiChatData();
        aiData.setAppendContents(Collections.singletonList(appendContent));

        searchRecProxy.aiSearch(completionReq, sseEmitter, messageContext);

        ArgumentCaptor<SSEResultCallback> callbackCaptor = ArgumentCaptor.forClass(SSEResultCallback.class);
        verify(aiSearchSseClient).aiSearch(eq(productRequestVo), callbackCaptor.capture());

        SSEResultCallback capturedCallback = callbackCaptor.getValue();

        capturedCallback.onEvent(mock(EventSource.class), "id", "type", JsonUtil.toJson(aiData));

    }

    @Test
    void aiSearch_OnClosed_CompletesSseEmitter() {
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        SseEmitter sseEmitter = new SseEmitter();

        MessageContext messageContext = new MessageContext();

        ProductRequestVo productRequestVo = new ProductRequestVo();
        when(messageBoxHandler.buildProductRequestVo(completionReq, messageContext)).thenReturn(productRequestVo);

        searchRecProxy.aiSearch(completionReq, sseEmitter, messageContext);

        ArgumentCaptor<SSEResultCallback> callbackCaptor = ArgumentCaptor.forClass(SSEResultCallback.class);
        verify(aiSearchSseClient).aiSearch(eq(productRequestVo), callbackCaptor.capture());

        SSEResultCallback capturedCallback = callbackCaptor.getValue();

        capturedCallback.onClosed(mock(EventSource.class));

    }

    @Test
    void aiSearch_OnFailure_SendsFailureResponse() throws IOException {
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        SseEmitter sseEmitter = new SseEmitter();

        MessageContext messageContext = new MessageContext();

        ProductRequestVo productRequestVo = new ProductRequestVo();
        when(messageBoxHandler.buildProductRequestVo(completionReq, messageContext)).thenReturn(productRequestVo);

        Response response = mock(Response.class);
        //when(response.message()).thenReturn("error message");

        searchRecProxy.aiSearch(completionReq, sseEmitter, messageContext);

        ArgumentCaptor<SSEResultCallback> callbackCaptor = ArgumentCaptor.forClass(SSEResultCallback.class);
        verify(aiSearchSseClient).aiSearch(eq(productRequestVo), callbackCaptor.capture());
        SSEResultCallback capturedCallback = callbackCaptor.getValue();

        capturedCallback.onFailure(mock(EventSource.class), new RuntimeException("cause"), response);

    }

    @Test
    void testAiStop_Success() {
        String messageId = "testMessageId";
        ProductRequestVo productRequestVo = new ProductRequestVo();
        productRequestVo.setMsgId(messageId);

        com.ddmc.searchrec.infra.utils.entity.ResponseBaseVo<String> responseBaseVo = new com.ddmc.searchrec.infra.utils.entity.ResponseBaseVo<>();
        responseBaseVo.setSuccess(true);
        when(aiSearchSseClient.aiStop(productRequestVo)).thenReturn(responseBaseVo);

        boolean result = searchRecProxy.aiStop(messageId);

        assertTrue(result);
        verify(aiSearchSseClient).aiStop(eq(productRequestVo));
    }

    @Test
    void testAiStop_Failure() {
        String messageId = "testMessageId";
        ProductRequestVo productRequestVo = new ProductRequestVo();
        productRequestVo.setMsgId(messageId);

        com.ddmc.searchrec.infra.utils.entity.ResponseBaseVo<String> responseBaseVo = new com.ddmc.searchrec.infra.utils.entity.ResponseBaseVo<>();
        responseBaseVo.setSuccess(false);
        when(aiSearchSseClient.aiStop(productRequestVo)).thenReturn(responseBaseVo);

        boolean result = searchRecProxy.aiStop(messageId);

        assertFalse(result);
        verify(aiSearchSseClient).aiStop(eq(productRequestVo));
    }

    @Test
    void aiStop_ValidMessageId_ThrowsException_ReturnsFalse() {
        when(aiSearchSseClient.aiStop(any(ProductRequestVo.class)))
            .thenThrow(new RuntimeException("Exception"));

        assertFalse(searchRecProxy.aiStop("validMessageId"));
    }


    @Test
    void aiStop_BlankMessageId_ReturnsFalse() {
        assertFalse(searchRecProxy.aiStop(""));
    }

    @Test
    void testProcessAiChatContent_WithValidContentAndAppClientId() {
        String content = "testContent";
        String appClientId = "testAppClientId";
        String expectedProcessedContent = "processedTestContent";

        when(linkHandler.convert(anyString(), anyString())).thenReturn(expectedProcessedContent);

        String result = searchRecProxy.processAiChatContent(content, appClientId);

        assertEquals(expectedProcessedContent, result);
        verify(linkHandler).convert(content, appClientId);
    }

    @ParameterizedTest
    @CsvSource({
        ", testAppClientId",
        "testContent, ",
        ", "
    })
    void testProcessAiChatContent_WithBlankContent(String content, String appClientId) {
        String expectedProcessedContent = content;

        String result = searchRecProxy.processAiChatContent(content, appClientId);

        assertEquals(expectedProcessedContent, result);
        verify(linkHandler, never()).convert(anyString(), anyString());
    }
}
