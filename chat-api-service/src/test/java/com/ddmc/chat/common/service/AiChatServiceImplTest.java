package com.ddmc.chat.common.service;

import static com.ddmc.chat.common.domain.Constants.SETTING_KEY_REASON_SWITCH;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.chat.common.domain.dto.req.ai.AiChatCompletionReq;
import com.ddmc.chat.common.domain.dto.req.ai.AiChatStopReq;
import com.ddmc.chat.common.domain.dto.req.ai.QueryConfigReq;
import com.ddmc.chat.common.domain.dto.req.ai.UpdateConfigReq;
import com.ddmc.chat.common.domain.dto.res.QueryConfigResponse;
import com.ddmc.chat.common.domain.entity.MessageContext;
import com.ddmc.chat.common.infra.config.DeepSeekConfig;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.dao.UserSettingsDAO;
import com.ddmc.chat.common.infra.handler.DeepSeekHandler;
import com.ddmc.chat.common.infra.proxy.ABTestProxy;
import com.ddmc.chat.common.infra.proxy.ChatContext;
import com.ddmc.chat.common.infra.proxy.NlpAiProxy;
import com.ddmc.chat.common.infra.proxy.SearchRecProxy;
import com.ddmc.chat.common.service.impl.AiChatServiceImpl;
import com.ddmc.chat.common.util.OUIDGenerator;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


@RunWith(PowerMockRunner.class)
@PrepareForTest({AiChatServiceImpl.class})
class AiChatServiceImplTest {

    @Mock
    private NlpAiProxy nlpAiProxy;

    @Mock
    private ABTestProxy abTestProxy;

    @Mock
    private GlobalApolloConfig globalApolloConfig;

    @Mock
    private SearchRecProxy searchRecProxy;

    @Mock
    private UserSettingsDAO userSettingsDAO;

    @Mock
    private DeepSeekHandler deepSeekHandler;

    @InjectMocks
    private AiChatServiceImpl aiChatService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCompletions_HitExperiment() throws Exception {
        // Arrange
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        completionReq.setQid("req123");
        completionReq.setQuestion("Hello");
        completionReq.setScene("search_result_assistant");

        SseEmitter sseEmitter = new SseEmitter();

        String scene = "search_result_assistant";
        MessageContext messageContext = new MessageContext();
        messageContext.setQuestId(OUIDGenerator.generate("q"));
        messageContext.setSessionId(OUIDGenerator.generate("s"));

        ChatContext chatContext = ChatContext.of(completionReq);

        when(globalApolloConfig.getAiChatDeepSeekAbtestExpId()).thenReturn("expId");
        when(abTestProxy.abTest(chatContext, "expId")).thenReturn("exp");
        when(searchRecProxy.aiSearch(eq(completionReq), eq(sseEmitter), any(MessageContext.class))).thenReturn(sseEmitter);

        // Act
        SseEmitter result = aiChatService.completions(scene, completionReq, sseEmitter);

        // Assert
        assertNotNull(result);
        verify(searchRecProxy, times(1)).aiSearch(eq(completionReq), eq(sseEmitter), any(MessageContext.class));
        verify(nlpAiProxy, never()).streamCall(any(), any(), any());
    }

    @Test
    void testCompletions_NotHitExperiment() throws Exception {
        // Arrange
        AiChatCompletionReq completionReq = new AiChatCompletionReq();
        completionReq.setQid("req123");
        completionReq.setQuestion("Hello");
        completionReq.setScene("search_result_assistant");

        SseEmitter sseEmitter = new SseEmitter();

        String scene = "search_result_assistant";
        MessageContext messageContext = new MessageContext();
        messageContext.setQuestId(OUIDGenerator.generate("q"));
        messageContext.setSessionId(OUIDGenerator.generate("s"));

        ChatContext chatContext = ChatContext.of(completionReq);

        when(globalApolloConfig.getAiChatDeepSeekAbtestExpId()).thenReturn("expId");
        when(abTestProxy.abTest(chatContext, "expId")).thenReturn("control");
        when(nlpAiProxy.streamCall(eq(completionReq), eq(sseEmitter), any(MessageContext.class))).thenReturn(sseEmitter);

        // Act
        SseEmitter result = aiChatService.completions(scene, completionReq, sseEmitter);

        // Assert
        assertNotNull(result);
        verify(nlpAiProxy, times(1)).streamCall(eq(completionReq), eq(sseEmitter), any(MessageContext.class));
        verify(searchRecProxy, never()).aiSearch(any(), any(), any());
    }

    @Test
    void testStop_HitExperiment() {
        // Arrange
        AiChatStopReq stopReq = new AiChatStopReq();
        when(searchRecProxy.aiStop(String.valueOf(stopReq.getMessageId()))).thenReturn(true);
        when(nlpAiProxy.stopStream(String.valueOf(stopReq.getMessageId()))).thenReturn(true);

        // Act
        Boolean result = aiChatService.stop(stopReq);

        // Assert
        assertTrue(result);
        verify(searchRecProxy, times(1)).aiStop(String.valueOf(stopReq.getMessageId()));
        verify(nlpAiProxy, times(1)).stopStream(String.valueOf(stopReq.getMessageId()));
    }

    @Test
    void testStop_NotHitExperiment() {
        // Arrange
        AiChatStopReq stopReq = new AiChatStopReq();
        when(searchRecProxy.aiStop(String.valueOf(stopReq.getMessageId()))).thenReturn(true);
        when(nlpAiProxy.stopStream(String.valueOf(stopReq.getMessageId()))).thenReturn(true);

        // Act
        Boolean result = aiChatService.stop(stopReq);

        // Assert
        assertTrue(result);
        verify(searchRecProxy, times(1)).aiStop(String.valueOf(stopReq.getMessageId()));
        verify(nlpAiProxy, times(1)).stopStream(String.valueOf(stopReq.getMessageId()));
    }

    @Test
    void updateConfig_EmptyUid_ThrowsIllegalArgumentException() {
        UpdateConfigReq updateConfigReq = new UpdateConfigReq("", "123", 1);

        assertThrows(IllegalArgumentException.class, () -> {
            aiChatService.updateConfig(updateConfigReq);
        });
    }

    @Test
    void updateConfig_NonEmptyUid_ReturnsTrue() {
        UpdateConfigReq updateConfigReq = new UpdateConfigReq("user123", "123", 1);
        Map<String, Integer> settingsMap = new HashMap<>();
        settingsMap.put(SETTING_KEY_REASON_SWITCH, 1);

        when(userSettingsDAO.saveOrUpdateUserSettings("user123", settingsMap)).thenReturn(true);

        boolean result = aiChatService.updateConfig(updateConfigReq);

        assertTrue(result);
        verify(userSettingsDAO, times(1)).saveOrUpdateUserSettings("user123", settingsMap);
    }

    @Test
    void queryConfig_EmptyUid_ThrowsIllegalArgumentException() {
        QueryConfigReq queryConfigReq = new QueryConfigReq("", "testQid", "");

        assertThrows(IllegalArgumentException.class, () -> {
            aiChatService.queryConfig(queryConfigReq);
        });
    }

    @Test
    void queryConfig_NullDeepSeekConfig_ReturnsEmptyResponse() {
        QueryConfigReq queryConfigReq = new QueryConfigReq("testUid", "testQid", "");
        when(globalApolloConfig.getDeepSeekConfig()).thenReturn(null);

        QueryConfigResponse response = aiChatService.queryConfig(queryConfigReq);

        assertNotNull(response);
        assertNull(response.getModelName());
        assertNull(response.getReasonSwitch());
        assertNull(response.getShowProductCnt());
        assertNull(response.getTips());
    }

    @Test
    void queryConfig_DegradeReasonSwitch_ReturnsDefaultValues() {
        QueryConfigReq queryConfigReq = new QueryConfigReq("testUid", "testQid", "");
        DeepSeekConfig deepSeekConfig = new DeepSeekConfig();
        Map<Integer, String> modelNameMap = new HashMap<>();
        modelNameMap.put(-1, "DeepSeek");
        modelNameMap.put(0, "DeepSeek");
        modelNameMap.put(1, "DeepSeek-R1");
        deepSeekConfig.setModelNameMap(modelNameMap);
        deepSeekConfig.setProductHomePageShowTotalNumber(10);
        deepSeekConfig.setTips("defaultTips");
        deepSeekConfig.setDsCompositeModelName("DeepSeek");
        when(globalApolloConfig.getDeepSeekConfig()).thenReturn(deepSeekConfig);
        when(deepSeekHandler.isDeepSeekReasonSwitchDegrade()).thenReturn(true);

        QueryConfigResponse response = aiChatService.queryConfig(queryConfigReq);

        assertNotNull(response);
        assertEquals("DeepSeek", response.getModelName());
        assertEquals(-1, response.getReasonSwitch().intValue());
        assertEquals(deepSeekConfig.getProductHomePageShowTotalNumber(), response.getShowProductCnt().intValue());
        assertEquals(deepSeekConfig.getTips(), response.getTips());
    }

    @Test
    void queryConfig_Composite() {
        QueryConfigReq queryConfigReq = new QueryConfigReq("testUid", "testQid", "search_result_assistant_composite");
        DeepSeekConfig deepSeekConfig = new DeepSeekConfig();
        Map<Integer, String> modelNameMap = new HashMap<>();
        modelNameMap.put(-1, "DeepSeek");
        modelNameMap.put(0, "DeepSeek");
        modelNameMap.put(1, "DeepSeek-R1");
        deepSeekConfig.setModelNameMap(modelNameMap);
        deepSeekConfig.setProductHomePageShowTotalNumber(10);
        deepSeekConfig.setTips("defaultTips");
        deepSeekConfig.setDsCompositeModelName("DeepSeek");
        when(globalApolloConfig.getDeepSeekConfig()).thenReturn(deepSeekConfig);

        QueryConfigResponse response = aiChatService.queryConfig(queryConfigReq);

        assertNotNull(response);
        assertEquals(deepSeekConfig.getDsCompositeModelName(), response.getModelName());
        assertEquals(deepSeekConfig.getTips(), response.getTips());
    }

    @Test
    void queryConfig_NonDegradeReasonSwitch_ReturnsUserSpecificValues() {
        QueryConfigReq queryConfigReq = new QueryConfigReq("testUid", "testQid", "");
        DeepSeekConfig deepSeekConfig = new DeepSeekConfig();
        Map<Integer, String> modelNameMap = new HashMap<>();
        modelNameMap.put(-1, "DeepSeek");
        modelNameMap.put(0, "DeepSeek");
        modelNameMap.put(1, "DeepSeek-R1");
        deepSeekConfig.setModelNameMap(modelNameMap);
        deepSeekConfig.setProductHomePageShowTotalNumber(15);
        deepSeekConfig.setTips("userTips");
        deepSeekConfig.setDsCompositeModelName("DeepSeek");
        when(globalApolloConfig.getDeepSeekConfig()).thenReturn(deepSeekConfig);
        when(deepSeekHandler.isDeepSeekReasonSwitchDegrade()).thenReturn(false);
        when(deepSeekHandler.queryReasonSwitch("testUid")).thenReturn(1);

        QueryConfigResponse response = aiChatService.queryConfig(queryConfigReq);

        assertNotNull(response);
        assertEquals("DeepSeek-R1", response.getModelName());
        assertEquals(1, response.getReasonSwitch().intValue());
        assertEquals(15, response.getShowProductCnt().intValue());
        assertEquals(deepSeekConfig.getTips(), response.getTips());
    }

    @Test
    void test_isInvokeAiSearch() throws Exception {
        DeepSeekConfig deepSeekConfig = new DeepSeekConfig();
        Map<Integer, String> modelNameMap = new HashMap<>();
        modelNameMap.put(-1, "DeepSeek");
        modelNameMap.put(0, "DeepSeek");
        modelNameMap.put(1, "DeepSeek-R1");
        deepSeekConfig.setModelNameMap(modelNameMap);
        deepSeekConfig.setProductHomePageShowTotalNumber(15);
        deepSeekConfig.setTips("userTips");
        deepSeekConfig.setSourceTypeList(Arrays.asList(1, 2));
        deepSeekConfig.setDsCompositeModelName("DeepSeek");
        when(globalApolloConfig.getDeepSeekConfig()).thenReturn(deepSeekConfig);
        String aiParam = "{\"intentionInfo\":{\"intentionWord\":\"test\"}, \"sourceType\": \"1\"}";
        Object isInvokeAiSearch = Whitebox.invokeMethod(aiChatService, "isInvokeAiSearch", aiParam);
        assertTrue((Boolean) isInvokeAiSearch);
        String aiParam2 = "{\"sourceType\": \"1\"}";
        Object isInvokeAiSearch2 = Whitebox.invokeMethod(aiChatService, "isInvokeAiSearch", aiParam2);
        assertTrue((Boolean) isInvokeAiSearch2);
        String aiParam3 = "{\"intentionInfo\":{\"intentionWord\":\"test\"}}";
        Object isInvokeAiSearch3 = Whitebox.invokeMethod(aiChatService, "isInvokeAiSearch", aiParam3);
        assertTrue((Boolean) isInvokeAiSearch3);
        String aiParam4 = "";
        Object isInvokeAiSearch4 = Whitebox.invokeMethod(aiChatService, "isInvokeAiSearch", aiParam4);
        assertFalse((Boolean) isInvokeAiSearch4);
    }

}
