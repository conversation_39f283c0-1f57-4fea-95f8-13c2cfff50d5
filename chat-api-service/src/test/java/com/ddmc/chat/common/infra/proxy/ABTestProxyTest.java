package com.ddmc.chat.common.infra.proxy;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.abtest.client.AbTestClient;
import com.ddmc.abtest.dto.AbResDTO;
import com.ddmc.abtest.dto.AbTestHitResultDTO;
import com.ddmc.abtest.dto.AbTestRespDTO;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ABTestProxyTest {

    @Mock
    private AbTestClient abTestClient;

    @Mock
    private GlobalApolloConfig globalApolloConfig;

    @InjectMocks
    private ABTestProxy abTestProxy;

    private ChatContext chatContext;

    @BeforeEach
    void setUp() {
        chatContext = new ChatContext();
        chatContext.setUid("60dd907c9cbe520001e63445");
        chatContext.setDeviceId("775b63b75eef0f172e07dd5c46283136fd4f5793");
        chatContext.setAppVersion("1.0.0");
        chatContext.setAppClientId(3);
        chatContext.setCityNumber("0101");
        chatContext.setStationId("5b50430dc0a1eac91e8b4bc2");
    }

    @Test
    void testAbTest_Success() {
        // Arrange
        String expLayerId = "exp1";
        String expectedGroup = "groupA";

        AbTestHitResultDTO hitResultDTO = new AbTestHitResultDTO();
        hitResultDTO.setHit(true);
        hitResultDTO.setGroup(expectedGroup);

        Map<String, AbTestHitResultDTO> hitResultMap = new HashMap<>();
        hitResultMap.put(expLayerId, hitResultDTO);

        AbTestRespDTO abTestRespDTO = new AbTestRespDTO();
        abTestRespDTO.setHitResult(hitResultMap);

        AbResDTO<AbTestRespDTO> abResDTO = new AbResDTO<>();
        abResDTO.setErrNo(0);
        abResDTO.setData(abTestRespDTO);

        when(abTestClient.ab2(any(), eq(expLayerId), any(), any(), any(), any(), any())).thenReturn(abResDTO);
        when(globalApolloConfig.getAbDegradeSwitch()).thenReturn(0);

        // Act
        String result = abTestProxy.abTest(chatContext, expLayerId);

        // Assert
        assertEquals(expectedGroup, result);
        verify(abTestClient, times(1)).ab2(eq(chatContext.getUid()), eq(expLayerId), eq(chatContext.getDeviceId()),
            eq(chatContext.getAppVersion()), eq(chatContext.getAppClientId()), eq(chatContext.getCityNumber()),
            eq(chatContext.getStationId()));
    }

    @Test
    void testAbTest_NoHit() {
        // Arrange
        String expLayerId = "exp1";

        AbTestHitResultDTO hitResultDTO = new AbTestHitResultDTO();
        hitResultDTO.setHit(false);
        hitResultDTO.setGroup("groupA");

        Map<String, AbTestHitResultDTO> hitResultMap = new HashMap<>();
        hitResultMap.put(expLayerId, hitResultDTO);

        AbTestRespDTO abTestRespDTO = new AbTestRespDTO();
        abTestRespDTO.setHitResult(hitResultMap);

        AbResDTO<AbTestRespDTO> abResDTO = new AbResDTO<>();
        abResDTO.setErrNo(0);
        abResDTO.setData(abTestRespDTO);

        when(abTestClient.ab2(any(), eq(expLayerId), any(), any(), any(), any(), any())).thenReturn(abResDTO);
        when(globalApolloConfig.getAbDegradeSwitch()).thenReturn(0);

        // Act
        String result = abTestProxy.abTest(chatContext, expLayerId);

        // Assert
        assertNull(result);
        verify(abTestClient, times(1)).ab2(eq(chatContext.getUid()), eq(expLayerId), eq(chatContext.getDeviceId()),
            eq(chatContext.getAppVersion()), eq(chatContext.getAppClientId()), eq(chatContext.getCityNumber()),
            eq(chatContext.getStationId()));
    }

    @Test
    void testAbTest_BlankExpLayerId() {
        // Arrange
        String expLayerId = "";

        when(globalApolloConfig.getAbDegradeSwitch()).thenReturn(0);
        // Act
        String result = abTestProxy.abTest(chatContext, expLayerId);

        // Assert
        assertNull(result);
        verify(abTestClient, never()).ab2(any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    void testAbTest_Exception() {
        // Arrange
        String expLayerId = "exp1";

        when(abTestClient.ab2(any(), eq(expLayerId), any(), any(), any(), any(), any())).thenThrow(new RuntimeException("Test Exception"));
        when(globalApolloConfig.getAbDegradeSwitch()).thenReturn(0);

        // Act
        String result = abTestProxy.abTest(chatContext, expLayerId);

        // Assert
        assertNull(result);
        verify(abTestClient, times(1)).ab2(eq(chatContext.getUid()), eq(expLayerId), eq(chatContext.getDeviceId()),
            eq(chatContext.getAppVersion()), eq(chatContext.getAppClientId()), eq(chatContext.getCityNumber()),
            eq(chatContext.getStationId()));
    }

    @Test
    void testAbTest_ErrorResponse() {
        // Arrange
        String expLayerId = "exp1";

        AbResDTO<AbTestRespDTO> abResDTO = new AbResDTO<>();
        abResDTO.setErrNo(1);
        abResDTO.setData(null);

        when(abTestClient.ab2(any(), eq(expLayerId), any(), any(), any(), any(), any())).thenReturn(abResDTO);
        when(globalApolloConfig.getAbDegradeSwitch()).thenReturn(0);

        // Act
        String result = abTestProxy.abTest(chatContext, expLayerId);

        // Assert
        assertNull(result);
        verify(abTestClient, times(1)).ab2(eq(chatContext.getUid()), eq(expLayerId), eq(chatContext.getDeviceId()),
            eq(chatContext.getAppVersion()), eq(chatContext.getAppClientId()), eq(chatContext.getCityNumber()),
            eq(chatContext.getStationId()));
    }

    @Test
    void testAbTest_NoHitResult() {
        // Arrange
        String expLayerId = "exp1";

        AbTestRespDTO abTestRespDTO = new AbTestRespDTO();
        abTestRespDTO.setHitResult(null);

        AbResDTO<AbTestRespDTO> abResDTO = new AbResDTO<>();
        abResDTO.setErrNo(0);
        abResDTO.setData(abTestRespDTO);

        when(abTestClient.ab2(any(), eq(expLayerId), any(), any(), any(), any(), any())).thenReturn(abResDTO);
        when(globalApolloConfig.getAbDegradeSwitch()).thenReturn(0);

        // Act
        String result = abTestProxy.abTest(chatContext, expLayerId);

        // Assert
        assertNull(result);
        verify(abTestClient, times(1)).ab2(eq(chatContext.getUid()), eq(expLayerId), eq(chatContext.getDeviceId()),
            eq(chatContext.getAppVersion()), eq(chatContext.getAppClientId()), eq(chatContext.getCityNumber()),
            eq(chatContext.getStationId()));
    }

    @Test
    void testAbTest_MissingExpLayerIdInHitResult() {
        // Arrange
        String expLayerId = "exp1";

        AbTestHitResultDTO hitResultDTO = new AbTestHitResultDTO();
        hitResultDTO.setHit(true);
        hitResultDTO.setGroup("groupA");

        Map<String, AbTestHitResultDTO> hitResultMap = new HashMap<>();
        hitResultMap.put("exp2", hitResultDTO);

        AbTestRespDTO abTestRespDTO = new AbTestRespDTO();
        abTestRespDTO.setHitResult(hitResultMap);

        AbResDTO<AbTestRespDTO> abResDTO = new AbResDTO<>();
        abResDTO.setErrNo(0);
        abResDTO.setData(abTestRespDTO);

        when(abTestClient.ab2(any(), eq(expLayerId), any(), any(), any(), any(), any())).thenReturn(abResDTO);
        when(globalApolloConfig.getAbDegradeSwitch()).thenReturn(0);

        // Act
        String result = abTestProxy.abTest(chatContext, expLayerId);

        // Assert
        assertNull(result);
        verify(abTestClient, times(1)).ab2(eq(chatContext.getUid()), eq(expLayerId), eq(chatContext.getDeviceId()),
            eq(chatContext.getAppVersion()), eq(chatContext.getAppClientId()), eq(chatContext.getCityNumber()),
            eq(chatContext.getStationId()));
    }
}
