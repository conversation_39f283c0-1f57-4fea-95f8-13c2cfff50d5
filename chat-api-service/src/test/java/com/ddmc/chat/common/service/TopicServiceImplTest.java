package com.ddmc.chat.common.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.ddmc.chat.common.domain.convert.ChatReqConvert;
import com.ddmc.chat.common.domain.dto.req.chat.ClearMessageReq;
import com.ddmc.chat.common.domain.dto.req.chat.CreateTopicReq;
import com.ddmc.chat.common.domain.dto.req.chat.DeleteMessageReq;
import com.ddmc.chat.common.domain.vo.ChatAggregateVO;
import com.ddmc.chat.common.domain.vo.DeleteChatAggregateVO;
import com.ddmc.chat.common.enums.MessageTypeEnum;
import com.ddmc.chat.common.exception.BizException;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.dao.MessageBoxDAO;
import com.ddmc.chat.common.infra.dao.TopicsDAO;
import com.ddmc.chat.common.infra.proxy.NlpAiProxy;
import com.ddmc.chat.common.mapper.DO.MessageBoxDO;
import com.ddmc.chat.common.mapper.DO.TopicsDO;
import com.ddmc.chat.common.service.impl.TopicServiceImpl;
import com.ddmc.chat.common.util.OUIDGenerator;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({TopicServiceImpl.class})
class TopicServiceImplTest {

    @InjectMocks
    private TopicServiceImpl topicServiceImpl;

    @Mock
    private TopicsDAO topicsDAO;

    @Mock
    private ChatReqConvert chatReqConvert;

    @Mock
    private MessageBoxDAO messageBoxDAO;

    @Mock
    private GlobalApolloConfig globalApolloConfig;

    @Mock
    private NlpAiProxy nlpAiProxy;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void testCreateTopic_Success() {
        CreateTopicReq req = mock(CreateTopicReq.class);
        when(req.getUid()).thenReturn("user1");
        when(req.getSessionId()).thenReturn("session1");

        try (MockedStatic<OUIDGenerator> mockedOUID = mockStatic(OUIDGenerator.class)) {
            mockedOUID.when(() -> OUIDGenerator.generate("t")).thenReturn("topic1");

            TopicsDO topicsDO = new TopicsDO();
            topicsDO.setTopicId("topic1");
            topicsDO.setUserId("user1");

            when(topicsDAO.save(any(TopicsDO.class))).thenReturn(true);

            ChatAggregateVO result = topicServiceImpl.createTopic(req);

            assertNotNull(result);
            assertNotNull(result.getChatTopic());
            assertEquals("session1", result.getChatTopic().getSessionId());
            assertEquals("topic1", result.getChatTopic().getTopicId());
        }
    }

    @Test
    void testCreateTopic_Failure() {
        CreateTopicReq req = mock(CreateTopicReq.class);
        when(req.getUid()).thenReturn("user1");

        try (MockedStatic<OUIDGenerator> mockedOUID = mockStatic(OUIDGenerator.class)) {
            mockedOUID.when(() -> OUIDGenerator.generate("t")).thenReturn("topic1");

            when(topicsDAO.save(any(TopicsDO.class))).thenReturn(false);

            assertThrows(BizException.class, () -> topicServiceImpl.createTopic(req));
        }
    }

    @Test
    void testDeleteMessageById_InvalidParam() {
        DeleteMessageReq req = mock(DeleteMessageReq.class);
        when(req.getMessageIdList()).thenReturn(null);
        when(globalApolloConfig.getDeleteNumber()).thenReturn(100);

        assertThrows(BizException.class, () -> topicServiceImpl.deleteMessageById(req));
    }

    @Test
    void testDeleteMessageById_MessageNotExist() {
        DeleteMessageReq req = mock(DeleteMessageReq.class);
        when(req.getMessageIdList()).thenReturn(Collections.emptyList());
        when(req.getUid()).thenReturn("user1");
        when(globalApolloConfig.getDeleteNumber()).thenReturn(100);

        when(messageBoxDAO.selectMessageById(anyString(), anyList())).thenReturn(Collections.emptyList());

        assertThrows(BizException.class, () -> topicServiceImpl.deleteMessageById(req));
    }

    @Test
    void testDeleteMessageById_DeleteFailed() {
        DeleteMessageReq req = mock(DeleteMessageReq.class);
        when(req.getMessageIdList()).thenReturn(Arrays.asList(1L, 2L));
        when(req.getUid()).thenReturn("user1");
        when(globalApolloConfig.getDeleteNumber()).thenReturn(100);

        MessageBoxDO message = new MessageBoxDO();
        message.setMessageId(1L);
        message.setSessionId("session1");
        when(messageBoxDAO.selectMessageById(anyString(), anyList())).thenReturn(Arrays.asList(message));

        when(messageBoxDAO.selectMessageBySessionId(anyString(), anyList())).thenReturn(Collections.emptyList());

        when(messageBoxDAO.deleteMessageById(anyString(), anyList())).thenReturn(false);

        assertThrows(BizException.class, () -> topicServiceImpl.deleteMessageById(req));
    }

    @Test
    void testDeleteMessageById_NoNeedNewTopic() {
        DeleteMessageReq req = mock(DeleteMessageReq.class);
        when(req.getMessageIdList()).thenReturn(Arrays.asList(1L, 2L));
        when(req.getUid()).thenReturn("user1");
        when(globalApolloConfig.getDeleteNumber()).thenReturn(100);

        MessageBoxDO message = new MessageBoxDO();
        message.setMessageId(1L);
        message.setSessionId("session1");
        when(messageBoxDAO.selectMessageById(anyString(), anyList())).thenReturn(Arrays.asList(message));

        when(messageBoxDAO.selectMessageBySessionId(anyString(), anyList())).thenReturn(Collections.emptyList());

        when(messageBoxDAO.deleteMessageById(anyString(), anyList())).thenReturn(true);

        DeleteChatAggregateVO result = topicServiceImpl.deleteMessageById(req);
        assertFalse(result.getDeleteAll());
        assertNotNull(result.getChatTopic());
    }

    @Test
    void testDeleteMessageById_NeedNewTopic() {
        DeleteMessageReq req = mock(DeleteMessageReq.class);
        when(req.getMessageIdList()).thenReturn(Arrays.asList(1L, 2L));
        when(req.getUid()).thenReturn("user1");
        when(globalApolloConfig.getDeleteNumber()).thenReturn(100);

        MessageBoxDO message = new MessageBoxDO();
        message.setMessageId(1L);
        message.setSessionId("session1");
        when(messageBoxDAO.selectMessageById(anyString(), anyList())).thenReturn(Arrays.asList(message));

        MessageBoxDO segmentMsg = new MessageBoxDO();
        segmentMsg.setMessageId(3L);
        segmentMsg.setType(MessageTypeEnum.SEGMENT.getCode());
        segmentMsg.setSessionId("session1");

        when(messageBoxDAO.selectMessageBySessionId(anyString(), anyList())).thenReturn(Arrays.asList(segmentMsg));

        when(messageBoxDAO.deleteMessageById(anyString(), anyList())).thenReturn(true);

        when(messageBoxDAO.selectMessageByUserId(anyString(), eq(1), any(LocalDateTime.class))).thenReturn(Collections.emptyList());

        CreateTopicReq createTopicReq = new CreateTopicReq();
        when(chatReqConvert.deleteMessageReq2CreateTopicReq(req)).thenReturn(createTopicReq);

        when(topicsDAO.save(any(TopicsDO.class))).thenReturn(true);

        DeleteChatAggregateVO result = topicServiceImpl.deleteMessageById(req);
        assertTrue(result.getDeleteAll());
        assertNotNull(result.getChatTopic());
    }

    @Test
    void testClearMessage_NoMessages() {
        ClearMessageReq req = mock(ClearMessageReq.class);
        when(req.getUid()).thenReturn("user1");
        LocalDateTime timeLimit = LocalDateTime.now().minusDays(7);
        when(globalApolloConfig.getHistoryDateLimit()).thenReturn(7);

        when(messageBoxDAO.selectMessageByUserId(anyString(), eq(1), eq(timeLimit))).thenReturn(Collections.emptyList());

        ChatAggregateVO result = topicServiceImpl.clearMessage(req);
        assertNull(result.getChatTopic());
    }

    @Test
    void testClearMessage_DeleteFailed() {
        ClearMessageReq req = mock(ClearMessageReq.class);
        when(req.getUid()).thenReturn("user1");
        LocalDateTime timeLimit = LocalDateTime.now().minusDays(7);
        when(globalApolloConfig.getHistoryDateLimit()).thenReturn(7);

        when(messageBoxDAO.selectMessageByUserId(anyString(), any(), any())).thenReturn(Collections.singletonList(mock(MessageBoxDO.class)));

        when(messageBoxDAO.deleteMessageByTime(anyString(), eq(timeLimit))).thenReturn(false);

        assertThrows(BizException.class, () -> topicServiceImpl.clearMessage(req));
    }

    @Test
    void testClearMessage_Success() {
        ClearMessageReq req = mock(ClearMessageReq.class);
        when(req.getUid()).thenReturn("user1");
        LocalDateTime timeLimit = LocalDateTime.now().minusDays(7);
        when(globalApolloConfig.getHistoryDateLimit()).thenReturn(7);

        List<MessageBoxDO> messageBoxDOList = new ArrayList<>();
        MessageBoxDO messageBoxDO1 = new MessageBoxDO();
        messageBoxDO1.setMessageId(System.currentTimeMillis());
        messageBoxDOList.add(messageBoxDO1);
        MessageBoxDO messageBoxDO2 = new MessageBoxDO();
        messageBoxDO2.setMessageId(System.currentTimeMillis());
        messageBoxDOList.add(messageBoxDO2);

        when(messageBoxDAO.selectMessageByUserId(anyString(), any(), any())).thenReturn(messageBoxDOList);

        when(messageBoxDAO.deleteMessageByTime(anyString(), any())).thenReturn(true);

        when(nlpAiProxy.deleteMessage(any(), any(), anyList())).thenReturn(true);

        CreateTopicReq createTopicReq = new CreateTopicReq();
        when(chatReqConvert.clearMessageReq2CreateTopicReq(req)).thenReturn(createTopicReq);

        try (MockedStatic<OUIDGenerator> mockedOUID = mockStatic(OUIDGenerator.class)) {
            mockedOUID.when(() -> OUIDGenerator.generate("t")).thenReturn("topic1");

            when(topicsDAO.save(any(TopicsDO.class))).thenReturn(true);

            ChatAggregateVO result = topicServiceImpl.clearMessage(req);
            assertNotNull(result.getChatTopic());
            assertEquals("topic1", result.getChatTopic().getTopicId());
        }
    }

    /**
     * TC01: sessionMessageList 为空
     */
    @Test
    void testIdentifyMessagesAndSessionsToDeleteMessage_SessionListEmpty() {
        String userId = "u1";
        List<String> sessionIdList = Arrays.asList("s1");
        List<Long> deleteMessageIdList = Collections.emptyList();

        when(messageBoxDAO.selectMessageBySessionId(userId, sessionIdList)).thenReturn(Collections.emptyList());

        Pair<List<Long>, List<String>> result = topicServiceImpl.identifyMessagesAndSessionsToDelete(userId, sessionIdList, deleteMessageIdList);

        assertNotNull(result);
        assertTrue(result.getLeft().isEmpty());
        assertTrue(result.getRight().isEmpty());
    }

    /**
     * TC02: 某个 session 中所有消息都被删除
     */
    @Test
    void testIdentifyMessagesAndSessionsToDelete_AllMessagesDeletedInSession() {
        String userId = "u1";
        List<String> sessionIdList = Arrays.asList("s1");
        List<Long> deleteMessageIdList = Arrays.asList(100L, 101L);

        List<MessageBoxDO> messageList = new ArrayList<>();
        messageList.add(createMockMessageBox(100L, "s1", MessageTypeEnum.TEXT.getCode()));
        messageList.add(createMockMessageBox(101L, "s1", MessageTypeEnum.TEXT.getCode()));

        when(messageBoxDAO.selectMessageBySessionId(userId, sessionIdList)).thenReturn(messageList);

        Pair<List<Long>, List<String>> result = topicServiceImpl.identifyMessagesAndSessionsToDelete(userId, sessionIdList, deleteMessageIdList);

        assertNotNull(result);
        assertTrue(result.getLeft().isEmpty());
        assertEquals(Arrays.asList("s1"), result.getRight());
    }

    /**
     * TC03: 某个 session 中只剩一条 SEGMENT 类型消息
     */
    @Test
    void testIdentifyMessagesAndSessionsToDelete_OnlySegmentLeft() {
        String userId = "u1";
        List<String> sessionIdList = Arrays.asList("s1");
        List<Long> deleteMessageIdList = Collections.singletonList(100L);

        List<MessageBoxDO> messageList = new ArrayList<>();
        messageList.add(createMockMessageBox(100L, "s1", MessageTypeEnum.TEXT.getCode())); // 已被删除
        messageList.add(createMockMessageBox(101L, "s1", MessageTypeEnum.SEGMENT.getCode())); // 剩余一条 SEGMENT

        when(messageBoxDAO.selectMessageBySessionId(userId, sessionIdList)).thenReturn(messageList);

        Pair<List<Long>, List<String>> result = topicServiceImpl.identifyMessagesAndSessionsToDelete(userId, sessionIdList, deleteMessageIdList);

        assertNotNull(result);
        assertEquals(Arrays.asList(101L), result.getLeft());
        assertFalse(result.getRight().isEmpty());
    }

    /**
     * TC04: 某个 session 中只剩一条非 SEGMENT 类型消息
     */
    @Test
    void testIdentifyMessagesAndSessionsToDelete_OnlyNonSegmentLeft() {
        String userId = "u1";
        List<String> sessionIdList = Arrays.asList("s1");
        List<Long> deleteMessageIdList = Collections.singletonList(100L);

        List<MessageBoxDO> messageList = new ArrayList<>();
        messageList.add(createMockMessageBox(100L, "s1", MessageTypeEnum.TEXT.getCode())); // 已被删除
        messageList.add(createMockMessageBox(101L, "s1", MessageTypeEnum.IMAGE.getCode())); // 剩余一条 IMAGE

        when(messageBoxDAO.selectMessageBySessionId(userId, sessionIdList)).thenReturn(messageList);

        Pair<List<Long>, List<String>> result = topicServiceImpl.identifyMessagesAndSessionsToDelete(userId, sessionIdList, deleteMessageIdList);

        assertNotNull(result);
        assertTrue(result.getLeft().isEmpty());
        assertTrue(result.getRight().isEmpty());
    }

    /**
     * 构造模拟的 MessageBoxDO 对象
     */
    private MessageBoxDO createMockMessageBox(Long messageId, String sessionId, Integer type) {
        return new MessageBoxDO()
            .setMessageId(messageId)
            .setSessionId(sessionId)
            .setType(type)
            .setOwner("u1")
            .setCreateTime(LocalDateTime.now());
    }
}
