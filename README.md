
## chat-service 项目介绍

1、项目规划了 chat-api-service 和 chat-admin-service 两个应用，分别toC和toB。但是目前只实现了 chat-api-service。C端服务主要是提供聊天功能，包括
开启会话，聊天，结束会话等功能。会话过程中用到的MessageId、SessionId等信息都是服务端生成的。

2、B端服务是用做后台Debug功能时， 查询消息记录的，目前还没有实现。


## 设计文档

[叮咚AI助手](https://cfl.corp.100.me/pages/viewpage.action?pageId=170520402)

[叮小咚AI助手支持语音问答](https://cfl.corp.100.me/pages/viewpage.action?pageId=192894479)

[叮小咚AI助手落地页消息格式增强](https://cfl.corp.100.me/pages/viewpage.action?pageId=193954548)
