package com.ddmc.chat.common.service.impl;

import static com.ddmc.chat.common.domain.Constants.SETTING_KEY_REASON_SWITCH;
import static com.ddmc.chat.common.domain.Constants.SETTING_VALUE_DEFAULT;

import com.ddmc.chat.common.domain.dto.req.ai.AiChatCompletionReq;
import com.ddmc.chat.common.domain.dto.req.ai.AiChatStopReq;
import com.ddmc.chat.common.domain.dto.req.ai.QueryConfigReq;
import com.ddmc.chat.common.domain.dto.req.ai.UpdateConfigReq;
import com.ddmc.chat.common.domain.dto.res.QueryConfigResponse;
import com.ddmc.chat.common.domain.entity.MessageContext;
import com.ddmc.chat.common.domain.vo.AiParamVO;
import com.ddmc.chat.common.enums.DsSceneEnum;
import com.ddmc.chat.common.infra.config.DeepSeekConfig;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.dao.UserSettingsDAO;
import com.ddmc.chat.common.infra.handler.DeepSeekHandler;
import com.ddmc.chat.common.infra.proxy.ABTestProxy;
import com.ddmc.chat.common.infra.proxy.ChatContext;
import com.ddmc.chat.common.infra.proxy.NlpAiProxy;
import com.ddmc.chat.common.infra.proxy.SearchRecProxy;
import com.ddmc.chat.common.service.AiChatService;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.chat.common.util.OUIDGenerator;
import com.ddmc.guide.enhance.json.JsonUtil;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AiChatServiceImpl implements AiChatService {

    @Autowired
    private NlpAiProxy nlpAiProxy;

    @Autowired
    private ABTestProxy abTestProxy;

    @Autowired
    private GlobalApolloConfig globalApolloConfig;

    @Autowired
    private SearchRecProxy searchRecProxy;

    @Autowired
    private UserSettingsDAO userSettingsDAO;

    @Autowired
    private DeepSeekHandler deepSeekHandler;

    /**
     * 发送、重新发送问题
     *
     * @param completionReqReq
     * @param sseEmitter
     */
    @Override
    public SseEmitter completions(String scene, AiChatCompletionReq completionReqReq, SseEmitter sseEmitter) throws Exception {
        MessageContext messageContext = new MessageContext();
        messageContext.setQuestId(OUIDGenerator.generate("q"));
        // todo:当前场景暂不需要缓存 sessionId，后续加入多轮对话场景时可根据不同 scene 做对应处理
        messageContext.setSessionId(OUIDGenerator.generate("s"));

        ChatContext chatContext = ChatContext.of(completionReqReq);

        if (isInvokeAiSearch(completionReqReq.getAiParam())) {
            // 新场景，必须走新接口
            return searchRecProxy.aiSearch(completionReqReq, sseEmitter, messageContext);
        }
        String testGroup = abTestProxy.abTest(chatContext, globalApolloConfig.getAiChatDeepSeekAbtestExpId());
        if (StringUtils.isNotBlank(testGroup) && "exp".equals(testGroup)) {
            // 命中实验，调用新的 DeepSeek 接口
            return searchRecProxy.aiSearch(completionReqReq, sseEmitter, messageContext);
        }
        //未命中实验，继续调用老的 DeepSeek 接口
        return nlpAiProxy.streamCall(completionReqReq, sseEmitter, messageContext);
    }

    /**
     * 取消回答
     *
     * @param stopReq
     */
    @Override
    public Boolean stop(AiChatStopReq stopReq) {
        try {
            searchRecProxy.aiStop(String.valueOf(stopReq.getMessageId()));
            nlpAiProxy.stopStream(String.valueOf(stopReq.getMessageId()));
        } catch (Exception e) {
            log.error("AiChatServiceImpl stop error", e);
            return false;
        }
        return true;
    }

    /**
     * 更新配置信息
     *
     * @param updateConfigReq req
     * @return
     */
    @Override
    public Boolean updateConfig(UpdateConfigReq updateConfigReq) {
        if (updateConfigReq == null || StringUtils.isBlank(updateConfigReq.getUid())) {
            log.error("updateConfigReq input is invalid, updateConfigReq: {}", updateConfigReq);
            throw new IllegalArgumentException("uid is empty or updateConfigReq is null");
        }
        // 修改 user_setting 表
        return userSettingsDAO.saveOrUpdateUserSettings(updateConfigReq.getUid(), Map.of(SETTING_KEY_REASON_SWITCH, updateConfigReq.getReasonSwitch()));
    }

    /**
     * 查询配置信息
     *
     * @param queryConfigReq req
     * @return
     */
    @Override
    public QueryConfigResponse queryConfig(QueryConfigReq queryConfigReq) {
        QueryConfigResponse res = new QueryConfigResponse();
        if (queryConfigReq == null || StringUtils.isBlank(queryConfigReq.getUid())) {
            log.error("queryConfig input is invalid, queryConfigReq: {}", queryConfigReq);
            throw new IllegalArgumentException("uid is empty or queryConfigReq is null");
        }

        DeepSeekConfig deepSeekConfig = globalApolloConfig.getDeepSeekConfig();
        if (Objects.isNull(deepSeekConfig)) {
            log.warn("query DeepSeek config is null, uid: {}", queryConfigReq.getUid());
            MonitorUtil.counterOnce("DeepSeek", "config_is_null");
            return res;
        }

        // 场景判断
        if (StringUtils.equals(DsSceneEnum.SEARCH_RESULT_ASSISTANT_COMPOSITE.getCode(), queryConfigReq.getScene())) {
            // 主搜综合场景，直接返回配置信息
            res.setModelName(deepSeekConfig.getDsCompositeModelName());
            res.setTips(deepSeekConfig.getTips());
            return res;
        }

        Map<Integer, String> modelNameMap = deepSeekConfig.getModelNameMap();
        if (modelNameMap == null) {
            log.warn("query DeepSeek config model name is null, uid: {}", queryConfigReq.getUid());
            MonitorUtil.counterOnce("DeepSeek", "model_name_config_is_null");
            return res;
        }
        // 获取首页可展示数量
        res.setShowProductCnt(deepSeekConfig.getProductHomePageShowTotalNumber());
        // tips
        res.setTips(deepSeekConfig.getTips());

        // DeepSeek 深度思考降级开关判断
        if (deepSeekHandler.isDeepSeekReasonSwitchDegrade()) {
            // 降级，设置为不展示
            log.info("DeepSeek reason switch is degraded, uid: {}", queryConfigReq.getUid());
            res.setReasonSwitch(SETTING_VALUE_DEFAULT);
            res.setModelName(modelNameMap.get(SETTING_VALUE_DEFAULT));
            return res;
        }

        // 查询用户配置
        Integer reasonSwitch = deepSeekHandler.queryReasonSwitch(queryConfigReq.getUid());
        res.setReasonSwitch(reasonSwitch);
        res.setModelName(modelNameMap.get(reasonSwitch));
        return res;
    }


    private boolean isInvokeAiSearch(String aiParam) {
        try {
            if (StringUtils.isBlank(aiParam)) {
                return false;
            }
            AiParamVO aiParamVO = JsonUtil.toObject(aiParam, AiParamVO.class);
            if (aiParamVO == null) {
                return false;
            }
            // 来源类型: 1,搜索发现 2,搜索提示 3,搜索历史 4,主动跳转DS 5,算法分发DS
            Integer sourceType = aiParamVO.getSourceType();
            DeepSeekConfig deepSeekConfig = globalApolloConfig.getDeepSeekConfig();
            if (sourceType != null
                && deepSeekConfig != null
                && CollectionUtils.isNotEmpty(deepSeekConfig.getSourceTypeList())
                && deepSeekConfig.getSourceTypeList().contains(sourceType)) {
                return true;
            }
            return aiParamVO.getIntentionInfo() != null
                && StringUtils.isNotBlank(aiParamVO.getIntentionInfo().getIntentionWord());
        } catch (Exception e) {
            log.error("AiChatServiceImpl isInvokeAiSearch error", e);
        }
        return false;
    }
}
