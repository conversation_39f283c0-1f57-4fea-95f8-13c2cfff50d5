package com.ddmc.chat.common.service;

import com.ddmc.chat.common.domain.dto.req.feedback.FeedbackReq;
import com.ddmc.chat.common.domain.vo.FeedbackVO;

/**
 * <AUTHOR>
 * @Date 2024/9/4 11:14 AM
 * @Description: 点赞、点踩相关
 */
public interface FeedBackService {

    /**
     * 点评，并返回二次点评列表？算发出还是固定好？
     */
    FeedbackVO feedBack(FeedbackReq entryReq);


    /**
     * 二次点评，并返回感谢文案？算发出还是固定好？
     */
    String feedBack2(FeedbackReq entryReq);

}
