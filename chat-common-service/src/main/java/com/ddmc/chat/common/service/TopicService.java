package com.ddmc.chat.common.service;

import com.ddmc.chat.common.domain.dto.req.chat.ClearMessageReq;
import com.ddmc.chat.common.domain.dto.req.chat.CreateTopicReq;
import com.ddmc.chat.common.domain.dto.req.chat.DeleteMessageReq;
import com.ddmc.chat.common.domain.vo.ChatAggregateVO;
import com.ddmc.chat.common.domain.vo.DeleteChatAggregateVO;

public interface TopicService {

    /**
     * 创建新话题
     *
     * @param req req
     * @return
     */
    ChatAggregateVO createTopic(CreateTopicReq req);

    /**
     * 删除指定聊天记录
     *
     * @param req req
     * @return
     */
    DeleteChatAggregateVO deleteMessageById(DeleteMessageReq req);

    /**
     * 清空聊天记录
     *
     * @param req req
     * @return
     */
    ChatAggregateVO clearMessage(ClearMessageReq req);

}
