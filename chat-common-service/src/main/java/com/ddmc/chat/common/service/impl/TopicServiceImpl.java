package com.ddmc.chat.common.service.impl;

import com.ddmc.chat.common.domain.convert.ChatReqConvert;
import com.ddmc.chat.common.domain.dto.req.chat.ClearMessageReq;
import com.ddmc.chat.common.domain.dto.req.chat.CreateTopicReq;
import com.ddmc.chat.common.domain.dto.req.chat.DeleteMessageReq;
import com.ddmc.chat.common.domain.vo.ChatAggregateVO;
import com.ddmc.chat.common.domain.vo.ChatTopicVO;
import com.ddmc.chat.common.domain.vo.DeleteChatAggregateVO;
import com.ddmc.chat.common.enums.MessageTypeEnum;
import com.ddmc.chat.common.exception.BizException;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.dao.MessageBoxDAO;
import com.ddmc.chat.common.infra.dao.TopicsDAO;
import com.ddmc.chat.common.infra.handler.TopicHandler;
import com.ddmc.chat.common.infra.proxy.NlpAiProxy;
import com.ddmc.chat.common.mapper.DO.MessageBoxDO;
import com.ddmc.chat.common.mapper.DO.TopicsDO;
import com.ddmc.chat.common.service.TopicService;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.chat.common.util.OUIDGenerator;
import com.ddmc.guide.enhance.json.JsonUtil;
import com.google.common.collect.Lists;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 会话管理服务
 */
@Service
@Slf4j
public class TopicServiceImpl implements TopicService {

    @Autowired
    private TopicsDAO topicsDAO;

    @Autowired
    private ChatReqConvert chatReqConvert;

    @Autowired
    private MessageBoxDAO messageBoxDAO;

    @Autowired
    private GlobalApolloConfig globalApolloConfig;

    @Autowired
    private NlpAiProxy nlpAiProxy;

    @Autowired
    private TopicHandler topicHandler;

    private static final String TOPIC_MONITOR_TYPE = "topic_op";

    /**
     * 创建新话题
     *
     * @param req req
     * @return
     */
    @Override
    public ChatAggregateVO createTopic(CreateTopicReq req) {
        // 创建新话题
        String topicId = OUIDGenerator.generate("t");
        // 构建话题实体
        TopicsDO topicsDO = new TopicsDO();
        topicsDO.setTopicId(topicId);
        topicsDO.setUserId(req.getUid());
        // 保存新话题
        boolean save = topicsDAO.save(topicsDO);
        if (!save) {
            log.error("create topic fail, uid:{}", req.getUid());
            MonitorUtil.counterOnce(TOPIC_MONITOR_TYPE, "create_topic_fail");
            throw new BizException("开启新话题失败");
        }
        // 删除 topicId 缓存
        topicHandler.deleteTopicIdCache(req.getUid());
        // 构建会话元信息
        ChatTopicVO topicVO = ChatTopicVO.builder().sessionId(req.getSessionId()).topicId(topicId).build();
        // 构建返回对象
        return ChatAggregateVO.builder().chatTopic(topicVO).build();
    }

    /**
     * 删除指定聊天记录
     *
     * @param req req
     * @return
     */
    @Override
    public DeleteChatAggregateVO deleteMessageById(DeleteMessageReq req) {
        // 参数检查
        if (CollectionUtils.isEmpty(req.getMessageIdList())
            || CollectionUtils.size(req.getMessageIdList()) > globalApolloConfig.getDeleteNumber()) {
            throw new BizException("参数异常");
        }
        // 构建返回对象
        ChatTopicVO chatTopic = ChatTopicVO.builder().topicId(req.getTopicId()).sessionId(req.getSessionId()).build();
        DeleteChatAggregateVO chatAggregateVO = DeleteChatAggregateVO.builder().chatTopic(chatTopic).deleteAll(false).build();
        // 通过 messageId 反查消息内容
        List<MessageBoxDO> messageList = messageBoxDAO.selectMessageById(req.getUid(), req.getMessageIdList());
        if (CollectionUtils.isEmpty(messageList)) {
            // 没有需要删除的 messageId, 直接返回
            log.info("deleteMessageById query messageList is empty, req: {}", JsonUtil.toJson(req));
            return chatAggregateVO;
        }
        // 需要删除的 messageId
        List<Long> deleteMessageIdList = messageList.stream().map(MessageBoxDO::getMessageId).collect(Collectors.toList());

        // 分段消息处理
        List<String> sessionIdList = messageList.stream().map(MessageBoxDO::getSessionId).distinct().collect(Collectors.toList());
        // 需要删除的分段消息 messageId & 删除了全部消息的 sessionId
        Pair<List<Long>, List<String>> deleteMessage = identifyMessagesAndSessionsToDelete(req.getUid(), sessionIdList, deleteMessageIdList);
        List<Long> deleteSegmentMessageIdList = deleteMessage.getLeft();
        List<String> deleteAllSessionIdList = deleteMessage.getRight();

        // 删除全部消息的 sessionId
        chatAggregateVO.setDeleteAllSessionIdList(deleteAllSessionIdList);

        if (CollectionUtils.isNotEmpty(deleteSegmentMessageIdList)) {
            deleteMessageIdList.addAll(deleteSegmentMessageIdList);
        }

        // 执行事务，删除消息
        deleteMessageIdList = deleteMessageIdList.stream().distinct().collect(Collectors.toList());
        boolean result = messageBoxDAO.deleteMessageById(req.getUid(), deleteMessageIdList);
        if (!result) {
            log.error("deleteMessageById fail, uid:{}", req.getUid());
            MonitorUtil.counterOnce(TOPIC_MONITOR_TYPE, "delete_message_fail");
            throw new BizException("删除消息失败");
        }

        // 通知算法
        List<String> questIdList = messageList.stream().map(MessageBoxDO::getQuestId).distinct().collect(Collectors.toList());
        boolean deleteNlpResult = nlpAiProxy.deleteMessage(req.getUid(), false, questIdList);
        if (!deleteNlpResult) {
            log.error("deleteMessageById nlp fail, uid:{}, deleteMessageIdList:{}", req.getUid(), deleteMessageIdList);
            MonitorUtil.counterOnce(TOPIC_MONITOR_TYPE, "delete_message_nlp_fail");
        }
        // 发送 mq 消息
        topicHandler.syncDeleteMessage(req.getUid(), false, questIdList);

        // 如果没有任何需要删除的分段消息，则意味着没有清空所有聊天记录，无需新开话题
        if (CollectionUtils.isEmpty(deleteSegmentMessageIdList)) {
            // 无需新开话题
            return chatAggregateVO;
        }

        // 反之，如果有需要删除的分段消息 id, 则需要检查是否删除了所有消息
        LocalDateTime localDateTime = LocalDateTime.now().minusDays(globalApolloConfig.getHistoryDateLimit());
        List<MessageBoxDO> messagesPerPage = messageBoxDAO.selectMessageByUserId(req.getUid(), 1, localDateTime);
        if (CollectionUtils.isEmpty(messagesPerPage)) {
            // 删除了所有, 需要新开话题
            CreateTopicReq createTopicReq = chatReqConvert.deleteMessageReq2CreateTopicReq(req);
            ChatAggregateVO aggregateVO = createTopic(createTopicReq);
            if (aggregateVO != null) {
                chatAggregateVO.setChatTopic(aggregateVO.getChatTopic());
            }
            chatAggregateVO.setDeleteAll(true);
            return chatAggregateVO;
        }
        // 无需新开话题
        return chatAggregateVO;
    }

    /**
     * 检查并获取需要删除的分段消息 id 以及删除了所有消息的 sessionId
     *
     * @param userId              用户 id
     * @param sessionIdList       session id
     * @param deleteMessageIdList 需要删除的消息 id
     * @return
     */
    public Pair<List<Long>, List<String>> identifyMessagesAndSessionsToDelete(String userId, List<String> sessionIdList,
        List<Long> deleteMessageIdList) {
        HashSet<Long> deleteMessageIdSet = new HashSet<>();
        HashSet<String> deleteAllSessionIdSet = new HashSet<>();
        List<MessageBoxDO> sessionMessageList = messageBoxDAO.selectMessageBySessionId(userId, sessionIdList);
        if (CollectionUtils.isEmpty(sessionMessageList)) {
            return Pair.of(new ArrayList<>(), new ArrayList<>());
        }
        // 根据 session id 分组
        Map<String, List<MessageBoxDO>> sessionMessageMap =
            sessionMessageList.stream().collect(Collectors.groupingBy(MessageBoxDO::getSessionId));
        // 检查 session 中的消息条数
        for (Map.Entry<String, List<MessageBoxDO>> entry : sessionMessageMap.entrySet()) {
            List<MessageBoxDO> messageBoxDOList = entry.getValue();
            // 去除已经标识好要删除的 messageId
            messageBoxDOList.removeIf(messageBoxDO -> deleteMessageIdList.contains(messageBoxDO.getMessageId()));
            int size = CollectionUtils.size(messageBoxDOList);
            if (size == 0) {
                // 已全部删除
                deleteAllSessionIdSet.add(entry.getKey());
            } else if (size == 1) {
                // 只剩 1 条消息
                MessageBoxDO messageBoxDO = messageBoxDOList.get(0);
                // 且是分段消息，则需要删除
                if (MessageTypeEnum.SEGMENT.getCode().equals(messageBoxDO.getType())) {
                    // 记录消息 id, 最后统一批量删除
                    deleteMessageIdSet.add(messageBoxDO.getMessageId());
                    // 从 redis 移除当前 session 对应分段消息 key
                    topicHandler.deleteSegmentCacheKey(userId, entry.getKey());
                    // 已全部删除
                    deleteAllSessionIdSet.add(entry.getKey());
                }
            }
        }
        return Pair.of(new ArrayList<>(deleteMessageIdSet), new ArrayList<>(deleteAllSessionIdSet));
    }

    /**
     * 清空聊天记录
     *
     * @param req req
     * @return
     */
    @Override
    public ChatAggregateVO clearMessage(ClearMessageReq req) {
        // 先检查可见范围内是否有聊天记录
        LocalDateTime localDateTime = LocalDateTime.now().minusDays(globalApolloConfig.getHistoryDateLimit());
        List<MessageBoxDO> messagesPerPage = messageBoxDAO.selectMessageByUserId(req.getUid(), 1, localDateTime);
        if (CollectionUtils.isEmpty(messagesPerPage)) {
            // 没有聊天记录，直接返回
            return ChatAggregateVO.builder().build();
        }
        // 删除可见范围内的聊天记录
        boolean deleteResult = messageBoxDAO.deleteMessageByTime(req.getUid(), localDateTime);
        if (!deleteResult) {
            log.error("clearMessage fail, uid:{}", req.getUid());
            MonitorUtil.counterOnce(TOPIC_MONITOR_TYPE, "clear_message_fail");
            throw new BizException("清空聊天记录失败");
        }
        // 通知算法清空聊天记录
        boolean deleteNlpResult = nlpAiProxy.deleteMessage(req.getUid(), true, null);
        if (!deleteNlpResult) {
            log.error("clearMessage nlp fail, uid:{}", req.getUid());
            MonitorUtil.counterOnce(TOPIC_MONITOR_TYPE, "clear_message_nlp_fail");
        }
        // 发送 mq 消息
        topicHandler.syncDeleteMessage(req.getUid(), true, Lists.newArrayList());
        // 创建新话题
        CreateTopicReq createTopicReq = chatReqConvert.clearMessageReq2CreateTopicReq(req);
        return createTopic(createTopicReq);
    }
}
