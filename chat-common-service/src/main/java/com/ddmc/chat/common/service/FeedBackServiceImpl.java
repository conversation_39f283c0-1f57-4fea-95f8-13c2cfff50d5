package com.ddmc.chat.common.service;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.ddmc.chat.common.constant.Constans;
import com.ddmc.chat.common.domain.dto.req.feedback.FeedbackReq;
import java.util.List;
import com.ddmc.chat.common.domain.vo.FeedbackVO;
import com.ddmc.chat.common.mapper.DO.MessageFeedbackDO;
import com.ddmc.chat.common.mapper.MessageFeedbackMapper;
import com.google.common.collect.Lists;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/9/4 11:14 AM
 * @Description:
 */
@Service
public class FeedBackServiceImpl implements FeedBackService {

    @Autowired
    private MessageFeedbackMapper feedbackMapper;

    @ApolloJsonValue("${chat.reason.list:[]}")
    private List<String> chatReasonList;

    @Value("${positive.chat.feedback.toast:}")
    private String positiveChatFeedbackToast;

    @Value("${negative.chat.feedback.toast:}")
    private String negativeChatFeedbackToast;

    @Override
    public FeedbackVO feedBack(FeedbackReq entryReq) {
        MessageFeedbackDO feedbackDO = new MessageFeedbackDO();

        feedbackDO.setMessageId(entryReq.getMessageId());
        feedbackDO.setFeedback(entryReq.getComment());
        feedbackDO.setReason(entryReq.getDetailComment());
        feedbackDO.setIsDelete(0);
        feedbackDO.setQuestId(entryReq.getQuestId());
        feedbackMapper.insert(feedbackDO);

        if (Constans.ONE.equals(entryReq.getComment())) {
            return new FeedbackVO(Lists.newArrayList(), positiveChatFeedbackToast);
        } else {
            return new FeedbackVO(chatReasonList, Strings.EMPTY);
        }
    }

    @Override
    public String feedBack2(FeedbackReq entryReq) {
        MessageFeedbackDO feedbackDO = new MessageFeedbackDO();

        feedbackDO.setMessageId(entryReq.getMessageId());
        feedbackDO.setFeedback(entryReq.getComment());
        feedbackDO.setReason(entryReq.getDetailComment());
        feedbackDO.setIsDelete(0);
        feedbackDO.setQuestId(entryReq.getQuestId());
        feedbackMapper.insert(feedbackDO);

        return negativeChatFeedbackToast;
    }
}
