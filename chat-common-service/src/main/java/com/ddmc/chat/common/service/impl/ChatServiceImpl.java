package com.ddmc.chat.common.service.impl;

import static com.ddmc.chat.common.constant.CacheKeyConstants.getAliyunTokenCacheKey;
import static com.ddmc.chat.common.constant.Constans.ALL;
import static com.ddmc.chat.common.domain.Constants.COMPLETIONS_HOME_PAGE_SCENE;
import static com.ddmc.chat.common.domain.Constants.MSG_CARD_PAGE_ID_TITLE;
import static com.ddmc.chat.common.domain.Constants.MSG_SENDER_ASSISTANT;
import static com.ddmc.chat.common.domain.Constants.MSG_TYPE_TEXT;
import static com.ddmc.chat.common.domain.Constants.SETTING_KEY_AUDIO_AUTOPLAY;
import static com.ddmc.chat.common.domain.Constants.SETTING_VALUE_OFF;
import static com.ddmc.chat.common.domain.Constants.SETTING_VALUE_ON;

import cn.hutool.core.util.BooleanUtil;
import com.ddmc.antispam.dto.ImgCheckDTO;
import com.ddmc.antispam.dto.TextCheckDTO;
import com.ddmc.chat.common.constant.Constans;
import com.ddmc.chat.common.domain.bo.ChatEntryConfigBO;
import com.ddmc.chat.common.domain.bo.EntryConfigBO;
import com.ddmc.chat.common.domain.bo.EntrySceneConfigBO;
import com.ddmc.chat.common.domain.bo.VersionRange;
import com.ddmc.chat.common.domain.dto.req.chat.CancelQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatHistoryReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.EntryReq;
import com.ddmc.chat.common.domain.dto.req.chat.SessionReq;
import com.ddmc.chat.common.domain.dto.req.chat.StartReq;
import com.ddmc.chat.common.domain.entity.FunctionButton;
import com.ddmc.chat.common.domain.entity.MessageContext;
import com.ddmc.chat.common.domain.entity.Session;
import com.ddmc.chat.common.domain.entity.SugWords;
import com.ddmc.chat.common.domain.vo.ChatEntryVO;
import com.ddmc.chat.common.domain.vo.DailyTipsVO;
import com.ddmc.chat.common.domain.vo.ISIConfig;
import com.ddmc.chat.common.domain.vo.MessageBoxVO;
import com.ddmc.chat.common.domain.vo.MessageVO;
import com.ddmc.chat.common.domain.vo.ProductInfoVO;
import com.ddmc.chat.common.domain.vo.SessionStartVO;
import com.ddmc.chat.common.enums.EntrySceneEnum;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.dao.UserSettingsDAO;
import com.ddmc.chat.common.infra.handler.MessageBoxHandler;
import com.ddmc.chat.common.infra.handler.TopicHandler;
import com.ddmc.chat.common.infra.proxy.ABTestProxy;
import com.ddmc.chat.common.infra.proxy.AliyunProxy;
import com.ddmc.chat.common.infra.proxy.AntispamProxy;
import com.ddmc.chat.common.infra.proxy.ChatContext;
import com.ddmc.chat.common.infra.proxy.DdfsProxy;
import com.ddmc.chat.common.infra.proxy.NlpAiProxy;
import com.ddmc.chat.common.infra.proxy.SummaryProxy;
import com.ddmc.chat.common.infra.proxy.dto.TextCheckReason;
import com.ddmc.chat.common.infra.utils.AsyncUtils;
import com.ddmc.chat.common.infra.utils.FeatureControlUtil;
import com.ddmc.chat.common.mapper.DO.UserSettingsDO;
import com.ddmc.chat.common.service.ChatService;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.chat.common.util.MsgIdGenerator;
import com.ddmc.chat.common.util.VersionUtils;
import com.ddmc.chat.common.util.OUIDGenerator;
import com.ddmc.guide.enhance.json.JsonUtil;
import com.ddmc.guide.enhance.redis.RedisClient;
import com.ddmc.guide.enhance.rest.SimpleResponseVo;
import com.ddmc.utils.date.DateUtils;
import com.google.common.collect.Lists;
import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * <AUTHOR>
 * @Date 2024/9/4 11:14 AM
 * @Description:
 */
@Service
@Slf4j
public class ChatServiceImpl implements ChatService {

    @Autowired
    private NlpAiProxy nlpAiProxy;

    @Autowired
    private SummaryProxy summaryProxy;

    @Autowired
    private DdfsProxy ddfsProxy;

    @Autowired
    private MessageBoxHandler messageBoxHandler;

    @Autowired
    private AntispamProxy antispamProxy;

    @Autowired
    private AliyunProxy aliyunProxy;

    @Autowired
    private ABTestProxy abTestProxy;

    @Autowired
    private UserSettingsDAO userSettingsDAO;

    @Autowired
    private GlobalApolloConfig globalApolloConfig;

    @Resource(name = "transformersRedisClient")
    private RedisClient transformersRedisClient;

    @Autowired
    private AsyncUtils asyncUtils;

    @Autowired
    private FeatureControlUtil featureControlUtil;

    @Autowired
    private TopicHandler topicHandler;

    private static final String ENTRY_CONFIG_EMPTY_MONITOR_TYPE = "aiEntryConfigEmpty";

    /**
     * 是否能开启会话
     */
    public ChatEntryVO entry(EntryReq entryReq) {
        try {

            // scene 非空 & 非商详场景处理
            if (StringUtils.isNotBlank(entryReq.getScene())
                && !StringUtils.equals(EntrySceneEnum.PRODUCT_DETAIL.getCode(), entryReq.getScene())) {
                return queryEntryByScene(entryReq);
            }

            if (StringUtils.isBlank(entryReq.getUid()) || StringUtils.isBlank(entryReq.getStationId()) ||
                    StringUtils.isBlank(entryReq.getProductId())) {
                return new ChatEntryVO(false, null, null);
            }

            // 获取统一配置项
            ChatEntryConfigBO chatEntryConfigBO = globalApolloConfig.getChatEntryConfigBO();
            // 快速判断，商品黑名单不展示AI入口——现在是否可以移除？
            if (!chatEntryConfigBO.isEnable()
                    || chatEntryConfigBO.getBlackPIds().contains(entryReq.getProductId())
                    || chatEntryConfigBO.getBlackUIds().contains(entryReq.getUid())) {
                return new ChatEntryVO(false, null, null);
            }

            // 这里逻辑做的太复杂，先看内层的版本区间配置，找不到的话再用外层的link做兜底，还要结合开关
            // 获取统一配置项中的版本区间配置
            EntryConfigBO entryConfigByAppVersion = getEntryConfigByAppVersion(entryReq.getAppVersion());
            if (entryConfigByAppVersion != null && !entryConfigByAppVersion.getEnable()) {
                // 分版本降级
                return new ChatEntryVO(false, null, null);
            }

            //  优先从内层获取挑战链接，取不到再fallback到外层
            String link = entryConfigByAppVersion != null ? entryConfigByAppVersion.getUrl() : chatEntryConfigBO.getLink();

            if (chatEntryConfigBO.getWhiteStationIds().contains(entryReq.getStationId()) ||
                    chatEntryConfigBO.getWhiteStationIds().contains(ALL)) {

                ArrayList<String> backendCategoryIds = Lists.newArrayList();
                if (StringUtils.isNotBlank(entryReq.getBackendCategoryPath())) {
                    backendCategoryIds = Lists.newArrayList(entryReq.getBackendCategoryPath().split(","));
                    backendCategoryIds.retainAll(chatEntryConfigBO.getWhiteCategoryIds());
                }

                if (CollectionUtils.isNotEmpty(backendCategoryIds) || chatEntryConfigBO.getWhitePIds().contains(entryReq.getProductId())
                        || chatEntryConfigBO.getWhitePIds().contains(ALL)) {
                    return new ChatEntryVO(true, chatEntryConfigBO.getText(), link);
                }
            }

        } catch (Exception e) {
            log.error(String.valueOf(e));
        }

        return new ChatEntryVO(false, null, null);
    }

    public ChatEntryVO queryEntryByScene(EntryReq entryReq) {
        if (StringUtils.isBlank(entryReq.getUid())
            || StringUtils.isBlank(entryReq.getScene())) {
            return new ChatEntryVO(false, null, null);
        }

        String apiVersion = entryReq.getApiVersion();
        // TODO: 品质之爱 CMS 页面版本判断需要使用 nativeVersion，不能使用 apiVersion 和 appVersion
        if (EntrySceneEnum.LOVE_QUALITY.getCode().equals(entryReq.getScene())) {
            apiVersion = entryReq.getNativeVersion();
        }
        // 读取通用配置，获取是否降级
        if(!featureControlUtil.isFeatureEnabled(entryReq.getAppClientId(), apiVersion, entryReq.getChannel())) {
            MonitorUtil.counterOnce("aiEntryDegrade",  entryReq.getScene());
            return new ChatEntryVO(false, null, null);
        }
        // 未降级，读取具体配置，获取需要下发的跳转链接
        Map<String, EntrySceneConfigBO> configMap = globalApolloConfig.getEntrySceneConfigMap();
        if (MapUtils.isEmpty(configMap)) {
            log.warn("queryEntryByScene config is empty, entryReq:{}", entryReq);
            MonitorUtil.counterOnce(ENTRY_CONFIG_EMPTY_MONITOR_TYPE,  "all");
            return new ChatEntryVO(false, null, null);
        }
        EntrySceneConfigBO sceneConfig = configMap.get(entryReq.getScene());
        if (sceneConfig == null) {
            log.warn("queryEntryByScene config is empty, scene:{}", entryReq.getScene());
            MonitorUtil.counterOnce(ENTRY_CONFIG_EMPTY_MONITOR_TYPE,  "sceneConfigEmpty_" + entryReq.getScene());
            return new ChatEntryVO(false, null, null);
        }
        // 单场景降级开关判断
        if (Objects.equals(sceneConfig.getDegradeSwitch(), Constans.ONE)) {
            MonitorUtil.counterOnce("aiEntryDegrade",  entryReq.getScene());
            return new ChatEntryVO(false, null, null);
        }
        // 检查 version 根据版本号选择 URL
        boolean matchingRule = VersionUtils.isGreaterOrEqual(apiVersion, sceneConfig.getVersionThreshold());
        String targetUrl = matchingRule ? sceneConfig.getAfterUrl() : sceneConfig.getBeforeUrl();
        // 未获取到对应链接
        if (StringUtils.isBlank(targetUrl)) {
            log.warn("queryEntryByScene url config is empty, scene:{}", entryReq.getScene());
            MonitorUtil.counterOnce(ENTRY_CONFIG_EMPTY_MONITOR_TYPE,  "urlEmpty_" + entryReq.getScene());
            return new ChatEntryVO(false, null, null);
        }
        return new ChatEntryVO(true, sceneConfig.getText(), targetUrl);
    }

    public EntryConfigBO getEntryConfigByAppVersion(String appVersion) {
        try {
            ChatEntryConfigBO chatEntryConfigBO = globalApolloConfig.getChatEntryConfigBO();

            if (StringUtils.isBlank(appVersion) || Objects.isNull(chatEntryConfigBO)) {
                return null;
            }
            // 分版本号下发入口链接
            List<VersionRange<EntryConfigBO>> versionRangeList = chatEntryConfigBO.getVersionRangeList();
            if (CollectionUtils.isNotEmpty(versionRangeList)) {
                Optional<VersionRange<EntryConfigBO>> entryConfigOp = versionRangeList.stream()
                        .filter(
                                versionRange -> versionRange.isInRange(appVersion)
                        ).findFirst();
                if (entryConfigOp.isPresent()) {
                    VersionRange<EntryConfigBO> entryConfigVersionRange = entryConfigOp.get();
                    // 根据版本号获取到的配置信息
                    return entryConfigVersionRange.getValue();
                }
            }
        } catch (Exception e) {
            log.error("getEntryConfigByAppVersion error", e);
        }
        return null;
    }

    /**
     * 开启会话，同时用户可能带商品id进入咨询，也可能不带。
     * 但如果带商品id的话，只能有一个商品。
     * 调用这个接口，就必然创建一个新的会话。旧的会话无论是否存在，都不复用。
     */
    public SessionStartVO start(StartReq startReq) {
        if (startReq == null || StringUtils.isBlank(startReq.getUid())) {
            throw new RuntimeException("uid is blank");
        }

        // 提前做好对象转换
        ChatContext chatContext = ChatContext.of(startReq);

        // 开始构造返回对象
        SessionStartVO sessionStartVO = new SessionStartVO();
        sessionStartVO.setAvatar(globalApolloConfig.getAvatar());
        sessionStartVO.setAvatar2(globalApolloConfig.getAvatar2());
        // 可分享轮数
        sessionStartVO.setShareNumber(globalApolloConfig.getShareNumber());
        // 可删除条数
        sessionStartVO.setDeleteNumber(globalApolloConfig.getDeleteNumber());

        // 1、生成Session，内部会记录sessionId到redis
        Session newSession = new Session(messageBoxHandler.createSessionIdAndRecord(), System.currentTimeMillis());
        sessionStartVO.setSession(newSession);

        // 2、调用算法获取 猜你想问、意图引导词、欢迎语
        CompletableFuture<Triple<String, List<String>, List<String>>> chatStartMessageFuture =
                asyncUtils.supplyAsync(() ->
                                nlpAiProxy.getFirstChatStartMessage(chatContext, startReq),
                        "subThreadCall_chatService_start_message"
                );

        // 3、只有请求参数有指定商品id时，才需要进行补全
        CompletableFuture<List<ProductInfoVO>> productInfoFuture;
        if (startReq.getProductId() != null) {
            productInfoFuture = asyncUtils.supplyAsync(() ->
                            summaryProxy.getProductInfoCardByProductIds(chatContext,
                                    MSG_CARD_PAGE_ID_TITLE, Collections.singletonList(startReq.getProductId())),
                    "subThreadCall_chatService_start_product"
            );
        } else {
            productInfoFuture = CompletableFuture.completedFuture(Collections.emptyList());
        }

        // 4、检查历史记录是否存在
        ChatHistoryReq chatHistoryReq = new ChatHistoryReq();
        chatHistoryReq.setLimit(globalApolloConfig.getHistoryMessagePageSize());
        chatHistoryReq.setUid(startReq.getUid());
        CompletableFuture<MessageBoxVO> historyFuture =
                asyncUtils.supplyAsync(() ->
                                history(chatHistoryReq),
                        "subThreadCall_chatService_start_history"
                );

        // 5. 语音功能
        if (Objects.equals(globalApolloConfig.getIsiDegradeSwitch(), Constans.ONE)) {
            log.warn("触发手动降级: ChatServiceImpl.start audio");
            MonitorUtil.counterOnce("triggerDegrade", "audio", "-1", "audio trigger degrade");
            sessionStartVO.setAudioEnabled(false);
        } else {
            sessionStartVO.setAudioEnabled(true);
            ISIConfig isiConfig = new ISIConfig();
            isiConfig.setAppKey(globalApolloConfig.getAliyunAppKey());
            isiConfig.setAsrStreamUrl(globalApolloConfig.getAliyunAppIsiAsrStreamUrl());
            isiConfig.setTtsStreamUrl(globalApolloConfig.getAliyunAppIsiTtsStreamUrl());
            isiConfig.setTone(globalApolloConfig.getAliyunAppTtsTone());
            // manually invoke aliyun token service to get token
            Pair<String, Long> token = getToken(false);
            if (token != null) {
                isiConfig.setToken(token.getLeft());
                isiConfig.setExpireTime(token.getRight());
            }

            sessionStartVO.setIsiConfig(isiConfig);
        }

        // 6. 获取每日小知识 - 只有首页需要展示
        CompletableFuture<DailyTipsVO> dailyTipsFuture;
        if (COMPLETIONS_HOME_PAGE_SCENE.equals(startReq.getScene())) {
            dailyTipsFuture = asyncUtils.supplyAsync(() ->
                            nlpAiProxy.queryDailyTips(chatContext),
                    "subThreadCall_chatService_start_dailyTips"
            );
        } else {
            dailyTipsFuture = CompletableFuture.completedFuture(null);
        }

        // 获取 topicId
        CompletableFuture<String> chatTopicIdFuture =
            asyncUtils.supplyAsync(() ->
                topicHandler.getTopicIdOrInsert(startReq.getUid()),
            "subThreadCall_chatService_getChatTopic"
        );

        // 7. 功能 button 配置
        setFunctionButtonList(sessionStartVO);
        // 8. sug 词配置
        setSugWordsList(sessionStartVO);
        // 9. 显式等待所有异步任务完成并处理结果
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(historyFuture, chatStartMessageFuture,
                productInfoFuture, dailyTipsFuture, chatTopicIdFuture);
        allFutures.join();
        // 9.1 是否有历史消息
        setHistoryFuture(historyFuture, sessionStartVO);
        // 9.2 问候语、引导词
        setStartMessageFuture(chatStartMessageFuture, sessionStartVO, newSession);
        // 9.3 商卡信息不全
        setProductInfoFuture(productInfoFuture, sessionStartVO);
        // 9.4 每日小知识
        setDailyTipsFuture(dailyTipsFuture, sessionStartVO, startReq);
        // 9.5 获取 话题 id
        setChatTopicFuture(chatTopicIdFuture, sessionStartVO);
        // 拍照降级开关
        sessionStartVO.setPhotoEnabled(!Objects.equals(globalApolloConfig.getAiChatPhotoDegradeSwitch(), Constans.ONE));
        return sessionStartVO;
    }

    private void setChatTopicFuture(CompletableFuture<String> chatTopicIdFuture, SessionStartVO sessionStartVO) {
        try {
            String topicId = chatTopicIdFuture.get(globalApolloConfig.getThreadTimeout(), TimeUnit.MILLISECONDS);
            sessionStartVO.setTopicId(topicId);
        } catch (InterruptedException ie) {
            log.error("setChatTopicFuture error", ie);
            MonitorUtil.counterOnce("setChatTopicFuture", "fail");
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("setChatTopicFuture error", e);
            MonitorUtil.counterOnce("setChatTopicFuture", "fail");
        }
    }

    private void setFunctionButtonList(SessionStartVO sessionStartVO) {
        List<FunctionButton> functionButtonList = globalApolloConfig.getFunctionButtonList();
        if (CollectionUtils.isEmpty(functionButtonList)) {
            sessionStartVO.setFunctionButtonList(Lists.newArrayList());
        } else {
            functionButtonList = functionButtonList
                    .stream().sorted(Comparator.comparing(FunctionButton::getSort))
                    .collect(Collectors.toList());
            sessionStartVO.setFunctionButtonList(functionButtonList);
        }
    }

    private void setSugWordsList(SessionStartVO sessionStartVO) {
        List<SugWords> sugList = globalApolloConfig.getSugWordsList();
        if (CollectionUtils.isEmpty(sugList)) {
            sessionStartVO.setSugList(Lists.newArrayList());
        } else {
            sugList = sugList
                    .stream().sorted(Comparator.comparing(SugWords::getSort))
                    .collect(Collectors.toList());
            sessionStartVO.setSugList(sugList);
        }

    }

    private void setDailyTipsFuture(CompletableFuture<DailyTipsVO> dailyTipsFuture,
        SessionStartVO sessionStartVO, StartReq startReq) {
        try {
            // 处理每日小知识
            DailyTipsVO dailyTips = dailyTipsFuture.get(globalApolloConfig.getThreadTimeout(), TimeUnit.MILLISECONDS);
            if (dailyTips != null) {
                sessionStartVO.setDailyTips(dailyTips);
                // 保存每日小知识
                String sessionId = sessionStartVO.getSession().getSessionId();
                Long messageId = MsgIdGenerator.genId();
                dailyTips.setMessageId(messageId);
                boolean result = messageBoxHandler.saveDailyTips(dailyTips, sessionId, messageId, startReq);
                if (result) {
                    MonitorUtil.counterOnce("saveDailyTipsMessage", "success");
                } else {
                    MonitorUtil.counterOnce("saveDailyTipsMessage", "fail");
                }
            }
        } catch (InterruptedException ie) {
            log.error("setDailyTipsFuture error", ie);
            MonitorUtil.counterOnce("processStartDailyTipsSync", "fail");
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("setDailyTipsFuture error", e);
            MonitorUtil.counterOnce("processStartDailyTipsSync", "fail");
        }
    }

    private void setProductInfoFuture(CompletableFuture<List<ProductInfoVO>> productInfoFuture, SessionStartVO sessionStartVO) {
        try {
            // 处理商品信息
            List<ProductInfoVO> productInfoVOList = productInfoFuture.get(globalApolloConfig.getThreadTimeout(), TimeUnit.MILLISECONDS);
            if (CollectionUtils.isNotEmpty(productInfoVOList) && productInfoVOList.size() == 1) {
                sessionStartVO.setProductInfoVO(productInfoVOList.get(0));
                sessionStartVO.getProductInfoVO().setStockOutReservedNew(true);
            }
        } catch (InterruptedException ie) {
            log.error("setProductInfoFuture error", ie);
            MonitorUtil.counterOnce("processStartProductSync", "fail");
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("setProductInfoFuture error", e);
            MonitorUtil.counterOnce("processStartProductSync", "fail");
        }
    }

    private void setStartMessageFuture(CompletableFuture<Triple<String, List<String>, List<String>>> chatStartMessageFuture,
                                       SessionStartVO sessionStartVO, Session newSession) {
        try {
            // 处理聊天开始消息结果
            Triple<String, List<String>, List<String>> chatStartMessage = chatStartMessageFuture.get(globalApolloConfig.getThreadTimeout(), TimeUnit.MILLISECONDS);
            if (chatStartMessage == null) {
                sessionStartVO.setWelcome(constructWelcomeMessage(newSession.getSessionId(), globalApolloConfig.getWelcome()));
            } else {
                // welcome message
                if (StringUtils.isNotBlank(chatStartMessage.getLeft())) {
                    sessionStartVO.setWelcome(constructWelcomeMessage(newSession.getSessionId(), chatStartMessage.getLeft()));
                } else {
                    sessionStartVO.setWelcome(constructWelcomeMessage(newSession.getSessionId(), globalApolloConfig.getWelcome()));
                }
                // intent guide words
                if (CollectionUtils.isNotEmpty(chatStartMessage.getMiddle())) {
                    sessionStartVO.setIntentGuideWords(chatStartMessage.getMiddle());
                }
                // guess questions
                if (CollectionUtils.isNotEmpty(chatStartMessage.getRight())) {
                    sessionStartVO.setQuickQuestionList(chatStartMessage.getRight());
                }
            }
        } catch (InterruptedException ie) {
            log.error("setStartMessageFuture error", ie);
            MonitorUtil.counterOnce("processStartMessageSync", "fail");
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("setStartMessageFuture error", e);
            MonitorUtil.counterOnce("processStartMessageSync", "fail");
            sessionStartVO.setWelcome(constructWelcomeMessage(newSession.getSessionId(), globalApolloConfig.getWelcome()));
        }
    }

    private void setHistoryFuture(CompletableFuture<MessageBoxVO> historyFuture, SessionStartVO sessionStartVO) {
        try {
            MessageBoxVO history = historyFuture.get(globalApolloConfig.getThreadTimeout(), TimeUnit.MILLISECONDS);
            if (history != null && CollectionUtils.isNotEmpty(history.getMessageList())) {
                sessionStartVO.setHasHistory(true);
            }
        } catch (InterruptedException ie) {
            log.error("setHistoryFuture error", ie);
            MonitorUtil.counterOnce("processStartHistorySync", "fail");
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("setHistoryFuture error", e);
            MonitorUtil.counterOnce("processStartHistorySync", "fail");
        }
    }

    private MessageVO constructWelcomeMessage(String sessionId, String content) {
        // 因为这条欢迎消息不落库，历史消息也不需要看到，所以字段可以不用设置完备
        MessageVO messageVO = new MessageVO();
        messageVO.setMessageId(MsgIdGenerator.genId());
        messageVO.setType(MSG_TYPE_TEXT);
        messageVO.setRole(MSG_SENDER_ASSISTANT);
        messageVO.setContent(content);
        messageVO.setSessionId(sessionId);
        messageVO.setTime(DateUtils.getCurTimestamp());
        return messageVO;
    }


    /**
     * 发送、重新发送问题
     */
    public SseEmitter completions(ChatQuestionReq chatQuestionReq, SseEmitter sseEmitter) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("completions.req:{}", JsonUtil.toJson(chatQuestionReq));
        }

        MessageContext messageContext = new MessageContext();//消息上下文类
        messageContext.setUid(chatQuestionReq.getUid());
        messageContext.setTopicId(chatQuestionReq.getTopicId());
        //检查sessionId,过期要生成新的session放入messageContext
        messageBoxHandler.checkSession(chatQuestionReq.getSessionId(), messageContext);

        // 分段消息处理
        processSegmentMessage(chatQuestionReq, messageContext, sseEmitter);

        // 图片消息处理
        List<String> checkImgReasonList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(chatQuestionReq.getImageUrls())) {
            // 图片过风控
            ImgCheckDTO imgCheckDTO = antispamProxy.buildImgCheckDTO(chatQuestionReq);
            checkImgReasonList = antispamProxy.imageCheck(imgCheckDTO);
            // 图片消息处理
            processImageMessage(chatQuestionReq, messageContext, sseEmitter, checkImgReasonList);
        }

        List<TextCheckReason> checkTextReasonList = new ArrayList<>();
        // 文字消息处理，生成用户消息，生成questId放入messageContext，并对用户消息和谐处理
        if (StringUtils.isNotBlank(chatQuestionReq.getQuestion())) {
            // 风控检查，用户输入若命中，则将用户输入打码后返回；不命中则原样返回
            checkTextReasonList = checkPass(chatQuestionReq);
            // 文本消息处理
            processTextMessage(chatQuestionReq, messageContext, sseEmitter, checkTextReasonList);
        }

        // 图片和风控任意一个被风控，直接返回系统提示信息(图片为默认兜底图)，并结束本轮次回合；否则，转发给算法，通过回调流式返回消息
        if (CollectionUtils.isNotEmpty(checkImgReasonList) || CollectionUtils.isNotEmpty(checkTextReasonList)) {
            MonitorUtil.counterOnce("saveUserMessage", "sensitive_word", "0", chatQuestionReq.getQuestion() + ",checkReasons:" + checkTextReasonList);

            // 额外再发送一次风控提示的系统消息
            MessageVO messageVO = messageBoxHandler.getSensitiveMessageVO(messageContext);
            if (!messageBoxHandler.saveDirectMessage(messageVO, chatQuestionReq)) {
                MonitorUtil.counterOnce("saveUserMessage", "system_risk_prompt_fail", "-1", JsonUtil.toJson(messageVO));
            }

            sseEmitter.send(SimpleResponseVo.ok(messageVO));
            // 提前结束流
            sseEmitter.complete();
            return sseEmitter;
        }

        // 叮小咚新形态或者有图片，都需要调用新版接口
        if (messageBoxHandler.isProcessMultimodalParam(chatQuestionReq)) {
            log.info("completions invoke nlpAiProxy.sendRequestV2, uid:{}, questId:{}",
                messageContext.getUid(), messageContext.getQuestId());
            return nlpAiProxy.sendRequestV2(chatQuestionReq, sseEmitter, messageContext);
        }
        ChatContext chatContext = ChatContext.of(chatQuestionReq);
        String testGroup = abTestProxy.abTest(chatContext, globalApolloConfig.getAiChatInvokeAbtestExpId());
        if (StringUtils.isNotBlank(testGroup) && "exp".equals(testGroup)) {
            log.info("completions invoke nlpAiProxy.sendRequestV2, uid:{}, questId:{}",
                messageContext.getUid(), messageContext.getQuestId());
            // 命中实验，调用算法新接口
            return nlpAiProxy.sendRequestV2(chatQuestionReq, sseEmitter, messageContext);
        }
        // 未命中，调用算法老接口
        return nlpAiProxy.sendRequest(chatQuestionReq, sseEmitter, messageContext);
    }

    /**
     * 处理图片消息
     *
     * @param chatQuestionReq    req
     * @param messageContext     message context
     * @param sseEmitter         sse emitter
     * @param checkImgReasonList img check reason list
     * @throws IOException
     */
    public void processImageMessage(ChatQuestionReq chatQuestionReq,
                                    MessageContext messageContext, SseEmitter sseEmitter, List<String> checkImgReasonList) throws IOException {

        if (CollectionUtils.isEmpty(chatQuestionReq.getImageUrls())) {
            return;
        }

        // 构建图片消息
        MessageVO userMessageVO = messageBoxHandler.getUserImageMessageVO(checkImgReasonList,
                chatQuestionReq.getImageUrls(), messageContext);
        // 保存图片消息
        boolean saveResult = messageBoxHandler.saveDirectMessage(userMessageVO, chatQuestionReq);
        if (saveResult) {
            MonitorUtil.counterOnce("saveUserImageMessage", "success");
        } else {
            MonitorUtil.counterOnce("saveUserImageMessage", "fail", "-1", JsonUtil.toJson(userMessageVO));
        }
        sseEmitter.send(SimpleResponseVo.ok(userMessageVO));
    }


    /**
     * 处理图片消息
     *
     * @param chatQuestionReq
     * @param messageContext
     * @param sseEmitter
     * @param checkTextReasonList
     * @throws IOException
     */
    public void processTextMessage(ChatQuestionReq chatQuestionReq, MessageContext messageContext, SseEmitter sseEmitter,
                                   List<TextCheckReason> checkTextReasonList) throws IOException {

        MessageVO userMessageVO = messageBoxHandler.getUserMessageVO(checkTextReasonList, chatQuestionReq.getQuestion(),
                messageContext);
        boolean saveResult = messageBoxHandler.saveDirectMessage(userMessageVO, chatQuestionReq);
        if (saveResult) {
            MonitorUtil.counterOnce("saveUserMessage", "success");
        } else {
            MonitorUtil.counterOnce("saveUserMessage", "fail", "-1", JsonUtil.toJson(userMessageVO));
        }
        sseEmitter.send(SimpleResponseVo.ok(userMessageVO));
    }

    private void processSegmentMessage(ChatQuestionReq chatQuestionReq, MessageContext messageContext, SseEmitter sseEmitter) {
        try {
            // 当前 session 是否已有分段消息
            boolean isSessionSegmentRecord = messageBoxHandler.isSessionSegmentRecord(chatQuestionReq.getSessionId());
            if (isSessionSegmentRecord) {
                if (log.isDebugEnabled()) {
                    log.debug("processSegmentMessage exist:{}", JsonUtil.toJson(chatQuestionReq));
                }
                return;
            }
            // 当前 session 没有分段消息
            ChatContext chatContext = ChatContext.of(chatQuestionReq);
            MessageVO segmentMessageVO = messageBoxHandler.getSegmentMessageVO(messageContext, chatContext);
            // 保存分段消息
            boolean saveResult = messageBoxHandler.saveDirectMessage(segmentMessageVO, chatQuestionReq);
            messageBoxHandler.createSessionSegmentRecord(chatQuestionReq.getSessionId());
            if (log.isDebugEnabled()) {
                log.debug("completions save segment message success sessionId:{}, segmentMessageVO:{}",
                        chatQuestionReq.getSessionId(), JsonUtil.toJson(segmentMessageVO));
            }
            if (saveResult) {
                MonitorUtil.counterOnce("saveSegmentMessage", "success");
                if (BooleanUtil.isTrue(globalApolloConfig.getWriteSegmentMessageSwitch())) {
                    // 分段消息写回前端
                    sseEmitter.send(SimpleResponseVo.ok(segmentMessageVO));
                }
            } else {
                MonitorUtil.counterOnce("saveSegmentMessage", "fail", "-1", JsonUtil.toJson(segmentMessageVO));
            }
        } catch (Exception e) {
            log.error("processSegmentMessage error, req:{}, messageContext: {}",
                    JsonUtil.toJson(chatQuestionReq), JsonUtil.toJson(messageContext), e);
            MonitorUtil.counterOnce("saveSegmentMessage", "error", "-1", JsonUtil.toJson(messageContext));
        }
    }

    private List<TextCheckReason> checkPass(ChatQuestionReq chatQuestionReq) throws Exception {
        TextCheckDTO checkDTO = new TextCheckDTO();
        checkDTO.setAppId("product_assistant");
        checkDTO.setServerType(0);
        checkDTO.setDataId(System.currentTimeMillis() + "");
        checkDTO.setSceneCode("product_assistant");
        checkDTO.setContent(chatQuestionReq.getQuestion());
        checkDTO.setUser(chatQuestionReq.getUid());
        return antispamProxy.textCheck(checkDTO);
    }


    /**
     * 取消/停止回答
     * 算法根据messageId做了路由转发，保证同一个messageId的聊天和停止聊天都在一台服务器上进行
     */
    public Boolean cancelQuestion(CancelQuestionReq cancelQuestionReq) {
        return nlpAiProxy.stopStream(cancelQuestionReq.getMessageId() + "");
    }

    /**
     * 结束会话
     */
    public Boolean sessionEnd(SessionReq sessionReq) {
        if (sessionReq == null || sessionReq.getSessionId() == null) {
            throw new RuntimeException("sessionId not find");
        }
        return messageBoxHandler.removeSession(sessionReq.getSessionId());
    }


    /**
     * 历史消息
     */
    public MessageBoxVO history(ChatHistoryReq chatHistoryReq) {
        if (StringUtils.isBlank(chatHistoryReq.getUid())) {
            throw new RuntimeException("uid is blank");
        }
        if (chatHistoryReq.getLimit() == null
                || chatHistoryReq.getLimit() <= 0
                || chatHistoryReq.getLimit() > globalApolloConfig.getHistoryMessagePageSize()) {
            chatHistoryReq.setLimit(globalApolloConfig.getHistoryMessagePageSize());
        }
        if (chatHistoryReq.getLastMessageId() == null) {
            chatHistoryReq.setLastMessageId(Long.MAX_VALUE);
        }
        return messageBoxHandler.history(chatHistoryReq);
    }


    @Override
    public Integer syncUnCommitMessage() {
        return messageBoxHandler.syncUnCommitMessage();
    }


    @Override
    public Boolean toggleSettings(String uid, Integer autoAudioPlay) {
        // do the save only when the value is valid
        if (Objects.equals(autoAudioPlay, SETTING_VALUE_ON) || Objects.equals(autoAudioPlay, SETTING_VALUE_OFF)) {
            return userSettingsDAO.saveOrUpdateUserSettings(uid, Map.of(SETTING_KEY_AUDIO_AUTOPLAY, autoAudioPlay));
        }

        return null;
    }


    @Override
    public Map<String, Integer> querySettings(String uid) {
        if (StringUtils.isBlank(uid)) {
            log.error("uid is empty");
            return Collections.emptyMap();
        }

        UserSettingsDO userSettingsDO = userSettingsDAO.selectUserSettingsByOwner(uid);

        if (userSettingsDO == null) {
            return Collections.emptyMap();
        }

        return Collections.singletonMap(SETTING_KEY_AUDIO_AUTOPLAY, userSettingsDO.getAudioAutoPlay());
    }


    @Override
    public String uploadFile(MultipartFile file, String messageId) {
        return ddfsProxy.uploadUserAudioFile(file, messageId);
    }


    @Override
    public Boolean record(String uid, Long messageId, String fileKey) {
        return messageBoxHandler.updateMessageAudioFileKey(uid, messageId, fileKey);
    }

    @Override
    public Pair<String, Long> getToken(boolean forceRefresh) {
        if (forceRefresh) {
            return invokeAliyunToGenerateNewToken();
        }

        // first try to get from redis
        String existToken = transformersRedisClient.getStr(getAliyunTokenCacheKey());
        if (existToken != null) {
            // token value is  tokenId,expireTime
            String[] tokenPair = existToken.split(",");
            if (tokenPair.length == 2) {
                String token = tokenPair[0];
                // the unit of expireTime is second
                long expireTime = Long.parseLong(tokenPair[1]);
                // check expireTime, if current time is less than 3 hours away from the persisted expireTime, then the token is considered expired
                // need to retrieve a new token. Otherwise, return the existing token.
                if (System.currentTimeMillis() <= expireTime * 1000 - 3 * 60 * 60 * 1000) {
                    return Pair.of(token, expireTime);
                }
            }
        }

        // if reach here, means the token is not found, or the token has expired
        return invokeAliyunToGenerateNewToken();
    }

    private @Nullable Pair<String, Long> invokeAliyunToGenerateNewToken() {
        // if not found, or the token has expired, then need to acquire a new token
        // by default, the new token has 36 hours duration
        Pair<String, Long> newToken = aliyunProxy.generateToken(globalApolloConfig.getAliyunAppAccessKeyId(),
                globalApolloConfig.getAliyunAppAccessKeySecret(),
                globalApolloConfig.getAliyunAppTokenDomain());

        if (newToken == null) {
            MonitorUtil.counterOnce("getToken_fail", "generate new token fail", "-1", "generate new token fail");
            log.error("generate new token fail");

            return null;
        }

        // save the new token to redis, and set the key's expire time as 5 minute ahead of token's expire time
        long durationSecond = newToken.getRight() - 300 - System.currentTimeMillis() / 1000;
        transformersRedisClient.setStr(getAliyunTokenCacheKey(), newToken.getLeft() + "," + newToken.getRight(), Duration.ofSeconds(durationSecond));
        return newToken;
    }

}
