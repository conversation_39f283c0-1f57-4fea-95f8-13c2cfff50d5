package com.ddmc.chat.common.service;

import com.ddmc.chat.common.domain.dto.req.chat.CancelQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatHistoryReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.EntryReq;
import com.ddmc.chat.common.domain.dto.req.chat.SessionReq;
import com.ddmc.chat.common.domain.dto.req.chat.StartReq;
import com.ddmc.chat.common.domain.vo.ChatEntryVO;
import com.ddmc.chat.common.domain.vo.MessageBoxVO;
import com.ddmc.chat.common.domain.vo.SessionStartVO;
import java.util.Map;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * @<PERSON> liwan<PERSON><PERSON>
 * @Date 2024/9/4 11:14 AM
 * @Description:
 */
public interface ChatService {

    /**
     * 是否能开启会话
     */
    ChatEntryVO entry(EntryReq entryReq);

    /**
     * 会话开始
     */
    SessionStartVO start(StartReq startReq);

    /**
     * 发送、重新发送问题
     */
    SseEmitter completions(ChatQuestionReq chatQuestionReq, SseEmitter sseEmitter) throws Exception;


    /**
     * 取消回答
     */
    Boolean cancelQuestion(CancelQuestionReq cancelQuestionReq);

    /**
     * 结束会话
     */
    Boolean sessionEnd(SessionReq sessionReq);


    /**
     * 历史消息
     */
    MessageBoxVO history(ChatHistoryReq sessionReq);

    Integer syncUnCommitMessage();

    /**
     * 切换 自动播放语音设置
     * @param uid
     * @param autoAudioPlay
     * @return
     */
    Boolean toggleSettings(String uid, Integer autoAudioPlay);


    /**
     * 获取 用户的个人设置，具体的设置项作为map的key，对应的设置值是value。值为1表示开启，0表示关闭
     * @return
     */
    Map<String, Integer> querySettings(String uid);


    /**
     * 上传文件，返回文件的key
     * @param file
     * @param messageId
     * @return
     */
    String uploadFile(MultipartFile file, String messageId);


    /**
     * 将上传的文件key，与对应的消息关联起来
     * @param uid
     * @param messageId
     * @param fileKey
     * @return
     */
    Boolean record(String uid, Long messageId, String fileKey);


    /**
     * for aliyun, 获取token及过期时间
     */
    Pair<String, Long> getToken(boolean forceRefresh);
}
