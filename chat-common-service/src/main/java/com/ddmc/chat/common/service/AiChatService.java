package com.ddmc.chat.common.service;

import com.ddmc.chat.common.domain.dto.req.ai.AiChatStopReq;
import com.ddmc.chat.common.domain.dto.req.ai.AiChatCompletionReq;
import com.ddmc.chat.common.domain.dto.req.ai.QueryConfigReq;
import com.ddmc.chat.common.domain.dto.req.ai.UpdateConfigReq;
import com.ddmc.chat.common.domain.dto.res.QueryConfigResponse;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


/**
 * <AUTHOR>
 */
public interface AiChatService {

    /**
     * 发送、重新发送问题
     */
    SseEmitter completions(String from, AiChatCompletionReq chatQuestionReq, SseEmitter sseEmitter) throws Exception;

    /**
     * 取消回答
     */
    Boolean stop(AiChatStopReq cancelQuestionReq);

    /**
     * 更新配置信息
     *
     * @param updateConfigReq req
     * @return
     */
    Boolean updateConfig(UpdateConfigReq updateConfigReq);

    /**
     * 查询配置信息
     *
     * @param queryConfigReq req
     * @return
     */
    QueryConfigResponse queryConfig(QueryConfigReq queryConfigReq);
}
