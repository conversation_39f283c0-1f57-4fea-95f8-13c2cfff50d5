package com.ddmc.chat.common.infra.proxy;

import static com.ddmc.chat.common.domain.Constants.LOVE_QUALITY_TAB_NAME;
import static com.ddmc.chat.common.domain.Constants.TRAIT_KEY;
import static com.ddmc.chat.common.domain.Constants.TRAIT_TYPE;

import com.ddmc.chat.common.constant.Constans;
import com.ddmc.chat.common.domain.dto.req.ai.AiChatCompletionReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.StartReq;
import com.ddmc.chat.common.domain.dto.res.sse.AiData;
import com.ddmc.chat.common.domain.dto.res.sse.AiMessage;
import com.ddmc.chat.common.domain.dto.res.sse.AppendContents;
import com.ddmc.chat.common.domain.dto.res.sse.Choice;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatAppendContents;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatChoice;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatData;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatDelta;
import com.ddmc.chat.common.domain.entity.MessageContext;
import com.ddmc.chat.common.domain.vo.AiChatMessageVO;
import com.ddmc.chat.common.domain.vo.DailyTipsVO;
import com.ddmc.chat.common.domain.vo.MessageVO;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.handler.LinkHandler;
import com.ddmc.chat.common.infra.handler.MessageBoxHandler;
import com.ddmc.chat.common.infra.handler.TopicHandler;
import com.ddmc.chat.common.infra.proxy.sse.ProductAssistResultCallbackAdapter;
import com.ddmc.chat.common.util.MDCUtil;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.guide.enhance.json.JsonUtil;
import com.ddmc.guide.enhance.rest.SimpleResponseVo;
import com.ddmc.llm.biz.agent.client.LLMBizAgentClient;
import com.ddmc.llm.biz.agent.client.ProductAssistSseClient;
import com.ddmc.llm.biz.agent.entity.ProductAssistDietDailyKnowledgeRequestVo;
import com.ddmc.llm.biz.agent.entity.ProductAssistDietDeleteHistoryRequestVo;
import com.ddmc.llm.biz.agent.entity.ProductAssistDietResponseVo;
import com.ddmc.llm.biz.agent.entity.ProductAssistPossibleQRequestVo;
import com.ddmc.llm.biz.agent.entity.ProductAssistPossibleQResponseVo;
import com.ddmc.llm.biz.agent.entity.ProductAssistRequestVo;
import com.ddmc.llm.biz.agent.entity.ProductAssistResponseVo;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * <AUTHOR>
 * @Date 2024/9/6 2:26 PM
 * @Description:
 */
@Service
@Slf4j
public class NlpAiProxy {

    @Autowired
    private ProductAssistSseClient sseClient;

    @Autowired
    private LLMBizAgentClient nlpApiClient;

    @Autowired
    private MessageBoxHandler messageBoxHandler;

    @Autowired
    private GlobalApolloConfig globalApolloConfig;

    @Autowired
    private LinkHandler linkHandler;

    @Autowired
    private TopicHandler topicHandler;

    private static final String METRICS_SSE_EMITTER_V2 = "sseEmitterV2";

    /**
     * ① 叮小咚会话，算法新接口，支持多模态，背后模型也有升级。
     *
     * @param chatQuestionReq
     * @param sseEmitter
     * @param messageContext
     * @return
     */
    public SseEmitter sendRequestV2(ChatQuestionReq chatQuestionReq, SseEmitter sseEmitter, MessageContext messageContext) {
        Map<String, String> copyOfContextMap = MDCUtil.getCopyOfContextMap();
        if (Objects.isNull(chatQuestionReq) || Objects.isNull(sseEmitter)) {
            return null;
        }
        sseEmitter.onTimeout(() -> MonitorUtil.counterOnce(METRICS_SSE_EMITTER_V2, "timeout"));
        ProductAssistRequestVo productAssistRequestVo = messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, true);

        log.info("sendRequestV2 request. Data: {}", JsonUtil.toJson(productAssistRequestVo));

        // 算法对接文档：https://cfl.corp.100.me/pages/viewpage.action?pageId=206932365
        sseClient.streamCallV2(productAssistRequestVo, new ProductAssistResultCallbackAdapter(
                "sendRequestV2", copyOfContextMap, sseEmitter, messageContext) {
                @Override
                public void onOpenInternal(EventSource eventSource, Response response) {
                    messageBoxHandler.saveUnCommitMessage(chatQuestionReq.getUid(), chatQuestionReq.getStationId(),
                        chatQuestionReq.getCityNumber(), chatQuestionReq.getProductId(), messageContext);
                }

                @Override
                public void onEventInternal(EventSource eventSource, String id, String type, String data)
                    throws Exception {
                    try {
                        AiChatData aiData = JsonUtil.toObject(data, AiChatData.class);
                        if (aiData == null) {
                            MonitorUtil.counterOnce(METRICS_SSE_EMITTER_V2, "null_data");
                            return;
                        }

                        List<AiChatAppendContents> appendContents = aiData.getAppendContents();
                        if (CollectionUtils.isNotEmpty(appendContents)) {

                            for (int i = 0; i < appendContents.size(); i++) {
                                AiChatAppendContents appendContent = appendContents.get(i);
                                String appendContentType = appendContent.getType();
                                MessageVO messageVO = null;
                                switch (appendContentType) {
                                    case "question":
                                        messageVO = messageBoxHandler.buildAiQuestionMessageVO(false, appendContent, messageContext);
                                        break;
                                    case "url":
                                        // 附加内容是URL，需要特殊处理
                                        messageVO = messageBoxHandler.buildAiUrlMessageVO(false, appendContent, messageContext);
                                        processAppendContentUrl(appendContent.getPurpose(), messageVO);
                                        break;
                                    case "product":
                                        // 附加内容是商卡，需要特殊处理
                                        messageVO = messageBoxHandler.buildAiProductCardMessageVO(false, appendContent, messageContext, chatQuestionReq);
                                        break;
                                    case "text":
                                        messageVO = messageBoxHandler.getAiTextMessageVO(false, appendContent.getContent(), messageContext);
                                        break;
                                    default:
                                        MonitorUtil.counterOnce(METRICS_SSE_EMITTER_V2, "unknown_type", "-1",
                                            appendContentType);
                                        // 不认识的类型，默认用兜底的文本消息
                                        messageVO = messageBoxHandler.getAiTextMessageVO(false, appendContent.getContent(), messageContext);
                                }
                                if (messageVO == null) {
                                    MonitorUtil.counterOnce(METRICS_SSE_EMITTER_V2, "null_message_vo");
                                    continue;
                                }
                                // 仅针对商卡消息，将商卡信息，设置到redis中，保存 extra 字段的单独key上
                                messageBoxHandler.appendMessageExtra(messageVO);
                                // 统一发送 append content 消息
                                sseEmitter.send(SimpleResponseVo.ok(messageVO));
                            }
                        } else if (CollectionUtils.isNotEmpty(aiData.getChoices())) {

                            List<AiChatChoice> choiceList = aiData.getChoices();
                            if (CollectionUtils.isEmpty(choiceList)) {
                                MonitorUtil.counterOnce(METRICS_SSE_EMITTER_V2, "null_choices");
                                return;
                            }

                            for (AiChatChoice choice : choiceList) {
                                AiChatDelta delta = choice.getDelta();
                                if (delta == null) {
                                    MonitorUtil.counterOnce(METRICS_SSE_EMITTER_V2, "null_delta");
                                    continue;
                                }
                                // 回答追加到redis里
                                messageBoxHandler.appendMessageContent(messageContext.getMessageId(), delta.getContent());
                                // 将消息转换格式并发送前端
                                MessageVO messageVO = messageBoxHandler.getAiTextMessageVO(false, delta.getContent(), messageContext);

                                sseEmitter.send(SimpleResponseVo.ok(messageVO));
                            }
                        }
                    } catch (Exception e) {
                        try {
                            sseEmitter.send(SimpleResponseVo.fail(e.getMessage()));
                        } catch (Exception ex) {
                            // do nothing
                            log.info("onEvent sseEmitter.send error, message: {}", ex.getMessage());
                            MonitorUtil.counterOnce(METRICS_SSE_EMITTER_V2, "onEvent_SendRequest.fail");
                        }
                        MonitorUtil.counterOnce(METRICS_SSE_EMITTER_V2, "sendRequest.fail", "-1", e);
                        log.error("Ai Chat SSE send event error", e);
                    }
                }

                @Override
                public void onClosedInternal(EventSource eventSource) {
                    // 将完整的消息持久化
                    messageBoxHandler.saveCommitMessage(messageContext);
                    try {
                        // 发送一个带结束标记的消息包给前端
                        MessageVO messageVO = messageBoxHandler.getAiTextMessageVO(true, "", messageContext);
                        sseEmitter.send(SimpleResponseVo.ok(messageVO));
                    } catch (Exception ex) {
                        // do nothing
                        log.info("onClosed sseEmitter.send error, message: {}", ex.getMessage());
                        MonitorUtil.counterOnce(METRICS_SSE_EMITTER_V2, "onClosed_SendRequest.fail");
                    }
                    try {
                        sseEmitter.complete();
                    } catch (Exception e) {
                        // do nothing
                        log.info("onClosed sseEmitter.complete error, message: {}", e.getMessage());
                        MonitorUtil.counterOnce(METRICS_SSE_EMITTER_V2, "onClosed_Complete.fail");
                    }
                }

                @Override
                public void onFailureInternal(EventSource eventSource, Throwable t, Response response) {
                    try {
                        sseEmitter.send(SimpleResponseVo.fail(JsonUtil.toJson(t.getCause())));//返回fail的都会被前端感知为异常，前端统一显示产品定的提示
                    } catch (Exception e) {
                        // do nothing
                        log.info("onFailure sseEmitter.send error, message: {}", e.getMessage());
                        MonitorUtil.counterOnce(METRICS_SSE_EMITTER_V2, "onFailure_SendRequest.fail");
                    }
                    MonitorUtil.counterOnce(METRICS_SSE_EMITTER_V2, "failure_callback", "-1", t);
                    try {
                        sseEmitter.completeWithError(t);
                    } catch (Exception e) {
                        // do nothing
                        log.info("onFailure sseEmitter.completeWithError error, message: {}", e.getMessage());
                        MonitorUtil.counterOnce(METRICS_SSE_EMITTER_V2, "onFailure_CompleteWithError.fail");
                    }
                    // 出现异常时也将完整的消息持久化
                    messageBoxHandler.saveCommitMessage(messageContext);
                }
            }
        );
        return sseEmitter;
    }


    private void processAppendContentUrl(String purpose, MessageVO messageVO) {
        // 判断用途，是否需要持久化存储
        if (globalApolloConfig.isUrlPurposePersistent(purpose)) {
            if (log.isDebugEnabled()) {
                log.debug("processAppendContentUrl appendContent need persistent, purpose: {}, messageVO: {}", purpose, messageVO);
            }
            // 回答追加到 redis 里, 最后统一写回数据库
            messageBoxHandler.appendMessageContent(messageVO.getMessageId(), messageVO.getContent());
        }
    }

    public Boolean stopStream(String messageId) {
        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        productAssistRequestVo.setMessageId(messageId);
        try {
            ProductAssistResponseVo productAssistResponseVo = sseClient.stopStream(productAssistRequestVo);
            return Objects.equals(productAssistResponseVo.getCode(), 0);
        } catch (Exception e) {
            log.error("sseClient.stopStream.exception", e);
            return false;
        }
    }


    /**
     * 叮小咚 start 接口调用。
     *
     * @param uc
     * @param startReq
     * @return
     */
    // 会话场景的开启，是指用户第一次进入会话时，调用算法，获取第一条欢迎消息，以及 意图引导词 或 猜你想问问题。后两者只会出现一个。
    // L: 欢迎消息；M: 意图引导词；R: 猜你想问问题。 M和R同时只会有一个有有效值，但可以同出现空值。
    public Triple<String, List<String>, List<String>> getFirstChatStartMessage(ChatContext uc, StartReq startReq) {
        if (isFallbackOn()) {
            log.warn("触发手动降级: NlpAiProxy.getFirstChatStartMessage");
            MonitorUtil.counterOnce("triggerDegrade", "NlpAiProxy_getFirstChatStartMessage", "-1", "nlpApiClient.getPossibleQuestion");
            return Triple.of(null, null, null);
        }

        try {
            ProductAssistPossibleQRequestVo productAssistRequestVo = new ProductAssistPossibleQRequestVo();
            // 将工程侧新生成的sessionID，传递给算法
//            productAssistRequestVo.setSessionId(sessionId);

            // 必须参数
            productAssistRequestVo.setUser(uc.getUid());
            productAssistRequestVo.setPageType(uc.getReferPage());
            productAssistRequestVo.setScene(messageBoxHandler.processScene(uc.getFrom(), uc.getScene()));
//            productAssistRequestVo.setStationId(uc.getStationId());
            productAssistRequestVo.setCityNumber(uc.getCityNumber());
            productAssistRequestVo.setNativeVersion(uc.getNativeVersion());
            productAssistRequestVo.setCityName(uc.getCityName());
            productAssistRequestVo.setTimestamp(System.currentTimeMillis());
            productAssistRequestVo.setStationId(uc.getStationId());

            // 可选参数，与算法确认，这些参数在开启会话的时候，一次性传递；算法会作为上下文保存在会话中。
            // 后续聊天过程，这些参数无需继续传递，算法会从会话上下文中获取。
            productAssistRequestVo.setProductMongoId(uc.getProductId());
            productAssistRequestVo.setFrontendCategoryName(uc.getFrontCategoryName());
            productAssistRequestVo.setFrontendCategoryId(uc.getFrontCategoryId());
            // todo, 未来搜索场景的搜索词放在这里透传
            productAssistRequestVo.setSearchQuery(uc.getSearchKeyword());
            productAssistRequestVo.setSearchResultsCount(uc.getSearchResultCnt());
            // 扩展参数处理
            if (Objects.nonNull(startReq) && MapUtils.isNotEmpty(startReq.getExtraParamMap())) {
                Map<String, String> extraParamMap = startReq.getExtraParamMap();
                // 评价列表页参数
                productAssistRequestVo.setTraitKey(extraParamMap.get(TRAIT_KEY));
                productAssistRequestVo.setTraitType(extraParamMap.get(TRAIT_TYPE));
                // 品质之爱落地页
                productAssistRequestVo.setTabPath(extraParamMap.get(LOVE_QUALITY_TAB_NAME));
            }

            if (log.isDebugEnabled()) {
                log.debug("getPossibleQuestion.req:{}", JsonUtil.toJson(productAssistRequestVo));
            }

            ProductAssistPossibleQResponseVo productAssistPossibleQResponseVo = nlpApiClient.getPossibleQuestion(productAssistRequestVo);

            if (log.isDebugEnabled()) {
                log.debug("getPossibleQuestion.res:{}", JsonUtil.toJson(productAssistPossibleQResponseVo));
            }

            if (productAssistPossibleQResponseVo != null && productAssistPossibleQResponseVo.getCode() == 0) {
                String welcomeMessage = productAssistPossibleQResponseVo.getGreeting();
                if (StringUtils.isNotBlank(productAssistPossibleQResponseVo.getUrl())
                        && StringUtils.isNotBlank(globalApolloConfig.getCustomerServiceButton())) {
                    // 拼接客服链接
                    welcomeMessage = welcomeMessage + String.format(globalApolloConfig.getCustomerServiceButton(), productAssistPossibleQResponseVo.getUrl());
                }
                List<String> guideWords = productAssistPossibleQResponseVo.getKeywords();
                List<String> questions = productAssistPossibleQResponseVo.getQuestions();

                return Triple.of(welcomeMessage, guideWords, questions);
            }

        } catch (Exception e) {
            log.error("getPossibleQuestions error", e);
        }

        return Triple.of(null, null, null);
    }

    public boolean isFallbackOn() {
        return Objects.equals(globalApolloConfig.getNlpSwitch(), Constans.ONE);
    }


    /**
     * Deepseek搜索Tab，算法老接口，返回搜索词相关的答案。
     * 后续应该全量切换到新接口{@link SearchRecProxy#aiSearch(AiChatCompletionReq, SseEmitter, MessageContext)}，老接口下线（估计3个月内）。
     *
     * @param completionReq    completion req
     * @param originSseEmitter sse emitter
     * @param messageContext   message context
     * @return sse emitter
     * @see <a href="https://cfl.corp.100.me/pages/viewpage.action?pageId=203927124">算法契约</a>
     * @see <a href="https://cfl.corp.100.me/pages/viewpage.action?pageId=203923844">后端技术文档</a>
     */
    public SseEmitter streamCall(AiChatCompletionReq completionReq, SseEmitter originSseEmitter,
                                 MessageContext messageContext) {
        Map<String, String> copyOfContextMap = MDCUtil.getCopyOfContextMap();
        if (Objects.isNull(completionReq) || Objects.isNull(originSseEmitter)) {
            return null;
        }

        originSseEmitter.onTimeout(() -> MonitorUtil.counterOnce("aiChatOriginSeeEmitter", "timeout"));

        ProductAssistRequestVo productAssistRequestVo = messageBoxHandler.buildProductAssistRequestVo(completionReq, messageContext);

        log.info("streamCall.req:{}", JsonUtil.toJson(productAssistRequestVo));

        sseClient.streamCall(productAssistRequestVo, new ProductAssistResultCallbackAdapter(
            "deepSeekV1", copyOfContextMap, originSseEmitter, messageContext) {
            @Override
            public void onOpenInternal(@NotNull EventSource eventSource, @NotNull Response response) {}

            @Override
            public void onEventInternal(@NotNull EventSource eventSource, String id, String type, @NotNull String data) {
                try {
                    AiChatData aiData = JsonUtil.toObject(data, AiChatData.class);
                    if (aiData == null) {
                        MonitorUtil.counterOnce("aiChatOriginSeeEmitter", "null_data");
                        return;
                    }

                    List<AiChatAppendContents> appendContents = aiData.getAppendContents();

                    if (CollectionUtils.isNotEmpty(appendContents)) {

                        for (int i = 0; i < appendContents.size(); i++) {
                            AiChatAppendContents appendContent = appendContents.get(i);
                            boolean isEnd = i == appendContents.size() - 1;
                            String appendContentType = appendContent.getType();
                            String content = processAiChatContent(appendContent.getContent(), completionReq.getAppClientId());

                            AiChatMessageVO messageVO = null;
                            switch (appendContentType) {
                                case "text":
                                    messageVO = messageBoxHandler.buildTextAiChatMessageVO(isEnd, content, "", appendContent.getQid(), messageContext);
                                    break;
                                case "product":
                                    // 附加内容是商卡，需要特殊处理
                                    messageVO = messageBoxHandler.buildAiProductCardMessageVO(isEnd, appendContent, messageContext, completionReq);
                                    break;
                                default:
                                    MonitorUtil.counterOnce("aiChatOriginSeeEmitter", "unknown_type", "-1",
                                            appendContentType);
                                    // 不认识的类型，默认用兜底的文本消息
                                    messageVO = messageBoxHandler.buildTextAiChatMessageVO(isEnd, content, "", appendContent.getQid(), messageContext);
                            }
                            if (messageVO == null) {
                                MonitorUtil.counterOnce("aiChatOriginSeeEmitter", "null_message_vo");
                                continue;
                            }
                            messageVO.setAiPurpose(appendContent.getPurpose());
                            originSseEmitter.send(SimpleResponseVo.ok(messageVO));

                        }
                    } else if (CollectionUtils.isNotEmpty(aiData.getChoices())) {

                        List<AiChatChoice> choiceList = aiData.getChoices();
                        if (CollectionUtils.isEmpty(choiceList)) {
                            MonitorUtil.counterOnce("aiChatOriginSeeEmitter", "null_choices");
                            return;
                        }

                        for (AiChatChoice choice : choiceList) {
                            String finishReason = choice.getFinishReason();
                            AiChatDelta delta = choice.getDelta();
                            if (delta == null) {
                                MonitorUtil.counterOnce("aiChatOriginSeeEmitter", "null_delta");
                                continue;
                            }
                            String content = processAiChatContent(delta.getContent(), completionReq.getAppClientId());
                            // 将消息转换格式并发送前端
                            boolean end = Objects.nonNull(finishReason) && !Objects.equals(finishReason, "null");
                            AiChatMessageVO messageVO = messageBoxHandler.buildTextAiChatMessageVO(
                                    end, content, delta.getReasoningContent(), aiData.getQid(), messageContext);

                            originSseEmitter.send(SimpleResponseVo.ok(messageVO));
                        }
                    }
                } catch (IOException e) {
                    try {
                        originSseEmitter.send(SimpleResponseVo.fail(e.getMessage()));
                    } catch (IOException ex) {
                        // do nothing
                    }
                    MonitorUtil.counterOnce("aiChatOriginSeeEmitter", "sendRequest.fail", "-1", e);
                    log.error("Ai Chat SSE send event error", e);
                }
            }

            /*
             * 该方法会在回答结束和 stop 时调用，后者stop需要注意，会在已经发送到缓冲区的消息全部通用onEvent回调后再关闭sse连接
             */
            @Override
            public void onClosedInternal(@NotNull EventSource eventSource) {
                try {
                    // 发送一个带结束标记的消息包给前端
                    MessageVO messageVO = messageBoxHandler.getAiTextMessageVO(true, "", messageContext);
                    originSseEmitter.send(SimpleResponseVo.ok(messageVO));
                } catch (Exception ex) {
                    // do nothing
                    log.info("streamCall onClosed sseEmitter.send error, message: {}", ex.getMessage());
                    MonitorUtil.counterOnce("aiChatOriginSeeEmitter", "onClosed_SendRequest.fail");
                }
                try {
                    originSseEmitter.complete();
                } catch (Exception e) {
                    log.error("Error closing SSE connection", e);
                }
            }

            @Override
            public void onFailureInternal(@NotNull EventSource eventSource, Throwable t, Response response) {
                try {
                    originSseEmitter.send(SimpleResponseVo.fail(JsonUtil.toJson(t.getCause())));
                } catch (Exception e) {
                    // do nothing
                }
                originSseEmitter.completeWithError(t);
            }
        });
        return originSseEmitter;
    }

    /**
     * for deepseek，根据 content 不同标识拼接跳转链接
     *
     * @param content     原 content 内容
     * @param appClientId app client id
     * @return 拼接跳转链接 + 样式后的 content 内容
     */
    public String processAiChatContent(String content, String appClientId) {
        if (StringUtils.isBlank(content) || StringUtils.isBlank(appClientId)) {
            return content;
        }
        return linkHandler.convert(content, appClientId);
    }

    public DailyTipsVO queryDailyTips(ChatContext chatContext) {
        ProductAssistDietDailyKnowledgeRequestVo requestVo = new ProductAssistDietDailyKnowledgeRequestVo();
        try {
            requestVo.setQid(UUID.randomUUID().toString());
            requestVo.setTimestamp(System.currentTimeMillis());
            requestVo.setUser(chatContext.getUid());
            requestVo.setStationId(chatContext.getStationId());
            requestVo.setCityName(chatContext.getCityName());
            requestVo.setCityNumber(chatContext.getCityNumber());
            requestVo.setTopP(Float.NaN);
            requestVo.setTemperature(Float.NaN);
            ProductAssistDietResponseVo dailyKnowledgeResponse = nlpApiClient.getDailyKnowledge(requestVo);
            if (Objects.isNull(dailyKnowledgeResponse)
                    || dailyKnowledgeResponse.getCode() != 0
                    || Objects.isNull(dailyKnowledgeResponse.getDailyKnowledge())) {
                return null;
            }
            return DailyTipsVO.builder()
                    .content(dailyKnowledgeResponse.getDailyKnowledge().getContent())
                    .date(dailyKnowledgeResponse.getDailyKnowledge().getDate())
                    .lunarDate(dailyKnowledgeResponse.getDailyKnowledge().getLunarDate())
                    .build();
        } catch (Exception e) {
            log.error("nlpProxy queryDailyTips error, uid: {}, stationId: {}", chatContext.getUid(), chatContext.getStationId(), e);
            return null;
        }
    }


    //------------------------------------------------------------------------
    // 以下方法即将移除
    //------------------------------------------------------------------------

    /**
     * 叮小咚会话，算法老接口，仅支持问题，叮小咚第一期上线时用的大模型接口。
     * 后续应该全量切换到新接口，老接口下线（估计3个月内）。
     *
     * @param chatQuestionReq
     * @param originSseEmitter
     * @param messageContext
     * @return
     */
    @Deprecated
    public SseEmitter sendRequest(ChatQuestionReq chatQuestionReq, SseEmitter originSseEmitter,
                                  MessageContext messageContext) {
        Map<String, String> copyOfContextMap = MDCUtil.getCopyOfContextMap();
        if (Objects.isNull(chatQuestionReq) || Objects.isNull(originSseEmitter)) {
            return null;
        }

        originSseEmitter.onTimeout(() -> MonitorUtil.counterOnce("originSeeEmitter", "timeout"));

        ProductAssistRequestVo productAssistRequestVo = messageBoxHandler.getProductAssistRequestVo(chatQuestionReq, messageContext, false);

        log.info("sendRequest.req:{}", JsonUtil.toJson(productAssistRequestVo));

        sseClient.streamCall(productAssistRequestVo, new ProductAssistResultCallbackAdapter(
            "sendRequestV1", copyOfContextMap, originSseEmitter, messageContext) {
            @Override
            public void onOpenInternal(@NotNull EventSource eventSource, @NotNull Response response) {
                messageBoxHandler.saveUnCommitMessage(chatQuestionReq.getUid(), chatQuestionReq.getStationId(),
                        chatQuestionReq.getCityNumber(), chatQuestionReq.getProductId(), messageContext);
            }

            @Override
            public void onEventInternal(@NotNull EventSource eventSource, String id, String type, @NotNull String data) {
                try {
                    AiData aiData = JsonUtil.toObject(data, AiData.class);
                    if (aiData != null) {
                        // 与算法约定，当appendContents不为空时，一定是最后一条信息。之前的具体回答文本，已经通过SSE的方式全部返回。
                        // 再根据具体 AppendContents 的type类型，转成不同的消息类型并进一步处理。
                        List<AppendContents> appendContents = aiData.getAppendContents();
                        if (CollectionUtils.isNotEmpty(appendContents)) {
                            // 虽然 appendContents 是List，但实际中只会有一条
                            AppendContents appendContent = aiData.getAppendContents().get(0);

                            // 算法契约 https://cfl.corp.100.me/pages/viewpage.action?pageId=191871279
                            // 对应的type只有
                            //      text：文本
                            //      url：链接
                            //      product：商品
                            String appendContentType = appendContent.getType();

                            MessageVO messageVO = null;
                            switch (appendContentType) {
                                case "url":
                                    // 附加内容是URL，需要特殊处理
                                    messageVO = messageBoxHandler.getAiUrlMessageVO(true, appendContent, messageContext);
                                    processAppendContentUrl(appendContent, messageVO);
                                    break;
                                case "product":
                                    // 附加内容是商卡，需要特殊处理
                                    messageVO = messageBoxHandler.getAiProductCardMessageVO(true, appendContent, messageContext, chatQuestionReq);
                                    break;
                                case "text":
                                    messageVO = messageBoxHandler.getAiTextMessageVO(true, appendContent.getContent(), messageContext);
                                    break;
                                default:
                                    MonitorUtil.counterOnce("originSeeEmitter", "unknown_type", "-1", "unknown type");
                                    // 不认识的类型，默认用兜底的文本消息
                                    messageVO = messageBoxHandler.getAiTextMessageVO(true, appendContent.getContent(), messageContext);
                            }

                            // 仅针对商卡消息，将商卡信息，设置到redis中，保存 extra 字段的单独key上
                            messageBoxHandler.appendMessageExtra(messageVO);

                            if (log.isDebugEnabled()) {
                                log.debug("sseEmitter.last.type:{}, last.send:{}", appendContentType, JsonUtil.toJson(messageVO));
                            }

                            // 统一发送最后一条消息
                            originSseEmitter.send(SimpleResponseVo.ok(messageVO));

                        } else if (aiData.getOutput() != null) {
                            // 回答过程中的消息，每次的消息文字，在 output 字段里
                            List<Choice> choices = aiData.getOutput().getChoices();
                            if (!CollectionUtils.isEmpty(choices)) {
                                String finishReason = choices.get(0).getFinishReason();
                                AiMessage message = choices.get(0).getMessage();
                                if (message != null) {
                                    // 回答追加到redis里
                                    messageBoxHandler.appendMessageContent(messageContext.getMessageId(), message.getContent());

                                    boolean isEnd = Objects.nonNull(finishReason) && !Objects.equals(finishReason, "null");
                                    // 再将消息转换格式并发送前端
                                    MessageVO messageVO = messageBoxHandler.getAiTextMessageVO(isEnd, message.getContent(), messageContext);

                                    if (log.isDebugEnabled()) {
                                        log.debug("sseEmitter.continue.send:{}", JsonUtil.toJson(messageVO));
                                    }

                                    originSseEmitter.send(SimpleResponseVo.ok(messageVO));
                                }
                            } else {
                                MonitorUtil.counterOnce("originSeeEmitter", "null_choices");
                            }
                        }
                    } else {
                        MonitorUtil.counterOnce("originSeeEmitter", "null_data");
                    }
                } catch (Exception e) {
                    try {
                        originSseEmitter.send(SimpleResponseVo.fail(e.getMessage()));
                    } catch (Exception ex) {
                        // do nothing
                    }
                    MonitorUtil.counterOnce("originSeeEmitter", "sendRequest.fail", "-1", e);
                    log.error("send event error", e);
                }
            }


            /*
             * 该方法会在回答结束和stop时调用，后者stop需要注意，会在已经发送到缓冲区的消息全部通用onEvent回调后再关闭sse连接
             */
            @Override
            public void onClosedInternal(@NotNull EventSource eventSource) {
                //将完整的消息持久化
                messageBoxHandler.saveCommitMessage(messageContext);
                originSseEmitter.complete();
            }

            @Override
            public void onFailureInternal(@NotNull EventSource eventSource, Throwable t, Response response) {
                try {
                    originSseEmitter.send(SimpleResponseVo.fail(JsonUtil.toJson(t.getCause())));//返回fail的都会被前端感知为异常，前端统一显示产品定的提示
                } catch (Exception e) {
                    // do nothing
                }
                originSseEmitter.completeWithError(t);
                //出现异常时也将完整的消息持久化
                messageBoxHandler.saveCommitMessage(messageContext);

            }

        });

        return originSseEmitter;
    }

    /**
     * 处理特殊类型的 url 回复
     *
     * @param appendContent 后置追加内容
     * @param messageVO     消息内容
     */
    @Deprecated
    private void processAppendContentUrl(AppendContents appendContent, MessageVO messageVO) {
        // 判断用途，是否需要持久化存储
        if (globalApolloConfig.isUrlPurposePersistent(appendContent.getPurpose())) {
            if (log.isDebugEnabled()) {
                log.debug("processAppendContentUrl appendContent need persistent, appendContent: {}, messageVO: {}", appendContent, messageVO);
            }
            // 回答追加到 redis 里, 最后统一写回数据库
            messageBoxHandler.appendMessageContent(messageVO.getMessageId(), messageVO.getContent());
        }
    }

    /**
     * 删除消息
     * <a href="https://cfl.corp.100.me/pages/viewpage.action?pageId=218644960">算法删除聊天记录接口文档</a>
     * deleteAll: 0-否（默认），1-是
     *
     * @param userId        用户 id
     * @param deleteAll     是否清空聊天记录
     * @param questIdList 要删除的消息 id 列表
     */
    public boolean deleteMessage(String userId, Boolean deleteAll, List<String> questIdList) {
        ProductAssistDietDeleteHistoryRequestVo reqVo = new ProductAssistDietDeleteHistoryRequestVo();
        ProductAssistDietResponseVo resVo = null;
        try {
            reqVo.setUser(userId);
            reqVo.setDeleteAll(Boolean.TRUE.equals(deleteAll) ? 1 : 0);
            reqVo.setDeletedQids(questIdList);
            reqVo.setQid(MDCUtil.getTraceId());
            reqVo.setTopP(Float.NaN);
            reqVo.setTemperature(Float.NaN);
            resVo = nlpApiClient.deleteHistoryMessage(reqVo);
            log.info("nlpApiClient.deleteHistoryMessage req: {}, res: {}",
                JsonUtil.toJson(reqVo), JsonUtil.toJson(resVo));
            if (Objects.isNull(resVo)
                || resVo.getCode() != 0) {
                log.warn("nlpApiClient.deleteHistoryMessage error, req:{}, res:{}",
                    JsonUtil.toJson(reqVo), JsonUtil.toJson(resVo));
                return false;
            }
        } catch (Exception e) {
            log.error("nlpApiClient.deleteHistoryMessage error, req:{}, res:{}",
                JsonUtil.toJson(reqVo), JsonUtil.toJson(resVo), e);
            // 处理失败, 发布消息通知
            topicHandler.syncDeleteMessage(userId, deleteAll, questIdList);
        }
        return true;
    }
}
