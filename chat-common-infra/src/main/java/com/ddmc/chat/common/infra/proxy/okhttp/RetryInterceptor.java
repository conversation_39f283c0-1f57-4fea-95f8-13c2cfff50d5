package com.ddmc.chat.common.infra.proxy.okhttp;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 自定义重试拦截器
 */
@Slf4j
public class RetryInterceptor implements Interceptor {
    private final int maxRetry;      // 最大重试次数
    private final long retryDelay;  // 重试间隔时间（毫秒）

    public RetryInterceptor(int maxRetry, long retryDelay) {
        this.maxRetry = maxRetry;
        this.retryDelay = retryDelay;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Response response = null;
        IOException lastException = null;

        for (int attempt = 1; attempt <= maxRetry; attempt++) {
            try {
                response = chain.proceed(request);
                if (response.isSuccessful()) {
                    return response;
                }
            } catch (IOException e) {
                lastException = e;
            }

            log.error("Attempt {} failed. Retrying in {}ms...", attempt, retryDelay * attempt);

            // 添加重试间隔逻辑（倍数退避策略），但最后一次失败无需等待
            if (attempt < maxRetry) {
                try {
                    TimeUnit.MILLISECONDS.sleep(retryDelay * attempt);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new IOException("Retry attempt interrupted", ie);
                }
            }
        }

        if (lastException != null) {
            throw lastException;
        }

        // 如果响应不为空但也不成功，返回响应
        if (response != null) {
            return response;
        }

        throw new IOException("Unknown error occurred");
    }
}