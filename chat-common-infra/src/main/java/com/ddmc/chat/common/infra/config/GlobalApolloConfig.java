package com.ddmc.chat.common.infra.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ddmc.chat.common.domain.bo.ChatEntryConfigBO;
import com.ddmc.chat.common.domain.bo.EntrySceneConfigBO;
import com.ddmc.chat.common.domain.entity.AiChatNlpParam;
import com.ddmc.chat.common.domain.entity.AiChatOpConfig;
import com.ddmc.chat.common.domain.entity.FunctionButton;
import com.ddmc.chat.common.domain.entity.OkHttpClientParam;
import com.ddmc.chat.common.domain.entity.SugWords;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Getter
@Slf4j
@Configuration
@EnableApolloConfig
public class GlobalApolloConfig {

    /**
     * 不同 scene 跳转链接配置
     */
    @ApolloJsonValue("${ai.chat.entry.scene.config:{}}")
    private Map<String, EntrySceneConfigBO> entrySceneConfigMap;

    // deepSeek 深度思考开关默认值实验
    @Value("${ai.chat.ds.reason.switch.default.abtest.exp.id:Deep_thinking_default_exp}")
    private String aiChatDsReasonSwitchAbtestExpId;

    /**
     * DeepSeek 相关设置
     */
    @ApolloJsonValue("${ai.chat.ds.config:{}}")
    private DeepSeekConfig deepSeekConfig;

    // deepSeek 深度思考开关降级开关
    @Value("${ai.chat.ds.reason.degrade.switch:0}")
    private Integer aiChatDsReasonDegradeSwitch;

    // 调用 DeepSeek 的实验 id
    @Value("${ai.chat.deep.seek.abtest.exp.id:search_result_AI_exp}")
    private String aiChatDeepSeekAbtestExpId;

    // 客服消息 button
    @Value("${chat.customer.service.button:<button class=\"btn-link\" data-src=\"%s\">和人工客服沟通</div>}")
    private String customerServiceButton;

    // SSE 后端 -> 算法超时配置
    @ApolloJsonValue("${chat.sse.algorithm.config:{}}")
    private OkHttpClientParam sseOkHttpClientParam;

    // DeepSeek 后端 -> 算法超时配置
    @ApolloJsonValue("${chat.ai.search.sse.algorithm.config:{}}")
    private OkHttpClientParam aiSearchSseOkHttpClientParam;

    // 上传的音频文件大小限制，单位 M， 默认 5M
    @Value("#{${audio.max.size.mb:5} * 1024 * 1024}")
    private long audioMaxSizeInByte;


    // 上传的音频文件的最大保存时长，单位 天，默认 60天
    @Value("#{${audio.max.persist.day:60} * 24 * 60 * 60}")
    private long audioMaxPersistInSecond;

    // 语音功能降级开关
    @Value("${chat.isi.degrade.switch:0}")
    private Integer isiDegradeSwitch;

    // 风控功能降级开关
    @Value("${chat.antispam.degrade.switch:0}")
    private Integer antispamDegradeSwitch;

    // 风控文本检测功能降级开关
    @Value("${chat.antispam.image.check.degrade.switch:0}")
    private Integer antispamImageCheckDegradeSwitch;

    // ab 功能降级开关
    @Value("${chat.ab.degrade.switch:0}")
    private Integer abDegradeSwitch;

    // Session的最长有效时间，单位分钟，默认15分钟
    @Value("${session.max.expire.minute:15}")
    private long sessionMaxExpireMinute;

    // 消息在Redis中临时最大保存时间，单位天，默认7天
    @Value("${message.temp.max.expire.day:7}")
    private long messageTempMaxExpireDay;

    // 用户消息的最大长度，无论是语音还是文字，最长不能超过这个限制
    @Value("${message.user.max.length:300}")
    private int messageUserMaxLength;

    @Value("${chat.sse.timeout: 10000}")
    private long sseTimeout;

    // summary 降级开关
    @Value("${summary.degrade.switch:0}")
    private Integer summarySwitch;

    // 语音功能的后台AB实验ID
    @Value("${audio.abtest.exp.id:maicai_AIchat_yuyinwenda}")
    private String audioAbtestExpId;

    @Value("${audio.file.format:.mp3}")
    private String audioFileFormat;

    // 是否需要显示AI入口的开关，目前仅商详页调用
    // 后续新增的分类页入口、搜索页入口等，不用配置分流条件，但需要给linkURL后附加不同的跳转参数
    @ApolloJsonValue("${chat.entry.config:{}}")
    private ChatEntryConfigBO chatEntryConfigBO;

    // ai机器人的头像
    @Value("${chat.entry.avatar:https://pub.ddimg.mobi/develop/e8586988c73341348f9fe08621789db5.png?imageView2/2/w/100/q/80/ignore-error/1/format/webp}")
    private String avatar;

    // 新版ai机器人的头像
    @Value("${chat.entry.avatar2:https://pub.ddimg.mobi/promo-admin-service/0a2efb07b8384c4a807796a5cc0b56a7.png}")
    private String avatar2;

    // 兜底的欢迎语，当算法不返回时再用这个
    @Value("${chat.entry.welcome:您好，我是叮小咚，作为您的智能伙伴。我既能回答商品详情知识，又能陪你聊天、答疑解惑。尽管来问我吧！}")
    private String welcome;

    // 【可公开】aliyun语音项目，必须传AppKey，这个是在aliyun后台申请项目时自动生成的，类似domain的作用
    // see https://help.aliyun.com/zh/isi/getting-started/obtain-an-access-token-1/
    @Value("${aliyun.app.key:Gas3dp1TULCVaFhm}")
    private String aliyunAppKey;

    // 【可公开】aliyun 智能语音交互（Intelligent Speech Interaction）的子能力，语音ASR/语音合成TTS 时候，需要传入的URL地址，aliyun提供的地址，分 上海/北京/深圳三个接入节点
    // ASR see https://help.aliyun.com/zh/isi/developer-reference/api-reference, example: wss://nls-gateway.aliyuncs.com/ws/v1, 支持就近地域智能接入
    // 这个地址是流式接口地址，无论是 asr还是tts，都直接访问这个域名即可
    // 测试阶段，发现ios平台连接智能域名会导致超时情况变多，android平台正常。咨询阿里云后，建议直接使用上海节点，考虑到我们的服务器都部署在华东，这样也合理
    @Value("${aliyun.app.isi.asr.stream.url:wss://nls-gateway-cn-shanghai.aliyuncs.com/ws/v1}")
    private String aliyunAppIsiAsrStreamUrl;

    // 【可公开】aliyun 智能语音交互（Intelligent Speech Interaction）的子能力，语音ASR/语音合成TTS 时候，需要传入的URL地址，aliyun提供的地址，分 上海/北京/深圳三个接入节点
    // TTS see https://help.aliyun.com/zh/isi/developer-reference/interface-description, example: wss://nls-gateway.aliyuncs.com/ws/v1, 支持就近地域智能接入
    // 这个地址是流式接口地址，无论是 asr还是tts，都直接访问这个域名即可
    // 测试阶段，发现ios平台连接智能域名会导致超时情况变多，android平台正常。咨询阿里云后，建议直接使用上海节点，考虑到我们的服务器都部署在华东，这样也合理
    // 还有另外一个TTS的地址是 非流式接口地址（https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/tts），这个接口是将文字转换为音频文件返回，请求时需要指定返回的音频文件格式
    @Value("${aliyun.app.isi.tts.stream.url:wss://nls-gateway-cn-shanghai.aliyuncs.com/ws/v1}")
    private String aliyunAppIsiTtsStreamUrl;

    // 【可公开】aliyun tts的语音合成时指定的多情感语音模型，see https://help.aliyun.com/zh/isi/developer-reference/overview-of-speech-synthesis
    @Value("${aliyun.app.tts.tone:aixia}")
    private String aliyunAppTtsTone;


    // 【不可公开】aliyun 获取刷新accessToken的服务端接口包装，see https://help.aliyun.com/zh/isi/getting-started/use-http-or-https-to-obtain-an-access-token
    @Value("${aliyun.app.token.domain:nls-meta.cn-shanghai.aliyuncs.com}")
    private String aliyunAppTokenDomain;

    // 【不可公开】aliyun 获取刷新accessToken 所必须的 accessKeyId，与 aliyunAppTokenUrl 一起使用
    @Value("${aliyun.app.access.key.id:LTAI5tQJYxTy76XqzFifRXvx}")
    private String aliyunAppAccessKeyId;

    // 【不可公开】aliyun 获取刷新accessToken 所必须的 accessKeySecret，与 aliyunAppTokenUrl 一起使用
    @Value("${aliyun.app.access.key.Secret:******************************}")
    private String aliyunAppAccessKeySecret;



    // 算法服务降级开关
    @Value("${nlp.degrade.switch:0}")
    private Integer nlpSwitch;

    // 调用算法必传的token appKey
    @Value("${nlp.chat.api.token:16727a640f5e8f7b649b5dd2}")
    private String token;

    @Value("#{${nlp.chat.api.token.map:{\"detail\":\"16727a640f5e8f7b649b5dd2\",\"category\":\"19a88675cd3cc5ab2f5f5c97\",\"search_result\":\"0e198467a175ae3a0db66da9\"}}}")
    private Map<String, String> nlpTokenMap;

    // 用户发言命名风控后的提示语
    @Value("${antispam.chat.api.prompt:您发送的消息可能包含敏感信息，请停止发送类似消息}")
    private String antispamPrompt;

    // 至多查询多少天前的数据，默认7天
    @Value("${history.date.limit:7}")
    private Integer historyDateLimit;

    // 每次拉取历史消息的最大条数，默认20条
    @Value("${history.message.page.size:20}")
    private Integer historyMessagePageSize;

    // appendContent 中 url 类型需要需要持久化存储的 purpose
    @ApolloJsonValue("${chat.append.type.url.purpose.persistent:['到货提醒']}")
    private List<String> urlPurposePersistentList;

    public boolean isUrlPurposePersistent(String purpose) {
        return CollectionUtils.isNotEmpty(urlPurposePersistentList)
            && urlPurposePersistentList.contains(purpose);
    }

    // 商品分段消息格式
    @Value("${history.goods.segment.text:咨询商品：【%s】}")
    private String goodsSegmentText;

    @Value("${chat.ai.model.timeout: 600000}")
    private long aiChatTimeout;

    // 请求算法 nlp 参数
    @ApolloJsonValue("${ai.chat.nlp.api.param:[]}")
    private List<AiChatNlpParam> aiChatNlpParamList;

    public AiChatNlpParam getAiChatNlpParam(String scene) {
        if (CollectionUtils.isEmpty(aiChatNlpParamList)) {
            return null;
        }
        return aiChatNlpParamList.stream()
            .filter(aiChatNlpParam -> StringUtils.equals(aiChatNlpParam.getScene(), scene))
            .findFirst()
            .orElse(null);
    }

    // 功能词动作降级开关
    @Value("${ai.chat.op.degrade.switch:0}")
    private Integer aiChatOpDegradeSwitch;

    // 功能词动作配置
    @ApolloJsonValue("${ai.chat.op.config.map:{}}")
    private Map<String, AiChatOpConfig> aiChatOpConfigMap;

    public AiChatOpConfig getAiChatOpConfig(String type) {
        return aiChatOpConfigMap.get(type);
    }

    // 底部功能 button 配置
    @ApolloJsonValue("${chat.function.button.list:[]}")
    private List<FunctionButton> functionButtonList;

    // sug 词配置
    @ApolloJsonValue("${chat.sug.words.list:[]}")
    private List<SugWords> sugWordsList;


    // 图片被风控后展示的默认兜底图
    @Value("${chat.default.image.url:https://pub.ddimg.mobi/develop/8c4ab9e457d34ea38de6a57ba04c5998.png}")
    private String chatDefaultImageUrl;

    // 线程默认超时时间
    @Value("${chat.executor.sub.thread.get.timeout:1000}")
    private Integer threadTimeout;

    // 可分享回答轮数
    @Value("${chat.share.number:6}")
    private Integer shareNumber;

    // 可删除回答条数
    @Value("${chat.delete.number:100}")
    private Integer deleteNumber;

    // 是否写会分段消息给前端开关
    @Value("${chat.segment.message.write.switch:false}")
    private Boolean writeSegmentMessageSwitch;

    // 拍照功能降级开关
    @Value("${ai.chat.photo.degrade.switch:0}")
    private Integer aiChatPhotoDegradeSwitch;

    // 叮小咚调用算法新老接口实验 id，exp 为实验组，命中实验组，调用算法新接口，其他组都走老接口
    @Value("${ai.chat.invoke.abtest.exp.id:product_apijiekou_replace}")
    private String aiChatInvokeAbtestExpId;

    // 未提交消息任务处理频次，单位: 分钟
    @Value("${ai.chat.message.schedule.freq: 30}")
    private Long messageScheduleFreq;

    // 未提交消息任务可执行集群
    @ApolloJsonValue("${ai.chat.message.schedule.switch: ['TE', 'SHT5PE', 'SHHW1PE']}")
    private List<String> messageScheduleSwitchList;

}
