package com.ddmc.chat.common.infra.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ddmc.chat.common.domain.dto.req.chat.ChatHistoryReq;
import com.ddmc.chat.common.mapper.DO.MessageBoxDO;
import com.ddmc.chat.common.mapper.DO.TopicMessageDO;
import com.ddmc.chat.common.mapper.MessageBoxMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2024/9/3 2:07 PM
 * @Description:
 */
@Component
public class MessageBoxDAO {

    @Autowired
    private MessageBoxMapper messageBoxMapper;

    @Autowired
    private TopicMessageDAO topicMessageDAO;

    /**
     * 更新消息,分库查询需要带上shardingKey
     */
    public boolean updateMessageByOwnerAndMessageId(MessageBoxDO messageBoxDO){
        LambdaUpdateWrapper<MessageBoxDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MessageBoxDO::getOwner, messageBoxDO.getOwner());
        wrapper.eq(MessageBoxDO::getMessageId, messageBoxDO.getMessageId());
        wrapper.set(MessageBoxDO::getContent, messageBoxDO.getContent());

        return messageBoxMapper.update(messageBoxDO, wrapper) > 0;

    }

    public MessageBoxDO selectByMessageIdAndOwner(Long messageId, String owner){
        return messageBoxMapper.selectByMessageIdAndOwner(messageId, owner);
    }

    public boolean updateMessageWithAudioFileKey(String uid, Long messageId, String audioFileKey) {
        LambdaUpdateWrapper<MessageBoxDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MessageBoxDO::getOwner, uid);
        wrapper.eq(MessageBoxDO::getMessageId, messageId);
        wrapper.set(MessageBoxDO::getAudioFileKey, audioFileKey);
        return messageBoxMapper.update(null, wrapper) > 0;
    }

    /**
     * 写入消息
     */
    public int insertMessage(MessageBoxDO messageBoxDO){
        return messageBoxMapper.insert(messageBoxDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean insertMessageAndTopic(MessageBoxDO messageBoxDO, TopicMessageDO topicMessageDO){
        return messageBoxMapper.insert(messageBoxDO) > 0 && topicMessageDAO.save(topicMessageDO);
    }

    public List<MessageBoxDO> selectMessage(ChatHistoryReq chatHistoryReq, Integer limit, LocalDateTime dateTime) {
        LambdaQueryWrapper<MessageBoxDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageBoxDO::getOwner, chatHistoryReq.getUid());
        queryWrapper.ge(MessageBoxDO::getCreateTime, dateTime);
        queryWrapper.eq(MessageBoxDO::getCommitStatus, 1);
        queryWrapper.lt(MessageBoxDO::getMessageId, chatHistoryReq.getLastMessageId());
        queryWrapper.orderByDesc(MessageBoxDO::getMessageId);
        queryWrapper.last("limit " + limit);
        return messageBoxMapper.selectList(queryWrapper);
    }

    public List<MessageBoxDO> selectMessageByUserId(String uid, Integer limit, LocalDateTime dateTime) {
        LambdaQueryWrapper<MessageBoxDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageBoxDO::getOwner, uid);
        queryWrapper.ge(MessageBoxDO::getCreateTime, dateTime);
        queryWrapper.eq(MessageBoxDO::getCommitStatus, 1);
        queryWrapper.orderByDesc(MessageBoxDO::getMessageId);
        queryWrapper.last("limit " + limit);
        return messageBoxMapper.selectList(queryWrapper);
    }

    public List<MessageBoxDO> selectUnCommitMessage(Integer count, LocalDateTime dateTime) {
        LambdaQueryWrapper<MessageBoxDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageBoxDO::getCommitStatus, 0);
        queryWrapper.ge(MessageBoxDO::getCreateTime, dateTime);
        queryWrapper.last("limit " + count);
        return messageBoxMapper.selectList(queryWrapper);
    }

    public boolean deleteMessageByTime(String userId, LocalDateTime dateTime){
        LambdaUpdateWrapper<MessageBoxDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MessageBoxDO::getOwner, userId);
        wrapper.ge(MessageBoxDO::getCreateTime, dateTime);
        wrapper.set(MessageBoxDO::getIsDelete, 1);
        MessageBoxDO messageBoxDO = new MessageBoxDO();
        messageBoxDO.setIsDelete(1);
        return messageBoxMapper.update(messageBoxDO, wrapper) > 0;
    }

    public boolean deleteMessageById(String userId, List<Long> messageIdList){
        LambdaUpdateWrapper<MessageBoxDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MessageBoxDO::getOwner, userId);
        wrapper.in(MessageBoxDO::getMessageId, messageIdList);
        wrapper.set(MessageBoxDO::getIsDelete, 1);
        MessageBoxDO messageBoxDO = new MessageBoxDO();
        messageBoxDO.setIsDelete(1);
        return messageBoxMapper.update(messageBoxDO, wrapper) > 0;
    }

    public List<MessageBoxDO> selectMessageById(String userId, List<Long> messageIdList){
        LambdaUpdateWrapper<MessageBoxDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MessageBoxDO::getOwner, userId);
        wrapper.in(MessageBoxDO::getMessageId, messageIdList);
        return messageBoxMapper.selectList(wrapper);
    }

    public List<MessageBoxDO> selectMessageBySessionId(String userId, List<String> sessionIdList){
        LambdaUpdateWrapper<MessageBoxDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MessageBoxDO::getOwner, userId);
        wrapper.in(MessageBoxDO::getSessionId, sessionIdList);
        return messageBoxMapper.selectList(wrapper);
    }
}
