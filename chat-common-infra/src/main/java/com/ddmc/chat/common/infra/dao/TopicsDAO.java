package com.ddmc.chat.common.infra.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ddmc.chat.common.mapper.DO.TopicsDO;
import com.ddmc.chat.common.mapper.TopicsMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class TopicsDAO {

    private final TopicsMapper topicsMapper;

    public boolean save(TopicsDO topicsDO) {
        return topicsMapper.insert(topicsDO) > 0;
    }

    public TopicsDO selectByUserId(String userId) {
        LambdaQueryWrapper<TopicsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TopicsDO::getUserId, userId);
        wrapper.orderByDesc(TopicsDO::getCreateTime);
        wrapper.last("limit 1");
        return topicsMapper.selectOne(wrapper);
    }
} 