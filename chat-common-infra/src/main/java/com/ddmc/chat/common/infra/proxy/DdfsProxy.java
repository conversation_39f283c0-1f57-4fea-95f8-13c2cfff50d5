package com.ddmc.chat.common.infra.proxy;

import com.alibaba.fastjson2.JSON;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ddfs.client.DdfsClient;
import com.ddmc.ddfs.request.FileUpRequestParams;
import com.ddmc.ddfs.response.BriefFileInfo;
import com.ddmc.ddfs.response.UpFileResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.net.URL;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DdfsProxy {

    private static final String APP_ID = "chat-api-service";

    @Autowired
    private DdfsClient ddfsClient;

    @Autowired
    private GlobalApolloConfig globalApolloConfig;

    /**
     * Upload file to private bucket.
     *
     * @param file
     * @param messageId
     * @return fileKey. If upload failed, return null.
     *      Later when needs to download file, there are two ways to download file:
     *      1. use fileKey and invoke DdfsClient.downloadWithAuth() method, will return file body directly. Usually used in RPC call.
     *      2. use fileKey and invoke DdfsClient.getAccReadLinks() method, will return download url, which is actual "/auth/download?fileKey=xxx" url.
     *      Then the client can use this url to download file. Usually used in web page, And this Url will check user's login status.
     */
    public String uploadUserAudioFile(MultipartFile file, String messageId) {
        String eventType = "DDFS_priBucketFileUpload";
        try {
            FileUpRequestParams request = new FileUpRequestParams();
            request.setAppId(APP_ID);  // constant, chat-api-service
            request.setFile(file);
            request.setDownName(messageId + globalApolloConfig.getAudioFileFormat()); // message id, which represent the message text, used for download file name, must be full file name with file extension
            request.setExpireSeconds(globalApolloConfig.getAudioMaxPersistInSecond());    // file expire time, default 60 days and convert it to second

            ResponseBaseVo<UpFileResponse> responseBaseVo = ddfsClient.priBucketFileUpload(request);
            log.debug("私有文件上传接口response：{}", JSON.toJSONString(responseBaseVo));

            if (responseBaseVo == null) {
                MonitorUtil.counterOnce(eventType, "ERROR", "-1", String.format("messageId=%s,err=%s", messageId, "response is null"));
                return null;
            }

            if (responseBaseVo.isSuccess() && responseBaseVo.getData() != null) {
                MonitorUtil.counterOnce(eventType, "SUCCESS", "0", "messageId=" + messageId);
                return responseBaseVo.getData().getFileKey();
            } else {
                MonitorUtil.counterOnce(eventType, "FAIL", "-1", String.format("messageId=%s,code=%s,message=%s", responseBaseVo.getCode(), responseBaseVo.getMsg()));
            }
        } catch (Exception e) {
            log.error("私有文件上传接口异常", e);
            MonitorUtil.counterOnce(eventType, "ERROR", "-1", String.format("messageId=%s,err=%s", messageId, e.getMessage()));
        }

        return null;
    }


    /**
     * Get private file's download URL.
     *
     * @param fileKeys
     * @return file download URL list. If failed, return null.
     */
    public List<URL> getDownloadAuthUrl(List<String> fileKeys) {
        String eventType = "DDFS_getAccReadLinks";

        try {
            if (fileKeys == null || fileKeys.isEmpty()) {
                MonitorUtil.counterOnce(eventType, "ERROR", "-1", "fileKeys is empty");
                return null;
            }

            ResponseBaseVo<List<BriefFileInfo>> responseBaseVo = ddfsClient.getAccReadLinks(fileKeys);
            log.debug("获取下载链接接口response：{}", JSON.toJSONString(responseBaseVo));

            if (responseBaseVo == null) {
                MonitorUtil.counterOnce(eventType, "ERROR", "-1", "response is null");
                return null;
            }

            if (responseBaseVo.isSuccess() && responseBaseVo.getData() != null) {
                MonitorUtil.counterOnce(eventType, "SUCCESS", "0", "fileKeys=" + fileKeys);
                return responseBaseVo.getData().stream().map(BriefFileInfo::getUrl).collect(Collectors.toList());
            } else {
                MonitorUtil.counterOnce(eventType, "FAIL", "-1", String.format("code=%s,message=%s", responseBaseVo.getCode(), responseBaseVo.getMsg()));
            }


        } catch (Exception e) {
            log.error("获取下载链接异常", e);
            MonitorUtil.counterOnce(eventType, "ERROR", "-1", e.getMessage());
        }

        return null;
    }

}
