package com.ddmc.chat.common.infra.proxy;

import com.ddmc.abtest.client.AbTestClient;
import com.ddmc.abtest.dto.AbResDTO;
import com.ddmc.abtest.dto.AbTestHitResultDTO;
import com.ddmc.abtest.dto.AbTestRespDTO;
import com.ddmc.chat.common.constant.Constans;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.util.MonitorUtil;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class ABTestProxy {

    @Autowired
    private AbTestClient abTestClient;

    @Autowired
    private GlobalApolloConfig globalApolloConfig;

    /**
     * Invoke ABTest service to get ABTest result.
     * @param chatContext user relative context, used for AB diversion
     * @param expLayerId experiment layer id
     * @return null if any exception happens, or the experiment close, or not meet the experiment condition;
     *         otherwise will return the matching ABTest experiment group name
     */
    public String abTest(ChatContext chatContext, String expLayerId) {

        if (isFallbackOn()) {
            log.warn("触发手动降级: ABTestProxy.abTest");
            MonitorUtil.counterOnce("triggerDegrade", "ABTestProxy_abTest", "-1", "abTestClient.ab2");
            return null;
        }

        if (StringUtils.isBlank(expLayerId)) {
            return null;
        }

        try {
            // use new AB client api
            AbResDTO<AbTestRespDTO> abResDTO = abTestClient.ab2(chatContext.getUid(), expLayerId,
                    chatContext.getDeviceId(), chatContext.getAppVersion(), chatContext.getAppClientId(),
                    chatContext.getCityNumber(), chatContext.getStationId());

            if (abResDTO != null && abResDTO.getErrNo() == 0 && abResDTO.getData() != null) {
                Map<String, AbTestHitResultDTO> abTestResp = abResDTO.getData().getHitResult();
                if (abTestResp != null && abTestResp.containsKey(expLayerId)) {
                    AbTestHitResultDTO expDto = abTestResp.get(expLayerId);
                    if (expDto != null && expDto.isHit() && StringUtils.isNotBlank(expDto.getGroup())) {
                        return expDto.getGroup();
                    }
                }
            }
        } catch (Exception e) {
            // in case any exception happens, like timeout
            log.warn("Error occurred during remote-call ABTest service", e);
        }

        return null;
    }

    private boolean isFallbackOn() {
        return Objects.equals(globalApolloConfig.getAbDegradeSwitch(), Constans.ONE);
    }

}
