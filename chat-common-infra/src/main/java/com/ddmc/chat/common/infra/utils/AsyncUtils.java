package com.ddmc.chat.common.infra.utils;

import com.csoss.monitor.api.trace.Span;
import com.csoss.monitor.api.trace.StatusCode;
import com.csoss.monitor.api.trace.Traces;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Supplier;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AsyncUtils {

    @Resource(name = "defaultExecutorPoolService")
    private ExecutorService executorService;

    public <T> CompletableFuture<T> supplyAsync(Supplier<T> runnable, String spanName) {
        Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
        return CompletableFuture.supplyAsync(() -> {
            //记录子调用信息(span),包含耗时，不需要在单独统计Timer
            Span threadSpan = Traces.spanBuilder(spanName).startSpan();
            T resp;
            try {
                if (MapUtils.isNotEmpty(copyOfContextMap)) {
                    MDC.setContextMap(copyOfContextMap);
                }
                resp = runnable.get();
                // 子调用成功
                threadSpan.setStatus(StatusCode.OK);
            } catch (Exception ex) {
                // 子调用报错
                threadSpan.recordException(ex);
                threadSpan.setStatus(StatusCode.ERROR);
                throw ex;
            } finally {
                try {
                    MDC.clear();
                    // 子调用完成
                    threadSpan.end();
                } catch (Exception e) {
                    log.error("csoss打点异常 e: ", e);
                }
            }
            return resp;
        }, executorService).exceptionally(e -> {
            log.error("AsyncUtils supplyAsync error", e);
            return null;
        });
    }

}
