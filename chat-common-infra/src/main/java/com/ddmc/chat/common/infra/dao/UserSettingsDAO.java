package com.ddmc.chat.common.infra.dao;

import static com.ddmc.chat.common.domain.Constants.SETTING_KEY_AUDIO_AUTOPLAY;
import static com.ddmc.chat.common.domain.Constants.SETTING_KEY_REASON_SWITCH;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ddmc.chat.common.mapper.DO.UserSettingsDO;
import com.ddmc.chat.common.mapper.UserSettingsMapper;
import java.util.Map;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserSettingsDAO {

    @Autowired
    private UserSettingsMapper userSettingsMapper;


    public boolean saveOrUpdateUserSettings(String uid, Map<String, Integer> settingsMap) {
        // prepare a new UserSettingsDO holder
        UserSettingsDO latestSettings = new UserSettingsDO();
        latestSettings.setOwner(uid);
        if (settingsMap.containsKey(SETTING_KEY_AUDIO_AUTOPLAY)) {
            latestSettings.setAudioAutoPlay(settingsMap.get(SETTING_KEY_AUDIO_AUTOPLAY));
        }
        if (settingsMap.containsKey(SETTING_KEY_REASON_SWITCH)) {
            latestSettings.setReasonSwitch(settingsMap.get(SETTING_KEY_REASON_SWITCH));
        }

        // check if the user settings already exist
        UserSettingsDO existSettings = selectUserSettingsByOwner(uid);

        int affected = 0;
        if (existSettings != null) {
            affected = updateSettings(latestSettings, existSettings);
        } else {
            affected = userSettingsMapper.insert(latestSettings);
        }

        return affected > 0;
    }

    public int updateSettings(UserSettingsDO latestSettings, UserSettingsDO existSettings) {
        // 只更新有改变的字段，减少数据库压力
        boolean noNeedUpdate = true;

        LambdaUpdateWrapper<UserSettingsDO> wrapper = new LambdaUpdateWrapper();
        wrapper.eq(UserSettingsDO::getOwner, existSettings.getOwner());

        if (Objects.nonNull(latestSettings.getAudioAutoPlay())
            && !Objects.equals(latestSettings.getAudioAutoPlay(), existSettings.getAudioAutoPlay())) {
            wrapper.set(UserSettingsDO::getAudioAutoPlay, latestSettings.getAudioAutoPlay());
            noNeedUpdate = false;
        }
        if (Objects.nonNull(latestSettings.getReasonSwitch())
            && !Objects.equals(latestSettings.getReasonSwitch(), existSettings.getReasonSwitch())) {
            wrapper.set(UserSettingsDO::getReasonSwitch, latestSettings.getReasonSwitch());
            noNeedUpdate = false;
        }

        // 若无需更新，直接返回1，标识（虚拟）更新成功；否则返回实际发生变更的数据行数
        return noNeedUpdate ? 1 : userSettingsMapper.update(null, wrapper);
    }

    public UserSettingsDO selectUserSettingsByOwner(String owner) {
        LambdaQueryWrapper<UserSettingsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSettingsDO::getOwner, owner);
        return userSettingsMapper.selectOne(queryWrapper);
    }

}
