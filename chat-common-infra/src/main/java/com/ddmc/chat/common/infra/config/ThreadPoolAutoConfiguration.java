package com.ddmc.chat.common.infra.config;

import com.ddmc.guide.enhance.log.MetricHelper;
import com.ddmc.soa.concurrent.WrappedExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class ThreadPoolAutoConfiguration {

    @Value("${executor.thread.core-pool-size:20}")
    private Integer corePoolSize;

    @Value("${executor.thread.max-pool-size:20}")
    private Integer maxPoolSize;

    @Value("${executor.thread.queue-capacity-strategy:1000}")
    private Integer queueCapacity;

    @Value("${executor.thread.keep-alive-seconds:60}")
    private Integer keepAliveSecond;

    @Value("${thread.search.sug.query.pool.maxPoolSize:20}")
    private Integer searchSugMaxPoolSize;
    @Value("${thread.search.sug.query.pool.corePoolSize:20}")
    private Integer searchSugCorePoolSize;

    @Value("${thread.search.sug.query.pool.queueCapacity:200}")
    private Integer searchSugQueueCapacity;

    @Value("${executor.thread.keep-alive-seconds:60}")
    private Integer searchSugKeepAliveSecond;

    private ThreadPoolExecutor threadPoolExecutor;

    @Bean(name = "defaultExecutorPoolService")
    public ExecutorService defaultExecutorPoolService() {
        threadPoolExecutor = new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAliveSecond, TimeUnit.SECONDS,
            // 缓冲队列：用来缓冲执行任务的队列
            new LinkedBlockingQueue<>(queueCapacity),
            // 线程池名的前缀：设置好了之后可以方便我们定位处理任务所在的线程池
            new ThreadFactoryBuilder().setNameFormat("chat-api-taskExecutorPool-%d").build(),
            // 缓冲队列满了之后的拒绝策略：由调用线程处理（一般是主线程）
            new DiscardEventPolicy());
        return WrappedExecutors.wrap(threadPoolExecutor);
    }

    public static class DiscardEventPolicy implements RejectedExecutionHandler {

        public DiscardEventPolicy() {
            // ignored
        }

        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
            //加个打点
            String msg = String.format("Thread pool is EXHAUSTED!" +
                    " Thread Name: %s, Pool Size: %d (active: %d, core: %d, max: %d, largest: %d), Task: %d (completed: %d)," +
                    " Executor status:(isShutdown:%s, isTerminated:%s, isTerminating:%s)!",
                Thread.currentThread().getName(), e.getPoolSize(), e.getActiveCount(), e.getCorePoolSize(), e.getMaximumPoolSize(), e.getLargestPoolSize(),
                e.getTaskCount(), e.getCompletedTaskCount(), e.isShutdown(), e.isTerminated(), e.isTerminating()
            );
            MetricHelper.error("thread_pool_full", "DiscardEventPolicy", msg);
            if (!e.isShutdown()) {
                r.run();
            }
        }
    }
}
