package com.ddmc.chat.common.infra.handler;

import static com.ddmc.chat.common.domain.Constants.SETTING_VALUE_DEFAULT;
import static com.ddmc.chat.common.domain.Constants.SETTING_VALUE_OFF;
import static com.ddmc.chat.common.domain.Constants.SETTING_VALUE_ON;

import com.ddmc.chat.common.constant.Constans;
import com.ddmc.chat.common.infra.config.DeepSeekConfig;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.dao.UserSettingsDAO;
import com.ddmc.chat.common.infra.proxy.ABTestProxy;
import com.ddmc.chat.common.infra.proxy.ChatContext;
import com.ddmc.chat.common.mapper.DO.UserSettingsDO;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeepSeekHandler {

    @Autowired
    private UserSettingsDAO userSettingsDAO;

    @Autowired
    private GlobalApolloConfig globalApolloConfig;

    @Autowired
    private ABTestProxy abTestProxy;

    /**
     * 获取深度思考开关
     *
     * @param uid uid
     * @return -1:降级不展示 0:关闭 1:开启
     */
    public Integer queryReasonSwitch(String uid) {
        DeepSeekConfig deepSeekConfig = globalApolloConfig.getDeepSeekConfig();
        try {
            // 是否降级
            if (isDeepSeekReasonSwitchDegrade()) {
                return SETTING_VALUE_DEFAULT;
            }

            // 查询用户配置
            UserSettingsDO userSettingsDO = userSettingsDAO.selectUserSettingsByOwner(uid);
            if (Objects.nonNull(userSettingsDO)
                && !Objects.equals(userSettingsDO.getReasonSwitch(), SETTING_VALUE_DEFAULT)) {
                // 非默认值，返回用户实际配置
                return userSettingsDO.getReasonSwitch();
            }

            // Ab 实验
            ChatContext chatContext = new ChatContext();
            chatContext.setUid(uid);
            String testGroup = abTestProxy.abTest(chatContext, globalApolloConfig.getAiChatDsReasonSwitchAbtestExpId());
            if (StringUtils.isNotBlank(testGroup) && "exp".equals(testGroup)) {
                // 命中实验组，默认关闭
                return SETTING_VALUE_OFF;
            }
            // 未命中实验组，默认打开
            return SETTING_VALUE_ON;
        } catch (Exception e) {
            log.error("queryReasonSwitch error，uid: {}", uid, e);
        }
        // 用户未设置过或出现其他异常，从 Apollo 取默认开关值
        return deepSeekConfig.getReasonDefaultSwitch();
    }

    /**
     * 深度思考降级开关判断
     *
     * @return true:降级 false:不降级
     */
    public boolean isDeepSeekReasonSwitchDegrade() {
        return Objects.equals(globalApolloConfig.getAiChatDsReasonDegradeSwitch(), Constans.ONE);
    }

}
