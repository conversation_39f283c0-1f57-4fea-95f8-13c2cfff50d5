package com.ddmc.chat.common.infra.pulsar;

import com.ddmc.guide.enhance.pulsar.BaseProducer;
import com.ddmc.guide.enhance.pulsar.PulsarConfigProperties;
import com.ddmc.guide.enhance.pulsar.PulsarFactory;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class DeleteMessageProducer extends BaseProducer {

    public DeleteMessageProducer(PulsarFactory pulsarFactory, PulsarConfigProperties properties) {
        super(pulsarFactory, properties);
    }

}
