package com.ddmc.chat.common.infra.handler;

import com.ddmc.chat.common.constant.CacheKeyConstants;
import com.ddmc.chat.common.domain.entity.DeleteMessage;
import com.ddmc.chat.common.infra.dao.TopicsDAO;
import com.ddmc.chat.common.mapper.DO.TopicsDO;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.chat.common.util.OUIDGenerator;
import com.ddmc.guide.enhance.json.JsonUtil;
import com.ddmc.guide.enhance.pulsar.BaseProducer;
import com.ddmc.guide.enhance.redis.RedisClient;
import java.time.Duration;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TopicHandler {

    @Autowired
    private TopicsDAO topicsDAO;

    @Resource(name = "deleteMessageProducer")
    private BaseProducer deleteMessageProducer;

    @Resource(name = "transformersRedisClient")
    private RedisClient transformersRedisClient;

    // topicId 缓存有效期，单位:分钟
    @Value("${ai.chat.topic.id.expire.minute:30}")
    private Integer topicIdExpireMinute;

    private static final String TOPIC_CACHE_OP = "topic_cache_op";

    /**
     * 删除 topicId 缓存
     *
     * @param userId 用户 id
     */
    public void deleteTopicIdCache(String userId) {
        if (StringUtils.isBlank(userId)) {
            return;
        }
        try {
            String topicIdCacheKey = CacheKeyConstants.getTopicIdCacheKey(userId);
            transformersRedisClient.delete(topicIdCacheKey);
        } catch (Exception e) {
            log.error("deleteTopicIdCache error, userId: {}", userId, e);
            MonitorUtil.counterOnce(TOPIC_CACHE_OP, "deleteTopicIdCacheFail");
        }
    }

    /**
     * 新增 topicId 缓存
     *
     * @param userId  用户 id
     * @param topicId topic id
     */
    public void saveTopicIdCache(String userId, String topicId) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(topicId)) {
            return;
        }
        try {
            String topicIdCacheKey = CacheKeyConstants.getTopicIdCacheKey(userId);
            transformersRedisClient.setStr(topicIdCacheKey, topicId, Duration.ofMinutes(topicIdExpireMinute));
        } catch (Exception e) {
            log.error("saveTopicIdCache error, userId: {}, topicId: {}", userId, topicId, e);
            MonitorUtil.counterOnce(TOPIC_CACHE_OP, "saveTopicIdCacheFail");
        }
    }

    /**
     * 获取最新的 topicId
     *
     * @param userId 用户 id
     * @return
     */
    public String getLatestTopicIdCache(String userId) {
        if (StringUtils.isBlank(userId)) {
            return null;
        }
        try {
            String topicIdCacheKey = CacheKeyConstants.getTopicIdCacheKey(userId);
            return transformersRedisClient.getStr(topicIdCacheKey);
        } catch (Exception e) {
            log.error("getLatestTopicIdCache error, userId: {}", userId, e);
            MonitorUtil.counterOnce(TOPIC_CACHE_OP, "getLatestTopicIdCacheFail");
        }
        return null;
    }

    /**
     * 获取用户话题 id
     *
     * @param userId 用户 id
     * @return
     */
    public String getTopicIdOrInsert(String userId) {
        // 从 redis 中获取 topicId
        String topicIdByCache = getLatestTopicIdCache(userId);

        if (StringUtils.isNotBlank(topicIdByCache)) {
            // 缓存中存在，直接返回
            return topicIdByCache;
        }

        // 缓存中不存在，从数据库查询当前用户最新的一个 topicId
        TopicsDO topicsDO = topicsDAO.selectByUserId(userId);
        if (topicsDO != null) {
            // 从数据库查询到后再存入 redis
            saveTopicIdCache(userId, topicsDO.getTopicId());
            return topicsDO.getTopicId();
        }

        String topicId = OUIDGenerator.generate("t");
        TopicsDO newTopicsDO = new TopicsDO();
        newTopicsDO.setTopicId(topicId);
        newTopicsDO.setUserId(userId);

        boolean save = topicsDAO.save(newTopicsDO);
        if (!save) {
            // 创建话题失败理论上不应该影响用户开启会话，此处暂时只做告警监控，不做中断
            log.warn("save chat fail, userId: {}", userId);
            MonitorUtil.counterOnce("topic_op", "get_topic_id_or_insert_fail");
        }
        return topicId;
    }

    /**
     * 删除聊天记录消息
     *
     * @param userId      用户 id
     * @param deleteAll   是否清空聊天记录
     * @param questIdList questId
     */
    public void syncDeleteMessage(String userId, Boolean deleteAll, List<String> questIdList) {
        if (StringUtils.isBlank(userId)) {
            return;
        }
        try {
            DeleteMessage deleteMessage = DeleteMessage.builder()
                .userId(userId)
                .deleteAll(deleteAll)
                .questIdList(questIdList)
                .build();
            String payload = JsonUtil.toJson(deleteMessage);
            deleteMessageProducer.asyncSendMessage(payload);
            log.info("syncDeleteMessage payload: {}", payload);
        } catch (Exception e) {
            log.error("syncDeleteMessage error, userId:{}, questIdList:{}", userId, JsonUtil.toJson(questIdList), e);
            MonitorUtil.counterOnce("topic_message_op", "sync_delete_message_fail");
        }
    }

    public void deleteSegmentCacheKey(String userId, String sessionId) {
        String segmentCacheKey = CacheKeyConstants.getSessionSegmentCacheKey(sessionId);
        try {
            transformersRedisClient.delete(segmentCacheKey);
        } catch (Exception e) {
            log.error("deleteSegmentCacheKey error, userId:{}, sessionId: {}", userId, sessionId, e);
            MonitorUtil.counterOnce("topic_op", "delete_segment_message_key_fail");
        }
    }
}
