package com.ddmc.chat.common.infra.dao.source;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper=false)
public class CommonFieldDO extends AutoIncrementDO {

    @TableField(value = "created_at")
    private Date createdAt;

    @TableField(value = "updated_at")
    private Date updatedAt;

    @TableField(value = "created_by")
    private String createdBy;

    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 是否删除 0:不删除 1:已删除
     **/
    @TableField("is_deleted")
    private Integer isDeleted;
}
