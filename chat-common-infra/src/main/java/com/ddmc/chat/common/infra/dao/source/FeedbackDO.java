package com.ddmc.chat.common.infra.dao.source;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
@TableName("message_feedback")
public class FeedbackDO extends CommonFieldDO {


    @TableField("uid")
    private String uid;

    @TableField("session_id")
    private Long sessionId;

    @TableField("message_id")
    private Long messageId;

    /**
     * 1:赞  2:踩
     */
    @TableField("feedback")
    private Integer feedback;

    /**
     * 详细点评
     */
    @TableField("reason")
    private String reason;
}
