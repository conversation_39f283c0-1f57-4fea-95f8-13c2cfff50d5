package com.ddmc.chat.common.infra.proxy.sse;

import com.ddmc.chat.common.domain.entity.MessageContext;
import com.ddmc.chat.common.util.MDCUtil;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.chat.common.util.TimeUtil;
import com.ddmc.guide.enhance.json.JsonUtil;
import com.ddmc.guide.enhance.rest.SimpleResponseVo;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * SSE 回调处理器公共实现
 * <AUTHOR>
 */
@Slf4j
public class SSECallbackHandler {

    protected final String methodName;
    protected final Map<String, String> contextMap;
    protected final SseEmitter sseEmitter;
    protected final MessageContext messageContext;
    protected final SSECallbackOperations operations;

    // 监控相关字段
    private final long startTime = System.currentTimeMillis();
    private boolean firstPacketSent = false;
    private long lastPacketTime = 0L;

    public SSECallbackHandler(
        String methodName,
        Map<String, String> contextMap,
        SseEmitter sseEmitter,
        MessageContext messageContext,
        SSECallbackOperations operations) {
        this.methodName = methodName;
        this.contextMap = contextMap;
        this.sseEmitter = sseEmitter;
        this.messageContext = messageContext;
        this.operations = operations;
    }

    /**
     * 模板方法：确保在MDC上下文中执行操作
     */
    private void ensureMDC(Runnable action) {
        MDCUtil.setCopyOfContextMap(contextMap);
        action.run();
    }

    public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
        ensureMDC(() -> {
            log.info("{} onOpen connection, messageContext: {}", methodName, JsonUtil.toJson(messageContext));
            try {
                this.operations.onOpenInternal(eventSource, response);
            } catch (Exception e) {
                handleException(e);
                MonitorUtil.counterOnce("sse_open_exception", methodName);
            }
        });
    }

    public void onEvent(@NotNull EventSource eventSource, String id, String type, @NotNull String data) {
        ensureMDC(() -> {
            long now = System.currentTimeMillis();
            log.info("{} onEvent, messageContext: {}, data: {}", methodName, JsonUtil.toJson(messageContext), data);
            // 首包耗时
            if (!firstPacketSent) {
                firstPacketSent = true;
                long firstPacketCost = TimeUtil.getInternalTime(startTime);
                MonitorUtil.timerOnce("sse_first_packet_cost", firstPacketCost, methodName);
                log.info("{} first packet, cost: {}s ", methodName, firstPacketCost);
                // 初始化 lastPacketTime 为当前时间
                lastPacketTime = now;
            } else {
                // 如果 lastPacketTime > 0，则计算数据包间隔
                if (lastPacketTime > 0) {
                    long packetInterval = TimeUtil.getInternalTime(lastPacketTime);
                    MonitorUtil.timerOnce("sse_packet_interval", packetInterval, methodName);
                    log.info("{} packet interval, cost: {}s ", methodName, packetInterval);
                }
                // 更新 lastPacketTime
                lastPacketTime = now;
            }
            try {
                this.operations.onEventInternal(eventSource, id, type, data);
            } catch (Exception e) {
                handleException(e);
                MonitorUtil.counterOnce("sse_event_exception", methodName);
            }
        });
    }

    public void onClosed(@NotNull EventSource eventSource) {
        ensureMDC(() -> {
            long totalCost = TimeUtil.getInternalTime(startTime);
            log.info("{} onClosed, messageContext: {}, totalCost: {}s", methodName, JsonUtil.toJson(messageContext), totalCost);
            MonitorUtil.timerOnce("sse_total_cost", totalCost, methodName);
            try {
                operations.onClosedInternal(eventSource);
            } catch (Exception e) {
                handleException(e);
                MonitorUtil.counterOnce("sse_close_exception", methodName);
            }
        });
    }

    public void onFailure(@NotNull EventSource eventSource, Throwable t, Response response) {
        ensureMDC(() -> {
            long totalCost = TimeUtil.getInternalTime(startTime);
            log.error("{} onFailure, messageContext: {}, totalCost: {}s", methodName, JsonUtil.toJson(messageContext), totalCost, t);
            MonitorUtil.timerOnce("sse_total_cost", totalCost, methodName);
            MonitorUtil.counterOnce("sse_failure", methodName);
            try {
                operations.onFailureInternal(eventSource, t, response);
            } catch (Exception e) {
                handleException(t);
                MonitorUtil.counterOnce("sse_failure_exception", methodName);
            }
        });
    }

    protected void handleException(Throwable t) {
        try {
            sseEmitter.send(SimpleResponseVo.fail(t.getMessage()));
        } catch (Exception ex) {
            // log
            log.warn("SSECallbackHandler handleException", t);
        }
    }
}
