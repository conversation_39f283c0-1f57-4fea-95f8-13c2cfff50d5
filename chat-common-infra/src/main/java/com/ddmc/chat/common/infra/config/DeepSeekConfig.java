package com.ddmc.chat.common.infra.config;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeepSeekConfig {

    // deepSeek 用户未设置时默认状态 1-打开；0-关闭
    private Integer reasonDefaultSwitch;

    // deepSeek 模型名称映射
    private Map<Integer, String> modelNameMap;

    // deepSeek 返回的首页展示商品数量
    private Integer productHomePageShowTotalNumber;

    // deepSeek 返回的商品总数量
    private Integer productTotalNumber;

    // tips 提示信息
    private String tips;

    // 必须走新接口的 sourceType 列表
    private List<Integer> sourceTypeList;

    // 主搜综合下的模型名称
    private String dsCompositeModelName;

}
