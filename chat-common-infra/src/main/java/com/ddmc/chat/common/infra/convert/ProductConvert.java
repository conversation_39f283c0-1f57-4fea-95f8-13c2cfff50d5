package com.ddmc.chat.common.infra.convert;

import com.ddmc.chat.common.domain.vo.ProductInfoVO;
import com.ddmc.summary.client.dto.ProductInfoOldBO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Mapper(componentModel = "spring",unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,imports = {
        LocalDateTime.class,
        Date.class, ZoneId.class})
public interface ProductConvert {


    ProductInfoVO productConverter(ProductInfoOldBO oldBO);


}
