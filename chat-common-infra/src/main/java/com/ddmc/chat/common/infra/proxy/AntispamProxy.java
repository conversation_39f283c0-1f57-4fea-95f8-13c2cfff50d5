package com.ddmc.chat.common.infra.proxy;

import cn.hutool.core.util.BooleanUtil;
import com.ddmc.antispam.ImgCheckService;
import com.ddmc.antispam.TextCheckService;
import com.ddmc.antispam.common.response.GenericResponse;
import com.ddmc.antispam.dto.CheckResultDTO;
import com.ddmc.antispam.dto.ImgCheckDTO;
import com.ddmc.antispam.dto.TextCheckDTO;
import com.ddmc.chat.common.constant.Constans;
import com.ddmc.chat.common.domain.dto.req.chat.ChatQuestionReq;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.proxy.dto.TextCheckReason;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.guide.enhance.json.JsonUtil;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/9/19 4:57 PM
 * @Description:  风控级别为 0：通过 1：嫌疑  2：拒绝   1和的时候还会返回关键词和原因  见https://cfl.corp.100.me/pages/viewpage.action?pageId=88207907
 * 产品需求为拦截【拒接】和【嫌疑】中【涉政词汇】原因的问题
 */
@Component
@Slf4j
public class AntispamProxy {


    /**
     * 风控 appKey
     */
    @Value("${antispam.chat.api.appKey:c6477659eda043159ce68b86048dba35}")
    private String appKey;

    @Resource
    private TextCheckService textCheckService;

    @Resource
    private ImgCheckService imgCheckService;

    @Autowired
    private GlobalApolloConfig globalApolloConfig;

    private static final String METRICS_ANTISPAM_IMAGE_TYPE = "antispamImage";
    private static final String METRICS_ANTISPAM_TEXT_TYPE = "antispamText";

    public List<String> imageCheck(ImgCheckDTO imgCheckDTO) {
        try {

            if (isFallbackOnByImage()) {
                log.warn("触发手动降级: AntispamProxy.imageCheck");
                MonitorUtil.counterOnce("triggerDegrade", "antispamProxy_imageCheck", "-1", "imgCheckService.check");
                return Lists.newArrayList();
            }

            // 图片检测对接文档：https://cfl.corp.100.me/pages/viewpage.action?pageId=88207907
            GenericResponse<CheckResultDTO> response = imgCheckService.check(imgCheckDTO, appKey);
            if (Objects.isNull(response)) {
                log.error("imgCheckService.check response is null, urls: {}", imgCheckDTO.getImgUrls());
                return Lists.newArrayList();
            }
            if (BooleanUtil.isFalse(response.isSuccess()) || Objects.isNull(response.getData())) {
                log.error("imageCheck error,  msg {} data:{}", response.getMessage(), JsonUtil.toJson(response.getData()));
                return Lists.newArrayList();
            }

            // level 检测结果 0:通过 1:嫌疑 2:拒绝
            if (response.getData().getLevel() == 0) {
                MonitorUtil.counterOnce(METRICS_ANTISPAM_IMAGE_TYPE, "pass");
                return Lists.newArrayList();
            }

            List<String> checkReasonList = JsonUtil.toObjList(JsonUtil.toJson(response.getData().getLabels()), String.class);
            if (response.getData().getLevel() == 1) {
                checkReasonList = checkReasonList.stream().filter(item -> Objects.equals(item, "涉政词汇")
                    || Objects.equals(item, "涉黄词汇")).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(checkReasonList)) {
                    MonitorUtil.counterOnce(METRICS_ANTISPAM_IMAGE_TYPE, "risk");
                } else {
                    MonitorUtil.counterOnce(METRICS_ANTISPAM_IMAGE_TYPE, "pass");
                }
                return checkReasonList;
            }

            if (response.getData().getLevel() == 2) {
                MonitorUtil.counterOnce(METRICS_ANTISPAM_IMAGE_TYPE, "risk");
                return checkReasonList;
            }
        } catch (Exception e) {
            log.error("textCheckService.check.error",e);
        }
        return Lists.newArrayList();
    }

    public ImgCheckDTO buildImgCheckDTO(ChatQuestionReq chatQuestionReq) {
        ImgCheckDTO imgCheckDTO = new ImgCheckDTO();
        imgCheckDTO.setAppId("product_assistant");
        imgCheckDTO.setServerType(1);
        imgCheckDTO.setDataId(System.currentTimeMillis() + "");
        imgCheckDTO.setSceneCode("product_assistant");
        imgCheckDTO.setImgUrls(chatQuestionReq.getImageUrls());
        imgCheckDTO.setUser(chatQuestionReq.getUid());
        return imgCheckDTO;
    }

    public List<TextCheckReason> textCheck(TextCheckDTO checkDTO) {

        if (isFallbackOn()) {
            log.warn("触发手动降级: AntispamProxy.textCheck");
            MonitorUtil.counterOnce("triggerDegrade", "antispamProxy_textCheck", "-1", "textCheckService.check");
            return Lists.newArrayList();
        }

        GenericResponse<CheckResultDTO> response = null;
        try {
            response = textCheckService.check(checkDTO, appKey);
            if (!response.isSuccess() || Objects.isNull(response.getData())) {
                log.error("textCheck-风控服务敏感词过滤异常 msg {} data:{}", response.getMessage(),
                    JsonUtil.toJson(response.getData()));
                return null;
            }
            //level 敏感字检测结果	0:通过 1:嫌疑 2:拒绝 //
            if (response.getData().getLevel() == 0) {
                return null;
            } else {
                List<TextCheckReason> checkReasonList = JsonUtil.toObjList(JsonUtil.toJson(response.getData().getLabels()),
                    TextCheckReason.class);
                if (response.getData().getLevel() == 1) {
                    //{
                    //    "dataId": "a491c6a54afe4ced934bb4485a5dbd4d",
                    //    "level": 2,
                    //    "labels": [
                    //        "违禁词汇",
                    //        "涉恐词汇",
                    //        "涉黄词汇",
                    //        "涉政词汇",
                    //        "性感低俗"
                    //    ]
                    //}
                    return checkReasonList.stream()
                        .filter(item -> Objects.equals(item.getLabel(), "涉政词汇")).collect(Collectors.toList());
                } else if (response.getData().getLevel() == 2) {
                    return checkReasonList;
                }
            }
        } catch (Exception e) {
            log.error("textCheckService.check.error",e);
        }
        return null;
    }

    private boolean isFallbackOn() {
        return Objects.equals(globalApolloConfig.getAntispamDegradeSwitch(), Constans.ONE);
    }

    private boolean isFallbackOnByImage() {
        return Objects.equals(globalApolloConfig.getAntispamImageCheckDegradeSwitch(), Constans.ONE);
    }

}
