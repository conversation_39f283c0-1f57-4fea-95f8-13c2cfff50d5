package com.ddmc.chat.common.infra.utils;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.semver4j.Semver;
import org.springframework.stereotype.Component;

/**
 * 功能控制工具类
 * 基于appClientId、api_version、channel三方字段，通过Apollo配置进行逻辑控制
 * 使用说明：
 * 1. 默认所有功能都是开启的
 * 2. 只有在Apollo中明确配置了规则且匹配到规则时，才会关闭功能
 * 3. 配置格式为JSON，key为功能名称，value为规则列表
 * 4. 规则中的字段都是可选的，未配置的字段表示不限制
 */
@Slf4j
@Component
public class FeatureControlUtil {

    /**
     * Apollo配置示例：
     * [
     *    {"appClientId":"1","apiVersion":"11.41.0","channels":["appstore"]},
     *    {"appClientId":"2","apiVersion":"11.41.*","channels":["googleplay","huawei"]},
     *    {"appClientId":"3","apiVersion":">=11.41.0","channels":["googleplay","huawei"]}
     * ]
     */
    @ApolloJsonValue("${feature.ai.control.blacklist:[]}")
    private List<FeatureControlRuleDTO> blacklist;

    /**
     * 检查功能是否开启
     * 默认返回true，只有在明确配置了规则且匹配到规则时才会返回false
     * 
     * @param appClientId 应用ID 必传
     * @param apiVersion 版本号 必传
     * @param channel 应用来源渠道 必传
     * @return 是否开启： true 开启功能， false 关闭功能
     */
    public boolean isFeatureEnabled(String appClientId, String apiVersion, String channel) {
        // 参数校验
        if (StringUtils.isBlank(appClientId) || StringUtils.isBlank(apiVersion) || StringUtils.isBlank(channel)) {
            log.debug("FeatureControlUtil params is blank, appClientId:{} apiVersion:{} channel:{}",appClientId ,apiVersion, channel);
            return true; // featureKey为空时默认开启
        }

        try {
            // 获取规则列表
            if (CollectionUtils.isEmpty(blacklist)) {
                log.debug("FeatureControlUtil blacklist is isEmpty}");
                return true; // 没有配置规则时默认开启
            }
            
            // 检查是否有匹配的规则
            boolean hasMatch = blacklist.stream().anyMatch(rule ->
                rule.matches(appClientId, apiVersion, channel)
            );
            
            if (hasMatch) {
                log.debug("FeatureControlUtil found matching rule, appClientId: {}, apiVersion: {}, channel: {}",
                     appClientId, apiVersion, channel);
                return false; // 匹配到规则时关闭功能
            }
            
            return true; // 没有匹配到规则时开启功能
        } catch (Exception e) {
            log.error("FeatureControlUtil checkFeatureEnabled Error, appClientId: {}, apiVersion: {}, channel: {}",
                    appClientId, apiVersion, channel, e);
            return true; // 发生异常时默认开启
        }
    }

    @Getter
    @Setter
    public static class FeatureControlRuleDTO {

        /**
         * 应用类型ID
         */
        private String appClientId;

        /**
         * 版本列表
         * 如："11.41.0" || "11.41.*" || ">=11.41.0"
         */
        private String apiVersion;

        /**
         * 渠道列表
         */
        private List<String> channels;

        /**
         * 是否匹配请求
         * 规则说明：三个字段必须有值才能匹配到
         * 1. 如果参数字段为null或空串，则无法匹配
         * 2. 如果配置字段包含"*"，表示匹配任意值
         * 3. 如果字段有具体值，则必须完全匹配
         * return true 命中过滤规则 false: 未命中过滤规则
         */
        public boolean matches(String appClientId, String apiVersion, String channel) {
            // 检查appClientId
            boolean appMatch = false;
            if (StringUtils.isNotBlank(this.appClientId) && StringUtils.isNotBlank(appClientId)) {
                appMatch = StringUtils.equals(this.appClientId, "*") ||  StringUtils.equals(this.appClientId, appClientId);
            }

            // 检查versions
            boolean versionMatch = false;
            if (StringUtils.isNotBlank(this.apiVersion) && StringUtils.isNotBlank(apiVersion)) {
                try {
                    Semver customerApiVersion = new Semver(apiVersion);
                    versionMatch = StringUtils.equals(this.apiVersion, "*") || customerApiVersion.satisfies(this.apiVersion);
                } catch (Exception e) {
                    log.error("FeatureControlRuleDTO matches versionMatch is error, configVersions:{} apiVersion:{}", this.apiVersion, apiVersion, e);
                }
            }

            // 检查channels
            boolean channelMatch = false;
            if (CollectionUtils.isNotEmpty(this.channels) && StringUtils.isNotBlank(channel)) {
                channelMatch = this.channels.contains("*") ||  this.channels.stream()
                        .filter(StringUtils::isNotBlank)
                        .anyMatch(c -> StringUtils.equals(c.toLowerCase(), channel.toLowerCase()));
            }

            return appMatch && versionMatch && channelMatch;
        }
    }
}