package com.ddmc.chat.common.infra.proxy.sse;

import com.ddmc.chat.common.domain.entity.MessageContext;
import com.ddmc.llm.biz.agent.entity.ProductAssistResultCallback;
import java.util.Map;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * ProductAssistResultCallback 的适配器
 * <AUTHOR>
 */
public abstract class ProductAssistResultCallbackAdapter extends ProductAssistResultCallback implements SSECallbackOperations {
    private final SSECallbackHandler handler;

    protected ProductAssistResultCallbackAdapter(String methodName, Map<String, String> contextMap,
        SseEmitter sseEmitter, MessageContext messageContext) {
        this.handler = new SSECallbackHandler(methodName, contextMap, sseEmitter, messageContext, this);
    }

    @Override
    public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
        handler.onOpen(eventSource, response);
    }

    @Override
    public void onEvent(@NotNull EventSource eventSource, String id, String type, @NotNull String data) {
        handler.onEvent(eventSource, id, type, data);
    }

    @Override
    public void onClosed(@NotNull EventSource eventSource) {
        handler.onClosed(eventSource);
    }

    @Override
    public void onFailure(@NotNull EventSource eventSource, Throwable t, Response response) {
        handler.onFailure(eventSource, t, response);
    }
}
