package com.ddmc.chat.common.infra.proxy;

import com.ddmc.chat.common.domain.dto.req.CommonReq;
import com.ddmc.chat.common.domain.dto.req.ai.AiChatCompletionReq;
import com.ddmc.chat.common.domain.dto.req.ai.AiChatStopReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.StartReq;
import com.ddmc.summary.client.enums.AppClientIdEnum;
import lombok.Data;


/**
 * 聊天会话的上下文的包装类
 */
@Data
public class ChatContext {

    // 基本公共信息
    private String uid;

    private String deviceId;

    private String stationId;

    private String cityNumber;

    private String apiVersion;

    private String appVersion;

    private String nativeVersion;

    private Integer appClientId;

    private String longitude;

    private String latitude;

    private String referPage;

    // 各场景特有信息
    private String productId;

    private String frontCategoryName;

    private String frontCategoryId;

    private String searchKeyword;

    private Integer searchResultCnt;

    private String scene;

    private String cityName;

    private String from;

    public static ChatContext of(String uid, String deviceId, String stationId, String cityNumber, String apiVersion,
                                 String appVersion, String nativeVersion, Integer appClientId, String longitude, String latitude, String referPage) {
        ChatContext chatContext = new ChatContext();
        chatContext.setUid(uid);
        chatContext.setDeviceId(deviceId);
        chatContext.setStationId(stationId);
        chatContext.setCityNumber(cityNumber);
        chatContext.setApiVersion(apiVersion);
        chatContext.setAppVersion(appVersion);
        chatContext.setNativeVersion(nativeVersion);
        chatContext.setAppClientId(appClientId);
        chatContext.setLongitude(longitude);
        chatContext.setLatitude(latitude);
        chatContext.setReferPage(referPage);
        return chatContext;
    }


    public static ChatContext of(StartReq startReq) {
        ChatContext chatContext = of((CommonReq) startReq);

        if (startReq.getProductId() != null) {
            chatContext.setProductId(startReq.getProductId());
        }
        if (startReq.getFrontCategoryName() != null) {
            chatContext.setFrontCategoryName(startReq.getFrontCategoryName());
        }
        if (startReq.getFrontCategoryId() != null) {
            chatContext.setFrontCategoryId(startReq.getFrontCategoryId());
        }
        if (startReq.getSearchKeyword() != null) {
            chatContext.setSearchKeyword(startReq.getSearchKeyword());
        }
        if (startReq.getSearchResultCnt() != null) {
            chatContext.setSearchResultCnt(startReq.getSearchResultCnt());
        }

        return chatContext;
    }

    public static ChatContext of(ChatQuestionReq chatQuestionReq) {
        ChatContext chatContext = of((CommonReq) chatQuestionReq);

        if (chatQuestionReq.getProductId() != null) {
            chatContext.setProductId(chatQuestionReq.getProductId());
        }

        return chatContext;
    }


    private static ChatContext of(CommonReq commonReq) {
        ChatContext chatContext = new ChatContext();
        chatContext.setUid(commonReq.getUid());
        chatContext.setDeviceId(commonReq.getDeviceId());
        chatContext.setStationId(commonReq.getStationId());
        chatContext.setCityNumber(commonReq.getCityNumber());
        chatContext.setApiVersion(commonReq.getApiVersion());
        chatContext.setAppVersion(commonReq.getAppVersion());
        chatContext.setNativeVersion(commonReq.getNativeVersion());
        AppClientIdEnum appClientIdEnum = AppClientIdEnum.getByCode(commonReq.getAppClientId());
        if (appClientIdEnum != null) {
            chatContext.setAppClientId(appClientIdEnum.getCodeInt());
        }
        chatContext.setLongitude(commonReq.getLongitude());
        chatContext.setLatitude(commonReq.getLatitude());
        chatContext.setReferPage(commonReq.getFrom());
        chatContext.setScene(commonReq.getScene());
        chatContext.setCityName(commonReq.getCityName());
        chatContext.setFrom(commonReq.getFrom());
        return chatContext;
    }

    public static ChatContext of(AiChatCompletionReq completionReq) {
        return of((CommonReq) completionReq);
    }

    public static ChatContext of(AiChatStopReq stopReq) {
        ChatContext chatContext = new ChatContext();
        chatContext.setUid(stopReq.getUid());
        return chatContext;
    }

}
