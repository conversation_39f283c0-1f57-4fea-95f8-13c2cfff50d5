package com.ddmc.chat.common.infra.proxy.sse;

import okhttp3.Response;
import okhttp3.sse.EventSource;

/**
 * SSE 回调操作接口
 */
public interface SSECallbackOperations {
    void onOpenInternal(EventSource eventSource, Response response);
    void onEventInternal(EventSource eventSource, String id, String type, String data) throws Exception;
    void onClosedInternal(EventSource eventSource);
    void onFailureInternal(EventSource eventSource, Throwable t, Response response);
}
