package com.ddmc.chat.common.infra.proxy;

import com.ddmc.chat.common.domain.dto.req.ai.AiChatCompletionReq;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatAppendContents;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatChoice;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatData;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatDelta;
import com.ddmc.chat.common.domain.entity.MessageContext;
import com.ddmc.chat.common.domain.vo.AiChatMessageVO;
import com.ddmc.chat.common.infra.handler.LinkHandler;
import com.ddmc.chat.common.infra.handler.MessageBoxHandler;
import com.ddmc.chat.common.infra.proxy.sse.SSEResultCallbackAdapter;
import com.ddmc.chat.common.util.MDCUtil;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.guide.enhance.json.JsonUtil;
import com.ddmc.guide.enhance.rest.SimpleResponseVo;
import com.ddmc.recipe.searchrec.client.AISearchSSEClient;
import com.ddmc.recipe.searchrec.entity.request.ProductRequestVo;
import com.ddmc.searchrec.infra.utils.entity.ResponseBaseVo;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@Service
@Slf4j
public class SearchRecProxy {

    @Autowired
    private AISearchSSEClient aiSearchSseClient;

    @Autowired
    private MessageBoxHandler messageBoxHandler;

    @Autowired
    private LinkHandler linkHandler;

    private static final String METRICS_SEARCH_SSE_EMITTER_TYPE = "aiSearchSseClient";


    /**
     * ② Deepseek搜索Tab，算法新接口，返回搜索词相关的答案。
     * 老接口在{@link NlpAiProxy#streamCall(AiChatCompletionReq, SseEmitter, MessageContext)}中，
     * 待算法实验推全，老接口应该无调用量。
     *
     * @param completionReq
     * @param originSseEmitter
     * @param messageContext
     * @return
     */
    public SseEmitter aiSearch(AiChatCompletionReq completionReq, SseEmitter originSseEmitter,
                               MessageContext messageContext) {
        Map<String, String> copyOfContextMap = MDCUtil.getCopyOfContextMap();
        if (Objects.isNull(completionReq) || Objects.isNull(originSseEmitter)) {
            return null;
        }

        originSseEmitter.onTimeout(() -> MonitorUtil.counterOnce(METRICS_SEARCH_SSE_EMITTER_TYPE, "timeout"));

        ProductRequestVo productRequestVo = messageBoxHandler.buildProductRequestVo(completionReq, messageContext);

        log.info("SearchRecProxy aiSearch req: {}", JsonUtil.toJson(productRequestVo));

        aiSearchSseClient.aiSearch(productRequestVo, new SSEResultCallbackAdapter(
            "deepSeekV2", copyOfContextMap, originSseEmitter, messageContext) {
            @Override
            public void onOpenInternal(@NotNull EventSource eventSource, @NotNull Response response) {}

            @Override
            public void onEventInternal(@NotNull EventSource eventSource, String id, String type, @NotNull String data) {
                try {
                    AiChatData aiData = JsonUtil.toObject(data, AiChatData.class);
                    if (aiData == null) {
                        MonitorUtil.counterOnce(METRICS_SEARCH_SSE_EMITTER_TYPE, "null_data");
                        return;
                    }

                    List<AiChatAppendContents> appendContents = aiData.getAppendContents();

                    if (CollectionUtils.isNotEmpty(appendContents)) {

                        for (int i = 0; i < appendContents.size(); i++) {
                            AiChatAppendContents appendContent = appendContents.get(i);
                            boolean isEnd = i == appendContents.size() - 1;
                            String appendContentType = appendContent.getType();
                            String content = processAiChatContent(appendContent.getContent(), completionReq.getAppClientId());

                            AiChatMessageVO messageVO = null;
                            switch (appendContentType) {
                                case "text":
                                    messageVO = messageBoxHandler.buildTextAiChatMessageVO(isEnd, content, "", appendContent.getQid(), messageContext);
                                    break;
                                case "product":
                                    // 附加内容是商卡，需要特殊处理
                                    messageVO = messageBoxHandler.buildAiProductCardMessageVO(isEnd, appendContent, messageContext, completionReq);
                                    break;
                                default:
                                    MonitorUtil.counterOnce(METRICS_SEARCH_SSE_EMITTER_TYPE, "unknown_type", "-1",
                                            appendContentType);
                                    // 不认识的类型，默认用兜底的文本消息
                                    messageVO = messageBoxHandler.buildTextAiChatMessageVO(isEnd, content, "", appendContent.getQid(), messageContext);
                            }
                            if (messageVO == null) {
                                MonitorUtil.counterOnce(METRICS_SEARCH_SSE_EMITTER_TYPE, "null_message_vo");
                                continue;
                            }
                            messageVO.setAiPurpose(appendContent.getPurpose());
                            originSseEmitter.send(SimpleResponseVo.ok(messageVO));

                        }
                    } else if (CollectionUtils.isNotEmpty(aiData.getChoices())) {

                        List<AiChatChoice> choiceList = aiData.getChoices();
                        if (CollectionUtils.isEmpty(choiceList)) {
                            MonitorUtil.counterOnce(METRICS_SEARCH_SSE_EMITTER_TYPE, "null_choices");
                            return;
                        }

                        for (AiChatChoice choice : choiceList) {
                            String finishReason = choice.getFinishReason();
                            AiChatDelta delta = choice.getDelta();
                            if (delta == null) {
                                MonitorUtil.counterOnce(METRICS_SEARCH_SSE_EMITTER_TYPE, "null_delta");
                                continue;
                            }
                            String content = processAiChatContent(delta.getContent(), completionReq.getAppClientId());
                            // 将消息转换格式并发送前端
                            boolean end = Objects.nonNull(finishReason);
                            AiChatMessageVO messageVO = messageBoxHandler.buildTextAiChatMessageVO(
                                    end, content, delta.getReasoningContent(), aiData.getQid(), messageContext);

                            originSseEmitter.send(SimpleResponseVo.ok(messageVO));
                        }
                    }
                } catch (ClientAbortException caException) {
                    MonitorUtil.counterOnce(METRICS_SEARCH_SSE_EMITTER_TYPE, "client_abort", "-1", caException);
                } catch (IOException e) {
                    try {
                        originSseEmitter.send(SimpleResponseVo.fail(e.getMessage()));
                    } catch (IOException ex) {
                        // do nothing
                    }
                    MonitorUtil.counterOnce(METRICS_SEARCH_SSE_EMITTER_TYPE, "sendRequest.fail", "-1", e);
                    log.error("SearchRecProxy send event error", e);
                }
            }

            /*
             * 该方法会在回答结束和 stop 时调用，后者stop需要注意，会在已经发送到缓冲区的消息全部通用onEvent回调后再关闭sse连接
             */
            @Override
            public void onClosedInternal(@NotNull EventSource eventSource) {
                try {
                    String qid = completionReq.getQid();
                    // 发送一个带结束标记的消息包给前端
                    AiChatMessageVO messageVO = messageBoxHandler.buildTextAiChatMessageVO(true, "", "", qid, messageContext);
                    originSseEmitter.send(SimpleResponseVo.ok(messageVO));
                } catch (Exception ex) {
                    // do nothing
                    log.info("SearchRecProxy onClosed originSseEmitter.send error, message: {}", ex.getMessage());
                    MonitorUtil.counterOnce(METRICS_SEARCH_SSE_EMITTER_TYPE, "onClosed_SendRequest.fail");
                }
                try {
                    originSseEmitter.complete();
                } catch (Exception e) {
                    log.error("SearchRecProxy Error closing SSE connection", e);
                }
            }

            @Override
            public void onFailureInternal(@NotNull EventSource eventSource, Throwable t, Response response) {
                try {
                    originSseEmitter.send(SimpleResponseVo.fail(JsonUtil.toJson(t.getCause())));
                } catch (Exception e) {
                    // do nothing
                }
                originSseEmitter.completeWithError(t);
            }
        });
        return originSseEmitter;
    }

    /**
     * for deepseek， 根据 content 不同标识拼接跳转链接
     *
     * @param content     原 content 内容
     * @param appClientId app client id
     * @return 拼接跳转链接 + 样式后的 content 内容
     */
    public String processAiChatContent(String content, String appClientId) {
        if (StringUtils.isBlank(content) || StringUtils.isBlank(appClientId)) {
            return content;
        }
        return linkHandler.convert(content, appClientId);
    }

    public Boolean aiStop(String messageId) {
        try {
            if (StringUtils.isBlank(messageId)) {
                return false;
            }
            ProductRequestVo productRequestVo = new ProductRequestVo();
            productRequestVo.setMsgId(messageId);
            ResponseBaseVo<String> response = aiSearchSseClient.aiStop(productRequestVo);
            return response.isSuccess();
        } catch (Exception e) {
            log.error("aiSearchSseClient.stopStream.exception", e);
            return false;
        }
    }

}
