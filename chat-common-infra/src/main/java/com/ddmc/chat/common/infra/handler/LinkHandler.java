package com.ddmc.chat.common.infra.handler;

import com.ddmc.chat.common.constant.Constans;
import com.ddmc.chat.common.domain.entity.AiChatOpConfig;
import com.ddmc.chat.common.domain.entity.AiChatOpMdTemplate;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.util.MonitorUtil;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class LinkHandler {

    @Autowired
    private GlobalApolloConfig globalApolloConfig;

    private static final String METRICS_TYPE_NAME = "linkHandler";

    private static final String TYPE_QUERY = "query";

    // 带分组的正则表达式
    private static final Pattern WIKI_PATTERN = Pattern.compile(
        "\\[\\[(.*?)\\|(.*?)\\]\\]",
        Pattern.CASE_INSENSITIVE
    );

    public String convert(String input, String appClientId) {

        if (StringUtils.isBlank(input) || StringUtils.isBlank(appClientId)) {
            return input;
        }

        try {
            Matcher matcher = WIKI_PATTERN.matcher(input);
            StringBuffer sb = new StringBuffer();

            while (matcher.find()) {
                // 匹配 | 前面的部分
                String displayText = matcher.group(1);
                // 匹配 | 后面的部分
                String attributesStr = matcher.group(2);

                if (isAiChatOpFallbackOn()) {
                    matcher.appendReplacement(sb, Matcher.quoteReplacement(displayText));
                    log.warn("触发手动降级: LinkHandler.convert");
                    MonitorUtil.counterOnce("triggerDegrade", "LinkHandler_convert");
                    continue;
                }

                Map<String, String> attributesMap = getAttributesMap(attributesStr);
                String type = attributesMap.get("type");
                String id = attributesMap.get("id");

                // 获取场景配置
                AiChatOpConfig opConfig = globalApolloConfig.getAiChatOpConfig(type);
                if (opConfig == null || MapUtils.isEmpty(opConfig.getActionTemplates())) {
                    matcher.appendReplacement(sb, matcher.group(0));
                    MonitorUtil.counterOnce(METRICS_TYPE_NAME, "type_config_null");
                    continue;
                }

                // 获取具体动作模板（优先使用匹配的动作，否则使用默认动作）
                String action = attributesMap.getOrDefault("action", opConfig.getDefaultAction());
                AiChatOpMdTemplate template = opConfig.getActionTemplates().get(action);

                if (template == null || StringUtils.isBlank(template.getAction()) || StringUtils.isBlank(template.getMdTemplate())) {
                    matcher.appendReplacement(sb, matcher.group(0));
                    MonitorUtil.counterOnce(METRICS_TYPE_NAME, "template_null");
                    continue;
                }

                // 构建目标 URL
                String url = template.getAppClientUrlMap().get(appClientId);
                String targetUrl = buildUrl(type, displayText, url, id);

                // 替换模板
                String replacement = String.format(
                    template.getMdTemplate(),
                    displayText,
                    template.getAction(),
                    encodeUrl(targetUrl),
                    type
                );

                matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            }
            matcher.appendTail(sb);
            return sb.toString();
        } catch (Exception e) {
            // 记录异常并返回原始输入
            log.error("LinkHandler convert error", e);
            MonitorUtil.counterOnce(METRICS_TYPE_NAME, "convert_error");
            return input;
        }
    }

    public Map<String, String> getAttributesMap(String attributesStr) {
        Map<String, String> attributesMap = new HashMap<>();
        if (StringUtils.isBlank(attributesStr)) {
            return attributesMap;
        }

        String[] attributes = attributesStr.split(",");
        for (String attribute : attributes) {
            if (StringUtils.isBlank(attribute)) {
                continue;
            }
            // 查找等号位置
            int eqIndex = attribute.indexOf('=');
            if (eqIndex == -1) {
                continue;
            }
            // 提取键和值，并去除两端的空白字符
            String key = attribute.substring(0, eqIndex).trim();
            String value = attribute.substring(eqIndex + 1).trim();
            // 校验键是否为空
            if (StringUtils.isBlank(key)) {
                continue;
            }
            attributesMap.put(key, value);
        }
        return attributesMap;
    }

    public static String buildUrl(String type, String displayText, String url, String id) {
        if (StringUtils.isBlank(type)
            || StringUtils.isBlank(url)
            || StringUtils.isBlank(displayText)) {
            return url;
        }
        if (TYPE_QUERY.equals(type)) {
            // 搜索词特殊处理
            return String.format(url, displayText);
        }
        return String.format(url, id);
    }

    /**
     * 实现 URL 转义
     *
     * @param url 需要转义 的 url
     * @return 转义后的 url
     */
    public String encodeUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return url;
        }
        try {
            return URLEncoder.encode(url, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("LinkHandler encode Url error, url: {}", url, e);
            MonitorUtil.counterOnce(METRICS_TYPE_NAME, "encode_url_error");
        }
        return url;
    }

    public boolean isAiChatOpFallbackOn() {
        return Objects.equals(globalApolloConfig.getAiChatOpDegradeSwitch(), Constans.ONE);
    }
}
