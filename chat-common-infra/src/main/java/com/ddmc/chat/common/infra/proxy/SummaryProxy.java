package com.ddmc.chat.common.infra.proxy;

import static com.ddmc.chat.common.domain.Constants.MSG_CARD_PAGE_ID_RECOMMENDATION;

import com.ddmc.chat.common.constant.Constans;
import com.ddmc.chat.common.domain.vo.ProductInfoVO;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.convert.ProductConvert;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.guide.enhance.json.JsonUtil;
import com.ddmc.summary.client.SummaryClient;
import com.ddmc.summary.client.dto.PageOldProductInfoDTO;
import com.ddmc.summary.client.dto.PageProductIdDTO;
import com.ddmc.summary.client.dto.SummaryBaseResponse;
import com.ddmc.summary.client.dto.request.ProductListRequest;
import com.ddmc.summary.client.enums.AppClientIdEnum;
import com.ddmc.summary.client.enums.ProductMergeSourceEnum;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SummaryProxy {

    @Autowired
    private SummaryClient summaryClient;

    @Autowired
    private ProductConvert productConvert;

    @Autowired
    private GlobalApolloConfig globalApolloConfig;


    public List<PageOldProductInfoDTO> getProductList(ProductListRequest productListRequest) {
        if (isFallbackOn()) {
            log.warn("触发手动降级: SummaryProxy.getProductList");
            MonitorUtil.counterOnce("triggerDegrade", "SummaryProxy_getProductList", "-1", "summaryClient.pageProductList");
            return Lists.newArrayList();
        }

        if (log.isDebugEnabled()) {
            log.debug("summary.pageProductList.request:{}", JsonUtil.toJson(productListRequest));
        }

        try {
            SummaryBaseResponse<List<PageOldProductInfoDTO>> summaryBaseResponse = summaryClient.pageProductList(productListRequest);
            if (Objects.isNull(summaryBaseResponse) || !summaryBaseResponse.isSuccess()) {
                return Lists.newArrayList();
            }

            if (log.isDebugEnabled()) {
                log.debug("summary.pageProductList.response:{}", JsonUtil.toJson(summaryBaseResponse));
            }

            return summaryBaseResponse.getData();
        } catch (Exception e) {
            log.error("invoke summary.pageProductList() error", e);
        }

        return Lists.newArrayList();
    }

    public ProductListRequest generateSummaryRequest(ChatContext chatContext, String pageId, List<String> productIds) {
        ProductListRequest summaryReq = new ProductListRequest();
        summaryReq.setUId(chatContext.getUid());
        summaryReq.setStationId(chatContext.getStationId());
        summaryReq.setCityNumber(chatContext.getCityNumber());
        summaryReq.setApiVersion(chatContext.getApiVersion());
        summaryReq.setAppClient(AppClientIdEnum.getByCode(String.valueOf(chatContext.getAppClientId())));
        summaryReq.setBreakShelf(true);
        summaryReq.setMustShowVip(true);
        summaryReq.setLongitude(chatContext.getLongitude());
        summaryReq.setLatitude(chatContext.getLatitude());
        summaryReq.setPageProductIdDTOS(Lists.newArrayList(new PageProductIdDTO(pageId, productIds)));
        summaryReq.setProductMergeType(ProductMergeSourceEnum.PRODUCT_DETAIL);
        return summaryReq;
    }


    public List<ProductInfoVO> getProductInfoCardByProductIds(ChatContext chatContext, String pageId, List<String> productIds) {
        // generate summary request
        ProductListRequest summaryReq = generateSummaryRequest(chatContext, pageId, productIds);

        // invoke summary
        List<PageOldProductInfoDTO> summaryRes = getProductList(summaryReq);

        // convert response
        return filterAndConvertProducts(summaryRes, pageId);
    }

    private List<ProductInfoVO> filterAndConvertProducts(List<PageOldProductInfoDTO> summaryRes, String pageId) {
        if (CollectionUtils.isEmpty(summaryRes)
            || CollectionUtils.isEmpty(summaryRes.get(0).getProductInfoBOS())) {
            return Collections.emptyList();
        }
        return summaryRes.get(0).getProductInfoBOS().stream()
            .filter(productInfoOldBO -> {
                    if (productInfoOldBO != null) {
                        // 过滤掉赠品
                        if (productInfoOldBO.getIsGift() == 1) {
                            return false;
                        }
                        // 根据 pageId 过滤掉库存为 0 的商品
                        if (MSG_CARD_PAGE_ID_RECOMMENDATION.equals(pageId)) {
                            return productInfoOldBO.getStockNumber() > 0;
                        }
                    }
                    return true;
                }
            )
            .map(productConvert::productConverter)
            .collect(Collectors.toList());
    }




    private boolean isFallbackOn() {
        return Objects.equals(globalApolloConfig.getSummarySwitch(), Constans.ONE);
    }

}
