package com.ddmc.chat.common.infra.handler;

import static com.ddmc.chat.common.constant.CacheKeyConstants.getMessageContextKey;
import static com.ddmc.chat.common.constant.CacheKeyConstants.getMessageExtraKey;
import static com.ddmc.chat.common.constant.CacheKeyConstants.getSessionCacheKey;
import static com.ddmc.chat.common.constant.CacheKeyConstants.getSessionSegmentCacheKey;
import static com.ddmc.chat.common.domain.Constants.COMPLETIONS_HOME_PAGE_SCENE;
import static com.ddmc.chat.common.domain.Constants.LOVE_QUALITY_TAB_NAME;
import static com.ddmc.chat.common.domain.Constants.LOVE_QUALITY_TEXT;
import static com.ddmc.chat.common.domain.Constants.MSG_APPEND_CONTENT_PURPOSE_ARRIVAL_ALERT;
import static com.ddmc.chat.common.domain.Constants.MSG_APPEND_CONTENT_PURPOSE_CUSTOM_SERVICE;
import static com.ddmc.chat.common.domain.Constants.MSG_CARD_PAGE_ID_RECOMMENDATION;
import static com.ddmc.chat.common.domain.Constants.MSG_CARD_PAGE_ID_SEARCH_RESULT_PAGE;
import static com.ddmc.chat.common.domain.Constants.MSG_CARD_PAGE_ID_TITLE;
import static com.ddmc.chat.common.domain.Constants.MSG_SENDER_ASSISTANT;
import static com.ddmc.chat.common.domain.Constants.MSG_SENDER_SYSTEM;
import static com.ddmc.chat.common.domain.Constants.MSG_SENDER_USER;
import static com.ddmc.chat.common.domain.Constants.MSG_STATUS_COMMIT;
import static com.ddmc.chat.common.domain.Constants.MSG_STATUS_UNCOMMIT;
import static com.ddmc.chat.common.domain.Constants.MSG_STATUS_UNKNOWN;
import static com.ddmc.chat.common.domain.Constants.MSG_TYPE_TEXT;
import static com.ddmc.chat.common.domain.Constants.REQ_SOURCE_DXD_NEW;
import static com.ddmc.chat.common.domain.Constants.TRAIT_KEY;
import static com.ddmc.chat.common.domain.Constants.TRAIT_TYPE;

import com.ddmc.chat.common.domain.convert.MessageConvert;
import com.ddmc.chat.common.domain.dto.req.ai.AiChatCompletionReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatHistoryReq;
import com.ddmc.chat.common.domain.dto.req.chat.ChatQuestionReq;
import com.ddmc.chat.common.domain.dto.req.chat.StartReq;
import com.ddmc.chat.common.domain.dto.res.sse.AppendContents;
import com.ddmc.chat.common.domain.dto.res.sse.chat.AiChatAppendContents;
import com.ddmc.chat.common.domain.dto.res.sse.chat.ProductItem;
import com.ddmc.chat.common.domain.entity.AiChatNlpParam;
import com.ddmc.chat.common.domain.entity.MessageContext;
import com.ddmc.chat.common.domain.vo.AiChatMessageVO;
import com.ddmc.chat.common.domain.vo.AiParamVO;
import com.ddmc.chat.common.domain.vo.DailyTipsVO;
import com.ddmc.chat.common.domain.vo.MessageBoxVO;
import com.ddmc.chat.common.domain.vo.MessageVO;
import com.ddmc.chat.common.domain.vo.MultimodalContentVO;
import com.ddmc.chat.common.domain.vo.ProductInfoVO;
import com.ddmc.chat.common.enums.DsSceneEnum;
import com.ddmc.chat.common.enums.MessageTypeEnum;
import com.ddmc.chat.common.infra.config.GlobalApolloConfig;
import com.ddmc.chat.common.infra.dao.MessageBoxDAO;
import com.ddmc.chat.common.infra.proxy.ChatContext;
import com.ddmc.chat.common.infra.proxy.SummaryProxy;
import com.ddmc.chat.common.infra.proxy.dto.TextCheckReason;
import com.ddmc.chat.common.mapper.DO.MessageBoxDO;
import com.ddmc.chat.common.mapper.DO.TopicMessageDO;
import com.ddmc.chat.common.util.MonitorUtil;
import com.ddmc.chat.common.util.MsgIdGenerator;
import com.ddmc.chat.common.util.OUIDGenerator;
import com.ddmc.guide.enhance.json.JsonUtil;
import com.ddmc.guide.enhance.redis.RedisClient;
import com.ddmc.llm.biz.agent.entity.LLMChatMessage;
import com.ddmc.llm.biz.agent.entity.ProductAssistRequestVo;
import com.ddmc.recipe.searchrec.entity.request.CommonParam;
import com.ddmc.recipe.searchrec.entity.request.ProductRequestVo;
import com.ddmc.summary.client.dto.PageOldProductInfoDTO;
import com.ddmc.summary.client.dto.ProductInfoOldBO;
import com.ddmc.summary.client.dto.request.ProductListRequest;
import com.ddmc.utils.date.DateUtils;
import com.google.common.collect.Lists;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @Date 2024/9/12 2:23 PM
 * @Description:
 */
@Service
@Slf4j
public class MessageBoxHandler {

    @Resource(name = "transformersRedisClient")
    private RedisClient transformersRedisClient;

    @Autowired
    private MessageBoxDAO messageBoxDAO;

    @Autowired
    private MessageConvert messageConvert;

    @Autowired
    private SummaryProxy summaryProxy;

    @Autowired
    private GlobalApolloConfig globalApolloConfig;

    @Autowired
    private DeepSeekHandler deepSeekHandler;


    /**
     * 落库一个未提交消息，在close的时候将消息提交
     */
    public MessageBoxDO saveUnCommitMessage(String uid, String stationId, String cityCode, String correlatePid,
        MessageContext messageContext) {
        MessageBoxDO messageBoxDO = new MessageBoxDO();
        messageBoxDO.setSessionId(messageContext.getSessionId());
        messageBoxDO.setQuestId(messageContext.getQuestId());
        messageBoxDO.setMessageId(messageContext.getMessageId());
        messageBoxDO.setOwner(uid);
        messageBoxDO.setSender(MSG_SENDER_ASSISTANT);
        messageBoxDO.setStationId(stationId);
        messageBoxDO.setCityCode(cityCode);
        messageBoxDO.setContent("");
        // 首次写入的，暂时没有内容的占位消息，明确设置状态
        messageBoxDO.setCommitStatus(MSG_STATUS_UNCOMMIT);
        if (correlatePid != null) {
            messageBoxDO.setCorrelatePid(correlatePid);
        }

        // 这里是onOpen()方法的回调，所以一定是最先被调用的场景。先写redis最多保留七天，再写数据库
        String messageKey = getMessageContextKey(messageContext.getMessageId());
        transformersRedisClient.setStr(messageKey, "", Duration.ofDays(globalApolloConfig.getMessageTempMaxExpireDay()));

        // 构建 topic message 映射关系
        TopicMessageDO topicMessageDO = new TopicMessageDO();
        topicMessageDO.setUserId(messageContext.getUid());
        topicMessageDO.setMessageId(messageBoxDO.getMessageId());
        topicMessageDO.setTopicId(messageContext.getTopicId());
        // 统一执行事务，写入数据库
        messageBoxDAO.insertMessageAndTopic(messageBoxDO, topicMessageDO);
        return messageBoxDO;
    }


    /**
     * 以messageId为key，将新收到的消息文本，持续追加到Redis中。
     * 在持续的回答过程中，这个方法会被调用多次，每次都会将新的文本追加到原有文本的后面
     *
     */
    public void appendMessageContent(Long messageId, String msgContext) {
        String messageKey = getMessageContextKey(messageId);
        if (transformersRedisClient.exist(messageKey)) {   //考虑到如果key不存在那么append的会写入一个不过期的key，还是判断一下key是否存在
            transformersRedisClient.appendStr(messageKey, msgContext);
        }
    }

    /**
     * 检查构造完成的消息是否有额外商卡信息，有则记录到Redis中
     * 在持续的回答过程中，只有最后一次调用这个方法，将最终的卡片信息写入Redis
     *
     */
    public void appendMessageExtra(MessageVO messageVO) {
        if (messageVO != null && messageVO.getRecommendProducts() != null && !messageVO.getRecommendProducts().isEmpty()) {
            String messageExtraKey = getMessageExtraKey(messageVO.getMessageId());
            String messageExtraValue = JsonUtil.toJson(messageVO.getRecommendProducts());
            transformersRedisClient.setStr(messageExtraKey, messageExtraValue, Duration.ofDays(globalApolloConfig.getMessageTempMaxExpireDay()));
        }
    }


    /**
     * 将用户输入的语音文件fileKey更新到对应的messageId字段里
     */
    public boolean updateMessageAudioFileKey(String uid, Long messageId, String audioFileKey) {
        return messageBoxDAO.updateMessageWithAudioFileKey(uid, messageId, audioFileKey);
    }

    /**
     * 用户侧发的消息，或者是命中风控的提示消息，
     * 在上一步完成消息的构造后，都是走这个方法，将消息直接落库。
     */
    public boolean saveDirectMessage(MessageVO messageVO, ChatQuestionReq chatQuestionReq) {
        MessageBoxDO messageBoxDO = messageConvert.messageVO2DO(messageVO);
        messageBoxDO.setOwner(chatQuestionReq.getUid());
        messageBoxDO.setCommitStatus(MSG_STATUS_COMMIT);
        if (chatQuestionReq.getProductId() != null) {
            messageBoxDO.setCorrelatePid(chatQuestionReq.getProductId());
        }
        messageBoxDO.setCityCode(chatQuestionReq.getCityNumber());
        messageBoxDO.setStationId(chatQuestionReq.getStationId());
        // 构建话题消息关联关系
        TopicMessageDO topicMessageDO = new TopicMessageDO();
        topicMessageDO.setUserId(messageBoxDO.getOwner());
        topicMessageDO.setMessageId(messageBoxDO.getMessageId());
        topicMessageDO.setTopicId(chatQuestionReq.getTopicId());
        return messageBoxDAO.insertMessageAndTopic(messageBoxDO, topicMessageDO);
    }

    /**
     * 从redis读出完整消息，并更新数据库状态。
     * 直接保存用户发送的消息 与算法沟通，保证onClosed或onFailure 能肯定回调仅有一次：正常结束或者主动关闭就onCLose，出现异常就onFailure；
     * 若服务重启，两个回调都不会发生，所以另外起一个job查询数据库unCommitted消息，从redis读取并更新，目前过期时间是7d
     *
     * @param messageContext
     */
    public void saveCommitMessage(MessageContext messageContext) {
        Long messageId = messageContext.getMessageId();
        String messageContentKey = getMessageContextKey(messageContext.getMessageId());
        String messageExtraKey = getMessageExtraKey(messageContext.getMessageId());

        //从DB尝试读出 未提交的 消息，此时DB的消息
        MessageBoxDO messageBoxDO = messageBoxDAO.selectByMessageIdAndOwner(messageId, messageContext.getUid());
        if (messageBoxDO == null || Objects.equals(messageBoxDO.getCommitStatus(), MSG_STATUS_COMMIT)) {
            return;
        }

        // 基本消息字段，所有回答文本都append的方式追加进去，一定会有
        String content = transformersRedisClient.getStr(messageContentKey);
        if (content == null) {
            log.error("msg not commit and can not find from redis");
            return;
        }
        messageBoxDO.setContent(content);

        // 扩展消息字段，仅卡片消息才可能存在，其它消息没有
        String extra = transformersRedisClient.getStr(messageExtraKey);
        messageBoxDO.setExtraContent(extra);

        //状态改为提交
        messageBoxDO.setCommitStatus(MSG_STATUS_COMMIT);

        if (messageBoxDAO.updateMessageByOwnerAndMessageId(messageBoxDO)) {
            transformersRedisClient.delete(messageContentKey);
            transformersRedisClient.delete(messageExtraKey);
        }
    }

    /**
     * 检查端上传来的session，若过期直接替换
     */
    public void checkSession(String sessionId, MessageContext messageContext) {
        String sessionCacheKey = getSessionCacheKey(sessionId);

        if (transformersRedisClient.exist(sessionCacheKey)) {
            messageContext.setSessionId(sessionId);
        } else {
            String newSessionId = createSessionIdAndRecord();
            messageContext.setSessionId(newSessionId);
        }
    }

    public String createSessionIdAndRecord() {
        String sessionId = OUIDGenerator.generate("s");
        transformersRedisClient.setStr(getSessionCacheKey(sessionId), sessionId,
                Duration.ofMinutes(globalApolloConfig.getSessionMaxExpireMinute()));

        return sessionId;
    }

    /**
     * 从缓存中删除sessionId
     */
    public Boolean removeSession(String sessionId) {
        String sessionCacheKey = getSessionCacheKey(sessionId);
        if (transformersRedisClient.exist(sessionCacheKey)) {
            return transformersRedisClient.delete(sessionCacheKey);
        }
        return true;
    }


    /**
     * 用户输入消息
     */
    public MessageVO getUserMessageVO(List<TextCheckReason> checkReasons, String question, MessageContext messageContext) {
        MessageVO messageVO = new MessageVO();
        String questId = StringUtils.defaultIfBlank(messageContext.getQuestId(), OUIDGenerator.generate("q"));
        messageContext.setQuestId(questId);
        messageVO.setQuestId(questId);
        messageVO.setMessageId(MsgIdGenerator.genId());
        messageVO.setSessionId(messageContext.getSessionId());
        messageVO.setTime(DateUtils.getCurTimestamp());
        messageVO.setType(MSG_TYPE_TEXT);
        messageVO.setRole(MSG_SENDER_USER);
        messageVO.setEnd(false);
        messageVO.setTopicId(messageContext.getTopicId());
        if (!CollectionUtils.isEmpty(checkReasons)) {
            messageVO.setRiskResult(true);
            messageVO.setContent("***********");
        } else {
            messageVO.setRiskResult(false);
            messageVO.setContent(question);
        }

        return messageVO;
    }

    public MessageVO getUserImageMessageVO(List<String> checkReasons, List<String> imageUrlList, MessageContext messageContext) {
        MessageVO messageVO = new MessageVO();
        String questId = StringUtils.defaultIfBlank(messageContext.getQuestId(), OUIDGenerator.generate("q"));
        messageContext.setQuestId(questId);
        messageVO.setQuestId(questId);
        messageVO.setMessageId(MsgIdGenerator.genId());
        messageVO.setSessionId(messageContext.getSessionId());
        messageVO.setTime(DateUtils.getCurTimestamp());
        messageVO.setType(MessageTypeEnum.IMAGE.getCode());
        messageVO.setRole(MSG_SENDER_USER);
        messageVO.setEnd(false);
        messageVO.setTopicId(messageContext.getTopicId());
        if (CollectionUtils.isNotEmpty(checkReasons)) {
            messageVO.setRiskResult(true);
            // 默认图
            messageVO.setContent(JsonUtil.toJson(Lists.newArrayList(globalApolloConfig.getChatDefaultImageUrl())));
        } else {
            messageVO.setRiskResult(false);
            messageVO.setContent(JsonUtil.toJson(imageUrlList));
        }
        return messageVO;
    }


    /**
     * 敏感消息
     */
    public MessageVO getSensitiveMessageVO(MessageContext messageContext) {
        MessageVO messageVO = new MessageVO();
        messageVO.setQuestId(messageContext.getQuestId());
        messageVO.setMessageId(MsgIdGenerator.genId());
        messageVO.setSessionId(messageContext.getSessionId());
        messageVO.setTime(DateUtils.getCurTimestamp());
        messageVO.setType(MSG_TYPE_TEXT);
        messageVO.setRole(MSG_SENDER_SYSTEM);
        messageVO.setEnd(true);
        messageVO.setContent(globalApolloConfig.getAntispamPrompt());
        messageVO.setTopicId(messageContext.getTopicId());
        return messageVO;

    }

    /**
     * 分段消息
     *
     * @param messageContext
     * @return
     */
    public MessageVO getSegmentMessageVO(MessageContext messageContext, ChatContext chatContext) {
        MessageVO messageVO = new MessageVO();
        String questId = StringUtils.defaultIfBlank(messageContext.getQuestId(), OUIDGenerator.generate("q"));
        messageContext.setQuestId(questId);
        messageVO.setQuestId(questId);
        messageVO.setMessageId(MsgIdGenerator.genId());
        messageVO.setSessionId(messageContext.getSessionId());
        messageVO.setTime(DateUtils.getCurTimestamp());
        messageVO.setType(MessageTypeEnum.SEGMENT.getCode());
        messageVO.setRole(MSG_SENDER_SYSTEM);
        messageVO.setEnd(false);
        messageVO.setContent("");
        messageVO.setTopicId(messageContext.getTopicId());
        // 商品 id 不为空，则为商品分段消息，否则只有时间分段，无商品信息
        if (StringUtils.isNotBlank(chatContext.getProductId())) {
            ProductListRequest summaryReq = summaryProxy.generateSummaryRequest(chatContext, MSG_CARD_PAGE_ID_TITLE, Collections.singletonList(chatContext.getProductId()));
            List<PageOldProductInfoDTO> summaryRes = summaryProxy.getProductList(summaryReq);
            if (CollectionUtils.isNotEmpty(summaryRes)
                && CollectionUtils.isNotEmpty(summaryRes.get(0).getProductInfoBOS())) {
                ProductInfoOldBO productInfoOldBO = summaryRes.get(0).getProductInfoBOS().get(0);
                if (productInfoOldBO != null) {
                    String shortTitle = productInfoOldBO.getShortTitle();
                    if (StringUtils.isNotBlank(shortTitle)) {
                        // 消息格式 咨询商品：【商品标题】，其中标题选取逻辑：商品短标题，不拼接买点标签
                        messageVO.setContent(String.format(globalApolloConfig.getGoodsSegmentText(), shortTitle));
                    } else {
                        MonitorUtil.counterOnce("saveSegmentMessage", "short_title_empty", "-1", JsonUtil.toJson(messageContext));
                    }
                }
            }
        }
        return messageVO;
    }

    /**
     * ai 生成的普 通文本消息，消息的type是文本类型
     *
     * @param end            是否结束
     * @param content
     * @param messageContext
     * @return
     */
    public MessageVO getAiTextMessageVO(boolean end, String content, MessageContext messageContext) {
        MessageVO messageVO = new MessageVO();
        messageVO.setQuestId(messageContext.getQuestId());
        messageVO.setMessageId(messageContext.getMessageId());
        messageVO.setSessionId(messageContext.getSessionId());
        messageVO.setEnd(end);
        messageVO.setContent(content);
        messageVO.setTime(DateUtils.getCurTimestamp());
        messageVO.setType(MSG_TYPE_TEXT);
        messageVO.setRole(MSG_SENDER_ASSISTANT);
        return messageVO;
    }

    public AiChatMessageVO buildTextAiChatMessageVO(boolean end, String content, String reasoningContent,
        String qid, MessageContext messageContext) {
        AiChatMessageVO messageVO = new AiChatMessageVO();
        messageVO.setQuestId(messageContext.getQuestId());
        messageVO.setMessageId(messageContext.getMessageId());
        messageVO.setSessionId(messageContext.getSessionId());
        messageVO.setEnd(end);
        messageVO.setContent(content);
        messageVO.setReasoningContent(reasoningContent);
        messageVO.setTime(DateUtils.getCurTimestamp());
        messageVO.setType(MessageTypeEnum.TEXT.getCode());
        messageVO.setRole(MSG_SENDER_ASSISTANT);
        messageVO.setQid(qid);
        return messageVO;
    }

    public MessageVO buildAiProductCardMessageVO(boolean end, AiChatAppendContents appendContents, MessageContext messageContext,
        ChatQuestionReq chatQuestionReq) {
        MessageVO messageVO = new MessageVO();
        messageVO.setQuestId(messageContext.getQuestId());
        messageVO.setMessageId(messageContext.getMessageId());
        messageVO.setSessionId(messageContext.getSessionId());
        messageVO.setEnd(end);
        messageVO.setTime(DateUtils.getCurTimestamp());
        messageVO.setType(MessageTypeEnum.TEXT.getCode());
        messageVO.setRole(MSG_SENDER_ASSISTANT);
        // 检查追加内容 appendContents 里的 content，type=product 的时候，content 为商品 id 列表，彼此之间用逗号分隔
        if (StringUtils.isNotEmpty(appendContents.getContent())) {
            ChatContext chatContext = ChatContext.of(chatQuestionReq);
            List<String> productIds = JsonUtil.toObjList(appendContents.getContent(), String.class);
            // invoke summary to construct product card
            List<ProductInfoVO> productInfoVOList = summaryProxy.getProductInfoCardByProductIds(chatContext, MSG_CARD_PAGE_ID_RECOMMENDATION, productIds);
            messageVO.setRecommendProducts(productInfoVOList);
        }
        messageVO.setPurpose(appendContents.getPurpose());
        return messageVO;
    }

    public AiChatMessageVO buildAiProductCardMessageVO(boolean end, AiChatAppendContents appendContents, MessageContext messageContext,
        AiChatCompletionReq completionReq) {
        AiChatMessageVO messageVO = new AiChatMessageVO();
        messageVO.setQuestId(messageContext.getQuestId());
        messageVO.setMessageId(messageContext.getMessageId());
        messageVO.setSessionId(messageContext.getSessionId());
        messageVO.setEnd(end);
        messageVO.setTime(DateUtils.getCurTimestamp());
        messageVO.setType(MessageTypeEnum.GOODS_CARD.getCode());
        messageVO.setRole(MSG_SENDER_ASSISTANT);
        messageVO.setAiPurpose(appendContents.getPurpose());
        // 无商卡直接返回
        if (CollectionUtils.isEmpty(appendContents.getItemList())) {
            messageVO.setRecommendProducts(Collections.emptyList());
            return messageVO;
        }
        // 解析并补充商卡信息
        ChatContext chatContext = ChatContext.of(completionReq);
        List<String> productIds = appendContents.getItemList().stream()
            .map(ProductItem::getPMongoId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        Map<String, ProductItem> productItemMap = appendContents.getItemList().stream()
            .filter(item -> item != null && item.getPMongoId() != null)
            .collect(Collectors.toMap(ProductItem::getPMongoId, Function.identity(), (key1, key2) -> key2));

        // invoke summary to construct product card
        List<ProductInfoVO> productInfoVOList = summaryProxy.getProductInfoCardByProductIds(chatContext, MSG_CARD_PAGE_ID_SEARCH_RESULT_PAGE, productIds);
        if (CollectionUtils.isNotEmpty(productInfoVOList)) {
            productInfoVOList.forEach(productInfoVO -> {
                ProductItem productItem = productItemMap.get(productInfoVO.getId());
                if (productItem != null) {
                    // 设置算法下发的 scm 字段信息
                    productInfoVO.setScm(productItem.getScm());
                    // 支持缺货加购
                    productInfoVO.setStockOutReservedNew(true);
                }
            });
            int endIndex = Math.min(productInfoVOList.size(), globalApolloConfig.getDeepSeekConfig().getProductTotalNumber());
            // 返回指定个数的商品
            messageVO.setRecommendProducts(productInfoVOList.subList(0, endIndex));
        }
        return messageVO;
    }

    /**
     * ai 生成的 最后附带超链接的消息，这种消息的type也是文本类型
     * 注意，当前SSE的模式下，一个回答是N条MessageVO消息持续返回，前面的N-1条都是文本消息，而最后一条是超链接消息。
     * 此时返回VO对象的content是一段HTML的片段，包含一个超链接按钮，点击后会触发跳转
     * for 老算法接口契约
     *
     * @param end
     * @param appendContents
     * @param messageContext
     * @return
     */
    public MessageVO getAiUrlMessageVO(boolean end, AppendContents appendContents, MessageContext messageContext) {
        MessageVO messageVO = new MessageVO();
        messageVO.setQuestId(messageContext.getQuestId());
        messageVO.setMessageId(messageContext.getMessageId());
        messageVO.setSessionId(messageContext.getSessionId());
        messageVO.setEnd(end);
        messageVO.setTime(DateUtils.getCurTimestamp());
        messageVO.setType(MSG_TYPE_TEXT);
        messageVO.setRole(MSG_SENDER_ASSISTANT);

        // appendContents的格式如下：
        // appendContents[i].type	string
        //      追加内容类型：
        //          text：文本
        //          url：链接
        //          product：商品
        // appendContents[i].content	string	追加内容，若追加类型是商品，则content为商品id列表
        // appendContents[i].displayText	string	前端展示内容
        // appendContents[i].purpose	string	用途
        // 详细参考： https://cfl.corp.100.me/pages/viewpage.action?pageId=191871279
        // 这里只处理URL类型的消息，目前有两种跳转：
        //  1、 客服咨询，需要跳转链接，content不为空
        //  2、 用户反馈，这种情况不需要跳转链接，content为空
        if (!StringUtils.isEmpty(appendContents.getDisplayText())) {
            StringBuilder innerContent = new StringBuilder();
            if (MSG_APPEND_CONTENT_PURPOSE_ARRIVAL_ALERT.equals(appendContents.getPurpose())) {
                // 到货提醒
                innerContent.append("<button class=\"btn-link btn-stock-notice\" ");
            } else if (MSG_APPEND_CONTENT_PURPOSE_CUSTOM_SERVICE.equals(appendContents.getPurpose())) {
                // 客服
                innerContent.append("<button class=\"btn-link btn-customer-service\" ");
            } else {
                innerContent.append("<button class=\"btn-link\" ");
            }
            if (StringUtils.isNotEmpty(appendContents.getContent())) {
                innerContent.append("data-src=\"").append(appendContents.getContent()).append("\" ");
            }
            innerContent.append(">");
            innerContent.append(appendContents.getDisplayText());
            innerContent.append("</button>");

            messageVO.setContent(innerContent.toString());
        }

        messageVO.setPurpose(appendContents.getPurpose());

        return messageVO;
    }

    public MessageVO buildAiQuestionMessageVO(boolean end, AiChatAppendContents appendContents, MessageContext messageContext) {
        MessageVO messageVO = new MessageVO();
        messageVO.setQuestId(messageContext.getQuestId());
        messageVO.setMessageId(messageContext.getMessageId());
        messageVO.setSessionId(messageContext.getSessionId());
        messageVO.setEnd(end);
        messageVO.setTime(DateUtils.getCurTimestamp());
        messageVO.setType(MessageTypeEnum.TEXT.getCode());
        messageVO.setRole(MSG_SENDER_ASSISTANT);
        if (StringUtils.isNotEmpty(appendContents.getContent())) {
            messageVO.setQuickQuestionList(JsonUtil.toObjList(appendContents.getContent(), String.class));
        }
        messageVO.setPurpose(appendContents.getPurpose());
        return messageVO;
    }

    /**
     * 与方法 getAiUrlMessageVO()逻辑一致，但入参类型从 AppendContents 变成 AiChatAppendContents
     * for 新算法接口
     *
     * @param end
     * @param appendContents
     * @param messageContext
     * @return
     */
    public MessageVO buildAiUrlMessageVO(boolean end, AiChatAppendContents appendContents, MessageContext messageContext) {
        MessageVO messageVO = new MessageVO();
        messageVO.setQuestId(messageContext.getQuestId());
        messageVO.setMessageId(messageContext.getMessageId());
        messageVO.setSessionId(messageContext.getSessionId());
        messageVO.setEnd(end);
        messageVO.setTime(DateUtils.getCurTimestamp());
        messageVO.setType(MSG_TYPE_TEXT);
        messageVO.setRole(MSG_SENDER_ASSISTANT);
        if (!StringUtils.isEmpty(appendContents.getDisplayText())) {
            StringBuilder innerContent = new StringBuilder();
            if (MSG_APPEND_CONTENT_PURPOSE_ARRIVAL_ALERT.equals(appendContents.getPurpose())) {
                // 到货提醒
                innerContent.append("<button class=\"btn-link btn-stock-notice\" ");
            } else if (MSG_APPEND_CONTENT_PURPOSE_CUSTOM_SERVICE.equals(appendContents.getPurpose())) {
                // 客服
                innerContent.append("<button class=\"btn-link btn-customer-service\" ");
            } else {
                innerContent.append("<button class=\"btn-link\" ");
            }
            if (StringUtils.isNotEmpty(appendContents.getContent())) {
                innerContent.append("data-src=\"").append(appendContents.getContent()).append("\" ");
            }
            innerContent.append(">");
            innerContent.append(appendContents.getDisplayText());
            innerContent.append("</button>");

            messageVO.setContent(innerContent.toString());
        }

        messageVO.setPurpose(appendContents.getPurpose());

        return messageVO;
    }

    /**
     * ai 生成的 产品卡片消息，这种消息的type是商卡类型
     * 注意，当前SSE的模式下，一个回答是N条MessageVO消息持续返回，前面的N-1条都是文本消息，而最后一条是商卡消息
     * 此时返回VO对象的content是空，商卡列表放在recommendProducts属性，
     * 为排障，extraContent属性里放着原始的商品列表，逗号分隔
     *
     * @param end
     * @param appendContents
     * @param messageContext
     * @return
     */
    public MessageVO getAiProductCardMessageVO(boolean end, AppendContents appendContents, MessageContext messageContext,
                                               ChatQuestionReq chatQuestionReq) {
        MessageVO messageVO = new MessageVO();
        messageVO.setQuestId(messageContext.getQuestId());
        messageVO.setMessageId(messageContext.getMessageId());
        messageVO.setSessionId(messageContext.getSessionId());
        messageVO.setEnd(end);
        messageVO.setTime(DateUtils.getCurTimestamp());
        messageVO.setType(MSG_TYPE_TEXT);
        messageVO.setRole(MSG_SENDER_ASSISTANT);

        // appendContents的格式如下：
        // appendContents[i].type	string
        //      追加内容类型：
        //          text：文本
        //          url：链接
        //          product：商品
        // appendContents[i].content	string	追加内容，若追加类型是商品，则content为商品id列表
        // appendContents[i].displayText	string	前端展示内容
        // appendContents[i].purpose	string	用途
        // 详细参考： https://cfl.corp.100.me/pages/viewpage.action?pageId=191871279
        // 这里只处理商品卡片类型的消息，根据算法契约 https://cfl.corp.100.me/pages/viewpage.action?pageId=191871279
        // 检查追加内容appendContents里的content，type=product的时候，content为商品id列表，彼此之间用逗号分隔
        if (StringUtils.isNotEmpty(appendContents.getContent())) {
            messageVO.setExtraContent(appendContents.getContent());

            ChatContext chatContext = ChatContext.of(chatQuestionReq);
            List<String> productIds = JsonUtil.toObjList(appendContents.getContent(), String.class);
            // invoke summary to construct product card
            List<ProductInfoVO> productInfoVOList = summaryProxy.getProductInfoCardByProductIds(chatContext, MSG_CARD_PAGE_ID_RECOMMENDATION, productIds);

            messageVO.setRecommendProducts(productInfoVOList);
        }
        messageVO.setPurpose(appendContents.getPurpose());

        return messageVO;
    }


    /**
     * 生成发起流式回答的请求，会生成messageId,端上停止回答的时候也会用这个messageId来停止
     *
     * @param chatQuestionReq c端入参
     * @param messageContext  消息上下文
     * @param isNew  来源是否为新接口
     * @return
     */
    public ProductAssistRequestVo getProductAssistRequestVo(ChatQuestionReq chatQuestionReq, MessageContext messageContext, boolean isNew) {
        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        Long messageId = MsgIdGenerator.genId();
        messageContext.setMessageId(messageId);
        productAssistRequestVo.setMessageId(messageId + "");
        productAssistRequestVo.setSessionId(messageContext.getSessionId());
        productAssistRequestVo.setQid(messageContext.getQuestId());
        productAssistRequestVo.setTopicId(messageContext.getTopicId());
        productAssistRequestVo.setToken(getToken(chatQuestionReq, isNew));
        productAssistRequestVo.setQuery(chatQuestionReq.getQuestion());
        productAssistRequestVo.setUser(chatQuestionReq.getUid());
        productAssistRequestVo.setTimestamp(System.currentTimeMillis());
        productAssistRequestVo.setStream(true);
        productAssistRequestVo.setScene(processScene(chatQuestionReq.getFrom(), chatQuestionReq.getScene()));

        productAssistRequestVo.setStationId(chatQuestionReq.getStationId());
        productAssistRequestVo.setCityNumber(chatQuestionReq.getCityNumber());
        productAssistRequestVo.setApiVersion(chatQuestionReq.getApiVersion());
        productAssistRequestVo.setAppClientId(chatQuestionReq.getAppClientId());
        productAssistRequestVo.setNativeVersion(chatQuestionReq.getNativeVersion());
        productAssistRequestVo.setDeviceId(chatQuestionReq.getDeviceId());
        productAssistRequestVo.setH5Source(chatQuestionReq.getH5Source());
        productAssistRequestVo.setLatitude(chatQuestionReq.getLatitude());
        productAssistRequestVo.setLongitude(chatQuestionReq.getLongitude());
        productAssistRequestVo.setCityName(chatQuestionReq.getCityName());

        // 新增参数
        productAssistRequestVo.setQuestionSource(chatQuestionReq.getQuestionSource());
        productAssistRequestVo.setPageType(chatQuestionReq.getFrom());
        productAssistRequestVo.setProductMongoId(chatQuestionReq.getProductId());
        productAssistRequestVo.setFrontendCategoryId(chatQuestionReq.getFrontCategoryId());
        productAssistRequestVo.setFrontendCategoryName(chatQuestionReq.getFrontCategoryName());
        // 其实本次的productId，还有新增的类目id/名称，都无需传递，算法服务端会根据sessionId自行获取
        // 搜索词
        productAssistRequestVo.setSearchQuery(chatQuestionReq.getSearchKeyword());
        // 扩展参数处理
        if (MapUtils.isNotEmpty(chatQuestionReq.getExtraParamMap())) {
            Map<String, String> extraParamMap = chatQuestionReq.getExtraParamMap();
            // 评价列表页参数
            productAssistRequestVo.setTraitKey(extraParamMap.get(TRAIT_KEY));
            productAssistRequestVo.setTraitType(extraParamMap.get(TRAIT_TYPE));
            // 品质之爱落地页
            productAssistRequestVo.setTabPath(extraParamMap.get(LOVE_QUALITY_TAB_NAME));
            productAssistRequestVo.setHighlightText(extraParamMap.get(LOVE_QUALITY_TEXT));
        }

        // 只有新接口，才需要做多模态参数处理
        if (isNew) {
            processMultimodalParam(productAssistRequestVo, chatQuestionReq);
        }
        return productAssistRequestVo;
    }

    /**
     * 该方法主要是为了兼容算法的新老接口传参逻辑
     *
     * @param from  from
     * @param scene scene
     * @return scene
     */
    public String processScene(String from, String scene) {
        // 前端传了 scene 时以前端传递的 scene 为准
        if (StringUtils.isNotBlank(scene)) {
            return scene;
        }
        // 前端没有传 scene 的场景
        // 在 dxd_new 场景下，scene 没传默认取 home_page
        if (REQ_SOURCE_DXD_NEW.equals(from)) {
            return COMPLETIONS_HOME_PAGE_SCENE;
        }
        // 在 dxd_new 以外的场景，如果没有传 scene，直接取 from 值设置给 scene
        return from;
    }

    /**
     * 叮小咚新形态或者有图片，需要做多模态模型参数处理
     * @param chatQuestionReq req
     * @return true:需要 false:不需要
     */
    public boolean isProcessMultimodalParam(ChatQuestionReq chatQuestionReq) {
        return REQ_SOURCE_DXD_NEW.equals(chatQuestionReq.getFrom())
            || CollectionUtils.isNotEmpty(chatQuestionReq.getImageUrls());
    }

    public void processMultimodalParam(ProductAssistRequestVo productAssistRequestVo, ChatQuestionReq chatQuestionReq) {
        if (CollectionUtils.isEmpty(chatQuestionReq.getImageUrls())) {
            // 无图片消息，创建纯文本请求参数
            List<LLMChatMessage> messagesList = new ArrayList<>();
            LLMChatMessage llmChatMessage = new LLMChatMessage();
            llmChatMessage.setRole("user");
            llmChatMessage.setContent(chatQuestionReq.getQuestion());
            messagesList.add(llmChatMessage);
            productAssistRequestVo.setMessages(messagesList);
        } else {
            // 有图片，创建多模态请求参数
            List<LLMChatMessage> messagesList = new ArrayList<>();
            LLMChatMessage llmChatMessage = new LLMChatMessage();
            llmChatMessage.setRole("user");
            // 多模态 content 内容
            List<MultimodalContentVO> contentVOList = new ArrayList<>();
            // 文本
            MultimodalContentVO textContentVO = new MultimodalContentVO();
            textContentVO.setType("text");
            textContentVO.setText(chatQuestionReq.getQuestion());
            contentVOList.add(textContentVO);
            // 图片
            MultimodalContentVO imageContentVO = new MultimodalContentVO();
            imageContentVO.setType("image_url");
            MultimodalContentVO.ImageData imageData = new MultimodalContentVO.ImageData();
            imageData.setUrl(chatQuestionReq.getImageUrls().get(0));
            imageContentVO.setImageUrl(imageData);
            contentVOList.add(imageContentVO);

            llmChatMessage.setContent(contentVOList);
            messagesList.add(llmChatMessage);
            productAssistRequestVo.setMessages(messagesList);
        }
    }

    public ProductAssistRequestVo buildProductAssistRequestVo(AiChatCompletionReq chatQuestionReq, MessageContext messageContext) {
        ProductAssistRequestVo productAssistRequestVo = new ProductAssistRequestVo();
        Long messageId = MsgIdGenerator.genId();
        messageContext.setMessageId(messageId);
        productAssistRequestVo.setMessageId(messageId + "");
        productAssistRequestVo.setSessionId(messageContext.getSessionId());
        productAssistRequestVo.setQid(chatQuestionReq.getQid());
        AiChatNlpParam aiChatNlpParam = globalApolloConfig.getAiChatNlpParam(chatQuestionReq.getScene());
        if (aiChatNlpParam != null) {
            productAssistRequestVo.setToken(aiChatNlpParam.getToken());
            productAssistRequestVo.setPageType(aiChatNlpParam.getPageType());
        }
        productAssistRequestVo.setSearchQuery(chatQuestionReq.getQuestion());
        productAssistRequestVo.setUser(chatQuestionReq.getUid());
        productAssistRequestVo.setStream(true);
        productAssistRequestVo.setRefreshAnswer(chatQuestionReq.getRefreshAnswer());
        // 通用常规参数
        productAssistRequestVo.setStationId(chatQuestionReq.getStationId());
        productAssistRequestVo.setCityNumber(chatQuestionReq.getCityNumber());
        productAssistRequestVo.setApiVersion(chatQuestionReq.getApiVersion());
        productAssistRequestVo.setAppClientId(chatQuestionReq.getAppClientId());
        productAssistRequestVo.setNativeVersion(chatQuestionReq.getNativeVersion());
        productAssistRequestVo.setDeviceId(chatQuestionReq.getDeviceId());
        productAssistRequestVo.setH5Source(chatQuestionReq.getH5Source());
        productAssistRequestVo.setLatitude(chatQuestionReq.getLatitude());
        productAssistRequestVo.setLongitude(chatQuestionReq.getLongitude());
        // 深度思考开关
        productAssistRequestVo.setModelFrontType(deepSeekHandler.queryReasonSwitch(chatQuestionReq.getUid()));
        return productAssistRequestVo;
    }

    private String getToken(ChatQuestionReq chatQuestionReq, boolean isNew) {
        Map<String, String> nlpTokenMap = globalApolloConfig.getNlpTokenMap();
        String token = "";

        // 新接口，直接取新 token
        if (isNew) {
            token = nlpTokenMap.get(REQ_SOURCE_DXD_NEW);
        } else {
            // 根据具体 from 值选取 token
            token = nlpTokenMap.get(chatQuestionReq.getFrom());
        }

        if (StringUtils.isBlank(token)) {
            log.warn("MessageBoxHandler.getToken is blank, req:{}", JsonUtil.toJson(chatQuestionReq));
            MonitorUtil.counterOnce("MessageBoxHandler_getToken", "is_blank", "0",
                JsonUtil.toJson(chatQuestionReq));
            // 默认详情页的 token
            return globalApolloConfig.getToken();
        }
        return token;
    }

    public MessageBoxVO history(ChatHistoryReq chatHistoryReq) {

        LocalDateTime localDateTime = LocalDateTime.now().minusDays(globalApolloConfig.getHistoryDateLimit());  //默认至多只查询到7天前
        // 查询结果是：时间要晚于7天前的当前时刻，但消息ID要小于传入的消息ID（不传默认MAX_LONG)，然后按消息ID从大到小排序
        List<MessageBoxDO> messagesPerPage = messageBoxDAO.selectMessage(chatHistoryReq, chatHistoryReq.getLimit() + 1, localDateTime);  //多查一条

        MessageBoxVO messageBoxVO = new MessageBoxVO();
        messageBoxVO.setUid(chatHistoryReq.getUid());

        if (CollectionUtils.isEmpty(messagesPerPage)) {
            messageBoxVO.setHasMore(false);
            messageBoxVO.setMessageList(Collections.emptyList());
            messageBoxVO.setLastMessageId(null);
        } else {
            if (messagesPerPage.size() > chatHistoryReq.getLimit()) {//有下一页
                // 查的时候多查了一条，而且顺序是MessageID从大到小，所以多的那条消息的messageID是当次结果中最小的那条，也即最后一条
                messagesPerPage.remove(messagesPerPage.size() - 1);//去掉最后一条
                messageBoxVO.setHasMore(true);
            } else {
                messageBoxVO.setHasMore(false);
            }

             List<MessageVO> messageVOs = messageConvert.messageDO2VO(messagesPerPage)
                    .stream()
                     .filter(this::filterContentBlankByText)
                        .peek(it -> {
                        // 历史消息end全部设置为true,前端end为false时打字会有动效
                        it.setEnd(true);
                        // 将镜像保存的历史卡片消息JSON格式，转换为对象列表
                        it.setRecommendProducts( it.getExtraContent() == null ? null : JsonUtil.toObjList(it.getExtraContent(), ProductInfoVO.class));
                    })
                    .sorted(Comparator.comparing(MessageVO::getMessageId))   // 返回前将顺序调整为MessageID从小到大，但其实没这个必要
                    .collect(Collectors.toList());
            messageBoxVO.setMessageList(messageVOs);

            // 同时返回本次消息集中的 “最小的” MessageID（本次最小的MessageId，对下一次来说就是请求的messageID起点）
            // 注意，这里还是从最原始的结果集中取，而不是从排序后的结果集中取，所以是取原始结果集中最后一条记录的messageID
            messageBoxVO.setLastMessageId(messagesPerPage.get(messagesPerPage.size() - 1).getMessageId());
        }
        return messageBoxVO;
    }

    private boolean filterContentBlankByText(MessageVO messageVO) {
        return !(MessageTypeEnum.TEXT.getCode().equals(messageVO.getType())
            && StringUtils.isBlank(messageVO.getContent()));
    }

    /**
     * 从数据库查询7天内未提交的消息，从redis获取未提交的消息体，重新保存到数据库，并删除redis
     *
     * @return
     */
    public Integer syncUnCommitMessage() {
        LocalDateTime localDateTime = LocalDateTime.now().plusDays(-7);
        Integer res = 0;
        List<MessageBoxDO> messageBoxDOS;
        messageBoxDOS = messageBoxDAO.selectUnCommitMessage( 500, localDateTime);
        if (CollectionUtils.isNotEmpty(messageBoxDOS)) {
            for (MessageBoxDO messageBoxDO : messageBoxDOS) {
                String messageKey = getMessageContextKey(messageBoxDO.getMessageId());
                if (transformersRedisClient.exist(messageKey)) {
                    messageBoxDO.setContent(transformersRedisClient.getStr(messageKey));
                    messageBoxDO.setCommitStatus(MSG_STATUS_COMMIT);
                    boolean update = messageBoxDAO.updateMessageByOwnerAndMessageId(messageBoxDO);
                    if (update) {//更新成功删除redis
                        transformersRedisClient.delete(messageKey);
                        res++;
                    }
                } else {//写入的时候先写redis，如果查不到，状态改2   表示此状态的消息不可追溯（先写redis再写db  出现此问题可能是redis超时、闪断）
                    messageBoxDO.setCommitStatus(MSG_STATUS_UNKNOWN);
                    res++;
                    messageBoxDAO.updateMessageByOwnerAndMessageId(messageBoxDO);
                    MonitorUtil.counterOnce("droppedMessage", messageBoxDO.getMessageId() + "");
                }
            }
        }
        return res;
    }

    public void createSessionSegmentRecord(String sessionId) {
        transformersRedisClient.setStr(getSessionSegmentCacheKey(sessionId), sessionId,
            Duration.ofMinutes(globalApolloConfig.getSessionMaxExpireMinute()));
    }

    public boolean isSessionSegmentRecord(String sessionId) {
        return StringUtils.isNotBlank(transformersRedisClient.getStr(getSessionSegmentCacheKey(sessionId)));
    }

    public ProductRequestVo buildProductRequestVo(AiChatCompletionReq chatQuestionReq, MessageContext messageContext) {
        ProductRequestVo productRequestVo = new ProductRequestVo();
        Long messageId = MsgIdGenerator.genId();
        messageContext.setMessageId(messageId);
        productRequestVo.setMsgId("" + messageId);
        productRequestVo.setStationId(chatQuestionReq.getStationId());
        productRequestVo.setCityNumber(chatQuestionReq.getCityNumber());
        productRequestVo.setQuestion(chatQuestionReq.getQuestion());
        productRequestVo.setQid(chatQuestionReq.getQid());
        productRequestVo.setUid(chatQuestionReq.getUid());
        productRequestVo.setScene(DsSceneEnum.transferScene(chatQuestionReq.getScene()));
        productRequestVo.setTraceId(MDC.get("traceId"));
        // common param
        CommonParam commonParam = new CommonParam();
        commonParam.setAppClientId(chatQuestionReq.getAppClientId());
        commonParam.setApiVersion(chatQuestionReq.getApiVersion());
        productRequestVo.setCommonParam(commonParam);
        // 深度思考开关
        productRequestVo.setModelFrontType(deepSeekHandler.queryReasonSwitch(chatQuestionReq.getUid()));
        // 扩展参数处理
        if (StringUtils.isNotBlank(chatQuestionReq.getAiParam())) {
            AiParamVO aiParamVO = JsonUtil.toObject(chatQuestionReq.getAiParam(), AiParamVO.class);
            if (aiParamVO == null) {
                return productRequestVo;
            }
            if (aiParamVO.getIntentionInfo() != null) {
                productRequestVo.setKeyword(aiParamVO.getIntentionInfo().getIntentionWord());
            }
            if (aiParamVO.getSourceType() != null) {
                productRequestVo.setSourceType(aiParamVO.getSourceType());
            }
        }
        return productRequestVo;
    }

    /**
     * 构建每日小知识 message 内容
     *
     * @param dailyTipsVO daily tips
     * @param sessionId session id
     * @param messageId message id
     * @return message vo
     */
    public MessageVO buildDailyTipsMessageVO(DailyTipsVO dailyTipsVO, String sessionId, Long messageId) {
        MessageVO messageVO = new MessageVO();
        messageVO.setQuestId(OUIDGenerator.generate("q"));
        messageVO.setMessageId(messageId);
        messageVO.setSessionId(sessionId);
        messageVO.setEnd(true);
        messageVO.setContent(JsonUtil.toJson(dailyTipsVO));
        messageVO.setTime(DateUtils.getCurTimestamp());
        messageVO.setType(MessageTypeEnum.DAILY_TIPS.getCode());
        messageVO.setRole(MSG_SENDER_ASSISTANT);
        return messageVO;
    }

    /**
     * 保存每日小知识
     *
     * @param dailyTipsVO daily tips
     * @param sessionId session id
     * @param startReq req
     */
    public boolean saveDailyTips(DailyTipsVO dailyTipsVO, String sessionId, Long messageId, StartReq startReq) {
        MessageVO messageVO = buildDailyTipsMessageVO(dailyTipsVO, sessionId, messageId);
        MessageBoxDO messageBoxDO = messageConvert.messageVO2DO(messageVO);
        messageBoxDO.setOwner(startReq.getUid());
        messageBoxDO.setCommitStatus(MSG_STATUS_COMMIT);
        if (startReq.getProductId() != null) {
            messageBoxDO.setCorrelatePid(startReq.getProductId());
        }
        messageBoxDO.setCityCode(startReq.getCityNumber());
        messageBoxDO.setStationId(startReq.getStationId());
        return messageBoxDAO.insertMessage(messageBoxDO) > 0;
    }

    public List<MessageBoxDO> queryMessageBoxByMessageId(String userId, List<Long> messageIdList) {
        if (CollectionUtils.isEmpty(messageIdList)) {
            return Lists.newArrayList();
        }
        return messageBoxDAO.selectMessageById(userId, messageIdList);
    }

}
