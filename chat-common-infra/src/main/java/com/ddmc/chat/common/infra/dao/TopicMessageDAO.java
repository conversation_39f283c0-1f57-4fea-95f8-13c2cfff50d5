package com.ddmc.chat.common.infra.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ddmc.chat.common.mapper.DO.TopicMessageDO;
import com.ddmc.chat.common.mapper.TopicMessageMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class TopicMessageDAO {

    private final TopicMessageMapper topicMessageMapper;

    public boolean save(TopicMessageDO topicMessageDO) {
        return topicMessageMapper.insert(topicMessageDO) > 0;
    }

    public TopicMessageDO selectByUserId(String userId) {
        LambdaQueryWrapper<TopicMessageDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TopicMessageDO::getUserId, userId);
        wrapper.orderByDesc(TopicMessageDO::getCreateTime);
        wrapper.last("limit 1");
        return topicMessageMapper.selectOne(wrapper);
    }
} 