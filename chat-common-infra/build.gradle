plugins {
    id 'common-conventions'
}


dependencies {
    implementation project(':chat-common')
    implementation project(':chat-common-domain')
    api project(':chat-common-mapper')



    //算法ai，契约文档 https://cfl.corp.100.me/pages/viewpage.action?pageId=191871279
    api('com.ddmc:llm-biz-agent-client:1.0.0-RELEASE')
    //搜索算法，契约文档 https://cfl.corp.100.me/pages/viewpage.action?pageId=205613818
    api('com.ddmc:recipe-search-rec-client:1.0.36-RELEASE')


    //风控
    api('com.ddmc.antispam:antispam-client:1.5.7-RELEASE')


    // 文件服务（ddfs）
    api('com.ddmc:ddfs-client:1.6.5-RELEASE') {
        exclude group: 'com.ddmc.antispam', module: 'antispam-client'
    }

    api ('com.ddmc.abtest:abtest-sdk:1.3.14-RELEASE') {
        exclude group: 'org.mapstruct', module: 'mapstruct'
    }

    //比较版本
    implementation 'org.semver4j:semver4j:5.6.0'

    implementation 'org.springframework:spring-tx'
}