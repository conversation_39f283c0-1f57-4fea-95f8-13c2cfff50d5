# V2 �o�߀/�c

## 1. ��

V2�o��/��	V1�o����'G�e�����oX�ӄ/�{���o���,F�aG	���I	���V1����`'��

## 2. ����

### 2.1 tS��

```mermaid
graph TB
    A[�7�] --> B[NlpAiProxy.sendRequestV2]
    B --> C[��
�SSEA]
    C --> D[MessageV2PersistenceService]
    D --> E[Redis�X]
    B --> F[MessageBoxHandler]
    F --> G[MySQLpn�]
    
    subgraph "V2�oA"
        H[onOpen: �`M&�o] --> I[onEvent: �����X0Redis]
        I --> J[onClosed: �Redisb
v�X0pn�]
        J --> K[Redispn]
    end
    
    subgraph "b
:6"
        L[����] --> M[k�*Ф�o]
        M --> N[�Redisb
��]
        N --> O[�X0pn�]
    end
```

### 2.2 B��

```mermaid
graph LR
    A[ControllerB] --> B[ServiceB]
    B --> C[InfraB]
    C --> D[DAOB]
    D --> E[pn�B]
    
    subgraph "V28���"
        F[MessageBuilder<br/>�o��h]
        G[MessageV2PersistenceService<br/>V2E
�]
        H[MessageCompatibilityService<br/>|�'
�]
    end
```

## 3. 8�����

### 3.1 MessageBuilder (�o��h)

**L#**: ��
B�V2�o/���{�

```java
public class MessageBuilder {
    // 8ù�
    public MessageBuilder addTextPart(String text);           // ���,��
    public MessageBuilder appendToLastTextPart(String text);  // ��0 �,��
    public MessageBuilder addProductCardPart(List<ProductInfoVO> products, String purpose);
    public MessageBuilder addButtonPart(String text, String action, String url);
    public MessageBuilder addQuickQuestionsPart(List<String> questions);
    public MessageBuilder addReasoningPart(String reasoningContent);
    
    public MessageBuildResult buildWithParts(); // ���o���h
}
```

### 3.2 MessageV2PersistenceService (V2E
�)

**L#**: �V2�o(Redis-��X�b

```java
public class MessageV2PersistenceService {
    public void saveMessageParts(Long messageId, List<MessageContentPart> parts);
    public void appendMessagePart(Long messageId, MessageContentPart part);
    public void appendToLastPartOfType(Long messageId, String partType, String contentKey, String appendContent);
    public List<MessageContentPart> loadMessageParts(Long messageId);
    public MessageBuilder restoreMessageBuilder(Long messageId);
    public void deleteMessageParts(Long messageId);
}
```

### 3.3 MessageCompatibilityService (|�'
�)

**L#**: V1/V2�o�|�'��

```java
public interface MessageCompatibilityService {
    MessageVO getMessage(Long messageId, String uid);  // ��+V1/V2<
    boolean saveMessage(MessageBuilder.MessageBuildResult buildResult, ChatQuestionReq request);
    boolean shouldUseV2();
}
```

## 4. pn���

### 4.1 �o;h (message_box)

```sql
CREATE TABLE message_box (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    message_id BIGINT NOT NULL,
    owner VARCHAR(64) NOT NULL,
    session_id VARCHAR(64),
    quest_id VARCHAR(64),
    topic_id VARCHAR(64),
    sender VARCHAR(32),
    content TEXT,                    -- V1(V2:z
    extra_content TEXT,              -- V1(V2
(
    commit_status TINYINT DEFAULT 0, -- 0:*Ф 1:�Ф 2:*�
    version TINYINT DEFAULT 1,       -- 1:V1< 2:V2<
    station_id VARCHAR(32),
    city_code VARCHAR(32),
    correlate_pid VARCHAR(64),
    create_time DATETIME,
    update_time DATETIME,
    
    INDEX idx_message_owner (message_id, owner),
    INDEX idx_commit_status_version (commit_status, version, update_time)
);
```

### 4.2 �o��h (message_content_parts)

```sql
CREATE TABLE message_content_parts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    message_id BIGINT NOT NULL,
    owner VARCHAR(64) NOT NULL,
    part_type VARCHAR(32) NOT NULL,  -- text, product_card, button, reasoningI
    content JSON NOT NULL,           -- ��wS��
    metadata JSON,                   -- ��Cpn
    sort_order INT DEFAULT 0,       -- ����
    create_time DATETIME,
    
    INDEX idx_message_owner (message_id, owner),
    INDEX idx_part_type (part_type)
);
```

### 4.3 RedisX��

```
# V2�o��X
chat:msg:v2:parts:{messageId} � JSON([MessageContentPart...])
���: 7)

# V1�o��X�
�	
chat:msg:context:{messageId} � String(�,��)
chat:msg:extra:{messageId} � JSON(F��o)
```

## 5. �㾡

### 5.1 8�A��

#### sendRequestV2 - V2�oA

```java
/**
 * V2�oA��
 * @param chatQuestionReq (7�B
 * @param sseEmitter SSEޥ
 * @param messageContext �o
�
 * @return SseEmitter
 */
public SseEmitter sendRequestV2(ChatQuestionReq chatQuestionReq, 
                               SseEmitter sseEmitter, 
                               MessageContext messageContext);
```

**A�**:
1. **onOpen**: �pn�`M&�o + �Redis��h
2. **onEvent**: 㐗������X0Redis
3. **onClosed**: �Redisb
��v�X0pn�
4. **onFailure**: 8�_gL�X;�

### 5.2 �ob
��

#### syncUnCommitMessageV2 - V2�ob

```java
/**
 * b
*��V2�o
 * @return b
��op�
 */
public Integer syncUnCommitMessageV2();
```

### 5.3 ��o��

#### history - �o����

```java
/**
 * ��o���|�V1/V2	
 * @param chatHistoryReq ���B
 * @return MessageBoxVO
 */
public MessageBoxVO history(ChatHistoryReq chatHistoryReq);
```

## 6. �o��{��I

### 6.1 �,�� (text)

```json
{
  "type": "text",
  "content": {
    "text": "(7�o��",
    "format": "plain"
  }
}
```

### 6.2 F�aG�� (product_card)

```json
{
  "type": "product_card",
  "content": {
    "products": [
      {
        "id": "product123",
        "title": "F��",
        "price": "29.9",
        "image": "http://example.com/image.jpg"
      }
    ],
    "purpose": "recommendation"
  }
}
```

### 6.3 	��� (button)

```json
{
  "type": "button",
  "content": {
    "text": "�
��",
    "action": "custom_service",
    "url": "http://example.com/service",
    "style": "primary"
  }
}
```

### 6.4 ����� (quick_questions)

```json
{
  "type": "quick_questions",
  "content": {
    "questions": ["�U(", "�<", "	�"]
  }
}
```

### 6.5 ���� (reasoning)

```json
{
  "type": "reasoning",
  "content": {
    "reasoning": "AI����..."
  }
}
```

## 7. A

### 7.1 V2�o�A

```mermaid
sequenceDiagram
    participant C as �7�
    participant N as NlpAiProxy
    participant M as MessageBoxHandler
    participant P as MessageV2PersistenceService
    participant R as Redis
    participant DB as pn�
    
    C->>N: ��o�B
    N->>M: saveUnCommitMessageV2()
    M->>DB: �e`M&�o(version=2, commit_status=0)
    N->>P: saveMessageParts(messageId, [])
    P->>R: �z��h
    
    loop SSEA��
        N->>N: 㐗���
        alt ���(appendContents)
            N->>P: appendMessagePart(�t��)
        else A��(delta)
            N->>P: appendToLastPartOfType(�χ,)
        end
        P->>R: ����h
        N->>C: ����o
    end
    
    N->>M: saveCommitMessageV2()
    M->>P: loadMessageParts()
    P->>R: ����h
    M->>DB: ��commit_status=1
    M->>DB: �X��0content_partsh
    M->>P: deleteMessageParts()
    P->>R: 4�pn
```

### 7.2 �ob
A

```mermaid
sequenceDiagram
    participant T as ����
    participant M as MessageBoxHandler
    participant P as MessageV2PersistenceService
    participant R as Redis
    participant DB as pn�
    
    T->>M: syncUnCommitMessageV2()
    M->>DB: ��*Ф�V2�o
    
    loop M�*Ф�o
        M->>P: loadMessageParts(messageId)
        P->>R: ����h
        
        alt Redis-	pn
            M->>DB: ��commit_status=1
            M->>DB: �X��0content_partsh
            M->>P: deleteMessageParts()
            P->>R: Redispn
        else Redis-�pn
            M->>DB: ��commit_status=2(*�)
        end
    end
```

### 7.3 ��o��A

```mermaid
sequenceDiagram
    participant C as �7�
    participant M as MessageBoxHandler
    participant DB as pn�
    participant CP as MessageContentPartDAO
    
    C->>M: history()
    M->>DB: ��oh
    
    loop M��o
        alt V1�o(version=1)
            M->>M: �content�extra_content
        else V2�o(version=2)
            M->>CP: selectPartsByMessageId()
            CP->>DB: ����h
            M->>M: ��contentParts
        end
    end
    
    M->>C: ��� <�MessageVOh
```

## 8. ��ѧ

### 8.1 8Ve

- **Redisޥ1%**: M�0�pn�X��UJf
- **���1%**: �U���(\��,��
- **pn��e1%**: �YRedispn����
- **
��/**: ǚ���b
*��o

### 8.2 ѧ

```java
// s.ѧ�
MonitorUtil.counterOnce("sseEmitterV2", "timeout");           // SSE��
MonitorUtil.counterOnce("sseEmitterV2", "unknown_type");      // *���{�
MonitorUtil.counterOnce("droppedMessageV2", messageId);      // V2�o"1
MonitorUtil.counterOnce("v2_message_recovery", "success");   // V2�ob
�
```

## 9. '�

### 9.1 Redis

- **y��\**: (pipeline�Qܤ�
- **�Ve**: 7)��2b�X�2
- **�)X�**: JSON��/(�)

### 9.2 pn�

- **"��**: `(commit_status, version, update_time)` T"
- **y��e**: (batchInsertPartsy��e��
- **u��**: history��/u�}

## 10. �r�Mn

### 10.1 Mn�p

```yaml
# ApolloMn
chat:
  message:
    v2:
      enabled: true                    # /&/(V2<
      temp_expire_days: 7             # Redis4�pn�)p
      recovery_batch_size: 500        # b
��y!'
      recovery_interval: "0 */10 * * * ?" # b
����(10�)
```

### 10.2 �r��U

- [ ] pn�hӄ�� (��versionW��content_partsh)
- [ ] RedisƤMn
- [ ] ����Mn
- [ ] ѧJfMn
- [ ] p��Ve

## 11. |�'�

### 11.1 |�

-  V1�o�h|�� ��
-  ��oAPI��+H,
-  �7���G�

### 11.2 G��

1. **6�1**: �rV2�ؤ(V1<
2. **6�2**: p� /V2<�Mn�6	
3. **6�3**: h�b0V2<
4. **6�4**: V1�s��Y3*�	

---

V2�o��(���`'��@
Л��:�iU'��}�(7S�:*e�AI��:o`��Z���/�@